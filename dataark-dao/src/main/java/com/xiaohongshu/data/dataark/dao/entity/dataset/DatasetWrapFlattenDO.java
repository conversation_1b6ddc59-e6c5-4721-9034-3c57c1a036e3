package com.xiaohongshu.data.dataark.dao.entity.dataset;

import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetWrapFlattenDO {
    private Long id;

    private String name;

    private String mediaType;

    private String contentType;

    private Boolean isRaw;

    private Long maxVersionId;

    private String status;

    private String language;

    private SimpleUser owner;

    private Long tokens;

    private Long size;

    private Long records;

    private String tokenTaskStatus;

    private LocalDateTime createTime;
}
