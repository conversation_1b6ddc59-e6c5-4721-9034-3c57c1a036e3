package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "query_record", autoResultMap = true)
public class QueryRecordDO extends BaseEntity {

    /**
     * 数据集ID
     */
    private Long datasetId;

    /**
     * 数据集版本
     */
//    private Long datasetVersion;

    /**
     * 数据集版本id
     */
    private Long datasetVersionId;

    /**
     * 查询类型,preview或sample
     */
    private String queryType;

    /**
     * 采样任务id
     */
    private Long sampleId;

    /**
     * 采样任务生成路径
     */
    private String samplePath;

    /**
     * 查询ID,查询服务生成
     */
    private String queryId;

    /**
     * 查询条件
     */
    private String queryCondition;

    /**
     * 查询状态,待补充枚举值
     */
    private String state;

    /**
     * emr集群olap连接串
     */
    private String verbose;

    /**
     * 数据所在的可用区
     */
    private String region;

    /**
     * 提交sql
     */
    private String submitSql;

    /**
     * 执行sql
     */
    private String executedSql;

    /**
     * 执行引擎
     */
    private String engine;

    /**
     * 执行队列
     */
    private String queue;

    /**
     * 是否需要下载
     */
    private Boolean download;

    /**
     * 执行耗时（分钟）
     */
    private Integer cost;

    /**
     * 执行结果行数
     */
    private Long rows;

    /**
     * 执行进度
     */
    private Double progress;

    /**
     * 进度详情
     */
    private String progressInfo;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 创建人
     */
    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser createBy;

    /**
     * 更新人
     */
    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser updateBy;

}
