package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "dataset_info", autoResultMap = true)
public class DatasetInfoDO extends BaseEntity {

    private String name;

    private String code;

    private String description;

    private String mediaType;

    private String contentType;

    private Boolean isRaw;

    private Long maxVersionId;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser createBy;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser updateBy;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser owner;
}
