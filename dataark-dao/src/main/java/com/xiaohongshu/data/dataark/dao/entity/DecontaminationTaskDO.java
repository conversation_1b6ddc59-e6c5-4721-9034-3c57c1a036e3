package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/8/12 11:30
 */

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "decontamination_task", autoResultMap = true)
@Slf4j
public class DecontaminationTaskDO extends BaseEntity  {

    private Long datasetId;

    private String taskId;

    private Long datasetVersionId;

    private String datasetVersionName;

    private String filePath;

    private String status;

    private String errorMsg;

    protected LocalDateTime startTime;

    protected LocalDateTime endTime;

}
