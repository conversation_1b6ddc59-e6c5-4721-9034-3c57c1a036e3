package com.xiaohongshu.data.dataark.dao.entity.dataset;

import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email y<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/9
 */
@Data
public class DatasetVersionInfoDetailDO {

    private Long id;

    private Long datasetId;

    private Integer version;

    private String name;

    private String code;

    private String mediaType;

    private String contentType;

    private Boolean isRaw;

    private String status;

    private String language;

    private String tokenTaskStatus;

    private Long tokens;

    private Long size;

    private Long records;

    private SimpleUser owner;

    private LocalDateTime createTime;

}
