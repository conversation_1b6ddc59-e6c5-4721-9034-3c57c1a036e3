package com.xiaohongshu.data.dataark.dao.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.dataverse.infra.mybatis.plugins.UltimateBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Mapper
public interface BinidxTaskMapper extends UltimateBaseMapper<BinidxTaskDO> {
    IPage<BinidxTaskDO> pageList(Page<BinidxTaskDO> page, @Param("datasetVersionId") Long datasetVersionId);
}
