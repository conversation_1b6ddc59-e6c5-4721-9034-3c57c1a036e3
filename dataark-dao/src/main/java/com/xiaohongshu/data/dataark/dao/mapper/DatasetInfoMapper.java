package com.xiaohongshu.data.dataark.dao.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetWrapFlattenDO;
import com.xiaohongshu.dataverse.infra.mybatis.plugins.UltimateBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
@Mapper
public interface DatasetInfoMapper extends UltimateBaseMapper<DatasetInfoDO> {
    Long updateMaxVersionIdById(DatasetInfoDO datasetInfoDO);

    Long updateBasicInfoById(DatasetInfoDO datasetInfoDO);

    Long updateOwnerById(DatasetInfoDO datasetInfoDO);

    IPage<DatasetWrapFlattenDO> selectFlattenList(Page<DatasetWrapFlattenDO> page,
                                                  @Param("name") String name, @Param("id") Long id, @Param("userId") String userId,
                                                  @Param("mediaType") String mediaType, @Param("contentType") String contentType,
                                                  @Param("language") String language, @Param("status") String status, @Param("userId1") String userId1);


}
