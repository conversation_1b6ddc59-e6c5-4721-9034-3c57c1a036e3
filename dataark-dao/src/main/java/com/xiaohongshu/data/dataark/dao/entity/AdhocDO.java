package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "adhoc", autoResultMap = true)
public class AdhocDO extends BaseEntity {

    private String name;

    private Long folderId;

    private String queryType;

    private String datasetVersionIds;

    @TableField(value = "`condition`")
    private String condition;

    @TableField(value = "`rows`")
    private Long rows;

    private boolean save;

    private String ownerId;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser owner;

    private String creatorId;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser creator;

    private String modifierId;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser modifier;


}
