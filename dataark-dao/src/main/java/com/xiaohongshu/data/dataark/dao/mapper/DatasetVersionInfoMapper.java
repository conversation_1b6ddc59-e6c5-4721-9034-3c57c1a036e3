package com.xiaohongshu.data.dataark.dao.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionInfoDetailDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionSimpleDO;
import com.xiaohongshu.dataverse.infra.mybatis.plugins.UltimateBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
@Mapper
public interface DatasetVersionInfoMapper extends UltimateBaseMapper<DatasetVersionInfoDO> {
    Integer selectMaxVersion(Long datasetId);

    DatasetVersionInfoDO selectByDatasetIdAndVersion(@Param("datasetId") Long datasetId, @Param("version") Integer version);

    List<DatasetVersionSimpleDO> selectDatasetVersionSimpleBatchIds(@Param("ids") List<Long> datasetVersionIds);

    List<DatasetVersionInfoDO> selectByDatasetId(Long datasetId);

    List<DatasetVersionInfoDO> selectNonDraftByDatasetId(Long datasetId);

    Long updateStateAndVersionById(DatasetVersionInfoDO datasetVersionInfoDO);

    int updateTokenTaskStatusCAS(@Param("datasetVersionId") Long datasetVersionId, @Param("tokenTaskSource") String tokenTaskSource,
                                 @Param("newStatus") String newStatus, @Param("oldStatus") String oldStatus);

    int saveTokenResult(@Param("datasetVersionId") Long datasetVersionId, @Param("tokens") Long tokens, @Param("records") Long records, @Param("status") String status);

    List<String> selectAllLabels();

    IPage<DatasetVersionInfoDetailDO> adhocListPage(Page<DatasetVersionInfoDetailDO> page, @Param("name") String name, @Param("version") Integer version,
                                                    @Param("mediaType") String mediaType, @Param("contentType") String contentType, @Param("label") String label,
                                                    @Param("userId") String userId, @Param("queryInverted") boolean queryInverted, @Param("queryVector") boolean queryVector);

    List<DatasetVersionInfoDetailDO> selectDetailByIds(@Param("datasetVersionIds") List<Long> datasetVersionIds);

    int updateVectorEmbeddingSuccess(@Param("datasetVersionId") Long datasetVersionId, @Param("newStatus") String newStatus, @Param("oldStatus") String oldStatus);

    int updateVectorEmbeddingFailed(@Param("datasetVersionId") Long datasetVersionId, @Param("newStatus") String newStatus, @Param("errorMsg") String errorMsg, @Param("oldStatus") String oldStatus);

    int updateVectorExportStatusCAS(@Param("datasetVersionId") Long datasetVersionId, @Param("newStatus") String newStatus, @Param("oldStatus") String oldStatus);

    int updateInvertedIndexExportStatusCAS(@Param("datasetVersionId") Long datasetVersionId, @Param("newStatus") String newStatus, @Param("oldStatus") String oldStatus);
}
