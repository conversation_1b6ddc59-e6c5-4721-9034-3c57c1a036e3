package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "adhoc_query_record", autoResultMap = true)
public class AdhocQueryRecordDO extends BaseEntity {

    private Long adhocId;

    private String queryType;

    private String queryId;

    private String datasetVersionIds;

    @TableField(value = "`condition`")
    private String condition;

    @TableField(value = "`rows`")
    private Long rows;

    private String state;

    private String errorMsg;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser creator;

}
