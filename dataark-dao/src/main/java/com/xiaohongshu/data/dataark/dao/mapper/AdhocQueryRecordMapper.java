package com.xiaohongshu.data.dataark.dao.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;
import com.xiaohongshu.dataverse.infra.mybatis.plugins.UltimateBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/9
 */
@Mapper
public interface AdhocQueryRecordMapper extends UltimateBaseMapper<AdhocQueryRecordDO> {

    /**
     * 根据adhocId查询分页记录
     *
     * @param adhocId adhocId
     * @return 分页结果
     */
    IPage<AdhocQueryRecordDO> selectPage(Page<AdhocQueryRecordDO> page, @Param("adhocId") Long adhocId, @Param("queryTypes") List<String> queryTypes);

}
