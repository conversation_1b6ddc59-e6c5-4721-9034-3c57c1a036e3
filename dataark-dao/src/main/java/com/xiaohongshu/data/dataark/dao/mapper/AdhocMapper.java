package com.xiaohongshu.data.dataark.dao.mapper;

import com.xiaohongshu.data.dataark.dao.entity.AdhocDO;
import com.xiaohongshu.dataverse.infra.mybatis.plugins.UltimateBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email ying<PERSON><EMAIL>
 * @date 2025/6/9
 */
@Mapper
public interface AdhocMapper extends UltimateBaseMapper<AdhocDO> {
    List<AdhocDO> search(@Param("keyword") String keyword, @Param("userId") String userId, @Param("queryTypes") List<String> queryTypes);
}
