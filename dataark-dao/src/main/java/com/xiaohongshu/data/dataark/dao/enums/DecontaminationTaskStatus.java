package com.xiaohongshu.data.dataark.dao.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 去污染任务状态
 * <AUTHOR>
 * @Date 2025/8/12 11:30
 */
@AllArgsConstructor
@Getter
public enum DecontaminationTaskStatus {


    RUNNING("Running", "去污染中"),
    SUCCEEDED("Succeeded", "已成功"),
    FAILED("Failed", "已失败"),
    ;

    private String key;
    private String des;

    /**
     * 根据枚举名称获取枚举
     *
     * @param key
     * @return
     */
    public static DecontaminationTaskStatus parse(String key) {
        boolean exit = false;
        DecontaminationTaskStatus type = DecontaminationTaskStatus.RUNNING;
        for (DecontaminationTaskStatus t : DecontaminationTaskStatus.values()) {
            if (t.key.equals(key)) {
                type = t;
                exit = true;
                break;
            }
        }
        if (exit) {
            return type;
        }
        throw new RuntimeException("去污染任务状态不存在");
    }

    public Boolean isFinished() {
        return this == SUCCEEDED || this == FAILED;
    }

}
