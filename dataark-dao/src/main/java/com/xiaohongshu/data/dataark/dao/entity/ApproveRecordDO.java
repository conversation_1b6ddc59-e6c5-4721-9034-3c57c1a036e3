package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @email y<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "approve_record", autoResultMap = true)
public class ApproveRecordDO extends BaseEntity {

    private String approveFormId;

    private String approveStatus;

    private String approveType;

    private Long datasetVersionId;

    private Long binidxTaskId;

    private String creator;

}
