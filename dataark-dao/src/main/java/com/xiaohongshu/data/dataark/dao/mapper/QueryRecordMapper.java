package com.xiaohongshu.data.dataark.dao.mapper;

import com.xiaohongshu.data.dataark.dao.entity.QueryRecordDO;
import com.xiaohongshu.dataverse.infra.mybatis.plugins.UltimateBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QueryRecordMapper extends UltimateBaseMapper<QueryRecordDO> {

    Long updateStateAndProgressAndErrorById(QueryRecordDO queryRecordDO);

    List<QueryRecordDO> selectByDatasetVersionIdAndQueryType(Long datasetVersionId, String queryType);

    QueryRecordDO selectLatestOneByDatasetVersionIdAndQueryType(Long datasetVersionId, String queryType);

    Integer selectRunningRecordCount();

    List<QueryRecordDO> selectByStateWithLimit(String state, Integer limit);

    Long updateSubmitSqlAndQueryIdAndStateById(QueryRecordDO queryRecordDO);

    QueryRecordDO selectLatestSamplePreview(@Param("sampleId") Long sampleId, @Param("queryType") String queryType);
}
