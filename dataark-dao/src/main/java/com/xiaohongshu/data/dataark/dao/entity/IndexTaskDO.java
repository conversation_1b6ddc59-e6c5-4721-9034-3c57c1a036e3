package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "index_task", autoResultMap = true)
public class IndexTaskDO extends BaseEntity {

    private Long datasetVersionId;

    private String datasetVersionName;

    private String indexType;

    private String platform;

    private String taskId;

    private String status;

    private String inputPath;

    private String outputPath;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String errorMsg;
}
