package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/8/22 17:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "dataset_explore_task", autoResultMap = true)
@Slf4j
public class DatasetExploreTaskDO extends BaseEntity {
    private Long datasetId;

    private Long datasetVersionId;

    private String metaName;

    private String status;
}
