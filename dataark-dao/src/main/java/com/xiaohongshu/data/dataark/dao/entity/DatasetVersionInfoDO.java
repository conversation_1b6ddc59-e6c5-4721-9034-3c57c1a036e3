package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.data.dataark.dao.entity.dataset.ProcessingTask;
import com.xiaohongshu.data.dataark.dao.handlers.DatasetProcessingTaskHandler;
import com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "dataset_version_info", autoResultMap = true)
@Slf4j
public class DatasetVersionInfoDO extends BaseEntity {

    private Long datasetId;

    private Integer version;

    private String label;

    private String dataType;

    private String ossPath;

    private String fileType;

    private String catalog;

    private String dbName;

    private String tableName;

    private String language;

    private String description;

    private Boolean isCustom;

    private String redocLink;

//    @TableField(typeHandler = DatasetSourceHandler.class)
//    private List<DataSource> dataSources;

    private String dataSources;

    @TableField(typeHandler = DatasetProcessingTaskHandler.class)
    private List<ProcessingTask> processingTasks;

    private String dataFunnel;

    private String dataValidation;

    private String experimentValidation;

    private String verificationReport;

    private String region;

    private Long tokens;

    private Long size;

    private Long records;

    private String status;

    private String publishOutputPath;

    private String tokenTaskStatus;

    private String tokenTaskSource;

    private String invertedIndexState;

    private String invertedCluster;

    private String invertedErrorMsg;

    private String vectorIndexState;

    private String vectorCluster;

    private String vectorErrorMsg;

    private String vectorDatasetVersionName;

    private String snapshotStatus;

    private String snapshotPath;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser createBy;

    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser updateBy;
}
