package com.xiaohongshu.data.dataark.dao.entity.dataset;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DatasetMetadata {
    private String location;
    private String fileFormat;
    private String catalog;
    private String dbName;
    private String tableName;
}
