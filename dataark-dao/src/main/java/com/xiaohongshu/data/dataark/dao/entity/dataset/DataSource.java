package com.xiaohongshu.data.dataark.dao.entity.dataset;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xiaohongshu.data.dataark.dao.enums.DataSourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataSource {
    private DataSourceType type;
    private Long datasetId;
    private String version;
    private Long datasetVersionId;
    private String datasetName;
    private String datasetCode;
    private String sourceUrl;
    private String sourceDescription;
}
