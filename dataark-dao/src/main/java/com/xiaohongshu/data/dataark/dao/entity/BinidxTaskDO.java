package com.xiaohongshu.data.dataark.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "binidx_task", autoResultMap = true)
public class BinidxTaskDO extends BaseEntity {

    private Long datasetVersionId;

    private String scene;

    private String sampleType;

    private Long sampleId;

    private String jobId;

    private String jobName;

    private String jobConf;

    private String jobUrl;

    private String outputPath;

    private String status;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String creator;

}
