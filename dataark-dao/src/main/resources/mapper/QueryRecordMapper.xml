<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaohongshu.data.dataark.dao.mapper.QueryRecordMapper">
    <resultMap id="BaseResultMap" type="com.xiaohongshu.data.dataark.dao.entity.QueryRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dataset_id" jdbcType="BIGINT" property="datasetId"/>
<!--        <result column="dataset_version" jdbcType="INTEGER" property="datasetVersion"/>-->
        <result column="dataset_version_id" jdbcType="BIGINT" property="datasetVersionId"/>
        <result column="query_type" jdbcType="VARCHAR" property="queryType"/>
        <result column="query_id" jdbcType="VARCHAR" property="queryId"/>
        <result column="query_condition" jdbcType="VARCHAR" property="queryCondition"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="verbose" jdbcType="VARCHAR" property="verbose"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="submit_sql" jdbcType="VARCHAR" property="submitSql"/>
        <result column="executed_sql" jdbcType="VARCHAR" property="executedSql"/>
        <result column="engine" jdbcType="VARCHAR" property="engine"/>
        <result column="queue" jdbcType="VARCHAR" property="queue"/>
        <result column="download" jdbcType="BIT" property="download"/>
        <result column="cost" jdbcType="INTEGER" property="cost"/>
        <result column="rows" jdbcType="INTEGER" property="rows"/>
        <result column="progress" jdbcType="DOUBLE" property="progress"/>
        <result column="progress_info" jdbcType="VARCHAR" property="progressInfo"/>
        <result column="error" jdbcType="VARCHAR" property="error"/>
        <result column="download_url" jdbcType="VARCHAR" property="downloadUrl"/>
        <result column="create_by"
                property="createBy"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="update_by"
                property="updateBy"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectByDatasetVersionIdAndQueryType" resultMap="BaseResultMap">
        select * from query_record
        where dataset_version_id = #{datasetVersionId,jdbcType=BIGINT}
        and query_type = #{queryType,jdbcType=VARCHAR}
        order by id desc
    </select>

    <select id="selectLatestOneByDatasetVersionIdAndQueryType" resultMap="BaseResultMap">
        select * from query_record
        where dataset_version_id = #{datasetVersionId,jdbcType=BIGINT}
        and query_type = #{queryType,jdbcType=VARCHAR}
        order by id desc
        limit 1
    </select>

    <select id="selectRunningRecordCount" resultType="java.lang.Integer">
        select count(*) from query_record
        where state = 'RUNNING'
        and update_time >= NOW() - INTERVAL 7 DAY
    </select>
    
    <select id="selectByStateWithLimit" resultMap="BaseResultMap">
        select * from query_record
        where state = #{state,jdbcType=VARCHAR}
        and query_id is not null
        and update_time >= NOW() - INTERVAL 7 DAY
        order by id asc
        limit #{limit,jdbcType=INTEGER}
    </select>

    <update id="updateStateAndProgressAndErrorById" parameterType="com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO">
        update query_record set state = #{state}, progress = #{progress}, error = #{error}, update_by = #{updateBy,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateSubmitSqlAndQueryIdAndStateById" parameterType="com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO">
        update query_record set submit_sql = #{submitSql}, query_id = #{queryId},
            sample_path = #{samplePath}, state = #{state},
            update_by = #{updateBy,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectLatestSamplePreview" resultMap="BaseResultMap">
        select * from query_record
        where sample_id = #{sampleId}
        and query_type = #{queryType,jdbcType=VARCHAR}
        order by id desc
        limit 1
    </select>
</mapper>