<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaohongshu.data.dataark.dao.mapper.AdhocMapper">

    <resultMap id="BaseResultMap" type="com.xiaohongshu.data.dataark.dao.entity.AdhocDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="folder_id" jdbcType="BIGINT" property="folderId"/>
        <result column="query_type" jdbcType="VARCHAR" property="queryType"/>
        <result column="dataset_version_ids" jdbcType="VARCHAR" property="datasetVersionIds"/>
        <result column="condition" jdbcType="LONGVARCHAR" property="condition"/>
        <result column="rows" jdbcType="INTEGER" property="rows"/>
        <result column="save" jdbcType="TINYINT" property="save"/>
        <result column="owner_id" jdbcType="VARCHAR" property="ownerId"/>
        <result column="owner"
                property="owner"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId"/>
        <result column="creator"
                property="creator"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="modifier_id" jdbcType="VARCHAR" property="modifierId"/>
        <result column="modifier"
                property="modifier"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="search" resultMap="BaseResultMap">
        select * from adhoc
        <where>
            save = 1
            <if test="userId != null and userId != ''">
                and owner_id = #{userId}
            </if>
            <if test="keyword != null and keyword != ''">
                and name like CONCAT('%', #{keyword}, '%')
            </if>
            <if test="queryTypes != null and queryTypes.size() > 0">
                and query_type in
                <foreach collection="queryTypes" separator="," open="(" close=")" item="queryType">
                    #{queryType,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
</mapper>