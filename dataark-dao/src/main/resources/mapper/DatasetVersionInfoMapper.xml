<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper">
    <resultMap id="BaseResultMap" type="com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dataset_id" jdbcType="BIGINT" property="datasetId"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="oss_path" jdbcType="VARCHAR" property="ossPath"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="catalog" jdbcType="VARCHAR" property="catalog"/>
        <result column="db_name" jdbcType="VARCHAR" property="dbName"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="language" jdbcType="VARCHAR" property="language"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="is_custom" jdbcType="TINYINT" property="isCustom"/>
        <result column="redoc_link" jdbcType="VARCHAR" property="redocLink"/>
        <result column="data_sources" jdbcType="VARCHAR" property="dataSources"/>
<!--        <result column="data_sources"-->
<!--                property="dataSources"-->
<!--                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.DatasetSourceHandler"-->
<!--                javaType="java.util.List"-->
<!--        />-->
        <result column="processing_tasks"
                property="processingTasks"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.DatasetProcessingTaskHandler"
                javaType="java.util.List"
        />
        <result column="data_funnel" jdbcType="VARCHAR" property="dataFunnel"/>
        <result column="data_validation" jdbcType="VARCHAR" property="dataValidation"/>
        <result column="experiment_validation" jdbcType="VARCHAR" property="experimentValidation"/>
        <result column="verification_report" jdbcType="VARCHAR" property="verificationReport"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="tokens" jdbcType="BIGINT" property="tokens"/>
        <result column="size" jdbcType="BIGINT" property="size"/>
        <result column="records" jdbcType="BIGINT" property="records"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_by"
                property="createBy"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="update_by"
                property="updateBy"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="DatasetVersionSimpleDOResultMap" type="com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionSimpleDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dataset_id" jdbcType="BIGINT" property="datasetId"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
    </resultMap>

    <resultMap id="DatasetVersionDetailDOResultMap" type="com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionInfoDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dataset_id" jdbcType="BIGINT" property="datasetId"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="media_type" jdbcType="VARCHAR" property="mediaType"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="is_raw" jdbcType="TINYINT" property="isRaw"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="language" jdbcType="VARCHAR" property="language"/>
        <result column="tokens" jdbcType="BIGINT" property="tokens"/>
        <result column="size" jdbcType="BIGINT" property="size"/>
        <result column="records" jdbcType="BIGINT" property="records"/>
        <result column="token_task_status" jdbcType="VARCHAR" property="tokenTaskStatus"/>
        <result column="owner"
                property="owner"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <select id="selectMaxVersion" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select max(version) from dataset_version_info where dataset_id = #{datasetId}
    </select>

    <select id="selectByDatasetIdAndVersion" resultMap="BaseResultMap">
        select * from dataset_version_info
        where dataset_id = #{datasetId,jdbcType=BIGINT}
        and version = #{version,jdbcType=INTEGER}
    </select>

    <select id="selectDatasetVersionSimpleBatchIds" parameterType="java.util.List" resultMap="DatasetVersionSimpleDOResultMap">
        select a.id, a.dataset_id, a.version, b.name, b.code
        from dataset_version_info a
        left join dataset_info b
        on a.dataset_id = b.id
        where a.id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectByDatasetId" resultMap="BaseResultMap">
        select * from dataset_version_info
        where dataset_id = #{datasetId,jdbcType=BIGINT}
    </select>

    <select id="selectNonDraftByDatasetId" resultMap="BaseResultMap">
        select * from dataset_version_info
        where dataset_id = #{datasetId,jdbcType=BIGINT} and status != 'draft'
        order by version desc
    </select>

    <update id="updateStateAndVersionById" parameterType="com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO">
        update dataset_version_info
        <set>
            <if test="status!=null and status!=''">
                status = #{status},
            </if>
            <if test="version!=null and version!=''">
                version = #{version},
            </if>
            <if test="snapshotStatus!=null and snapshotStatus!=''">
                snapshot_status = #{snapshotStatus},
            </if>
            <if test="updateBy!=null">
                update_by = #{updateBy,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateTokenTaskStatusCAS">
        update dataset_version_info
        set token_task_status = #{newStatus}, token_task_source = #{tokenTaskSource}
        where id = #{datasetVersionId} and token_task_status = #{oldStatus}
    </update>

    <update id="saveTokenResult">
        update dataset_version_info
        set token_task_status = #{status}, tokens = #{tokens}, records = #{records}
        where id = #{datasetVersionId}
    </update>

    <select id="selectAllLabels" resultType="java.lang.String">
        select distinct(label) from dataset_version_info
        where label is not null and label != ''
    </select>

    <select id="adhocListPage" resultMap="DatasetVersionDetailDOResultMap">
        select
            dvi.id, dvi.dataset_id, dvi.version, di.name, di.code,
            di.media_type, di.content_type, di.is_raw, dvi.status,
            dvi.language, dvi.tokens, dvi.size, dvi.records,
            dvi.token_task_status, di.owner, dvi.create_time
        from dataset_version_info dvi
        left join dataset_info di on dvi.dataset_id = di.id
        <where>
            dvi.status != 'draft'
            <if test="name != null and name != ''">
                and di.name like concat('%', #{name}, '%')
            </if>
            <if test="version != null">
                and dvi.version = #{version}
            </if>
            <if test="mediaType != null and mediaType != ''">
                and di.media_type = #{mediaType}
            </if>
            <if test="contentType != null and contentType != ''">
                and di.content_type = #{contentType}
            </if>
            <if test="label != null and label != ''">
                and dvi.label = #{label}
            </if>
            <if test="userId != null and userId != ''">
                and di.owner ->> '$.userId' = #{userId}
            </if>
            <if test="queryInverted != null and queryInverted">
                and dvi.inverted_index_state = 'success'
            </if>
            <if test="queryVector != null and queryVector">
                and dvi.vector_index_state = 'success'
            </if>
        </where>
    </select>

    <select id="selectDetailByIds" resultMap="DatasetVersionDetailDOResultMap">
        select
            dvi.id, dvi.dataset_id, dvi.version, di.name, di.code,
            di.media_type, di.content_type, di.is_raw, dvi.status,
            dvi.language, dvi.tokens, dvi.size, dvi.records,
            dvi.token_task_status, di.owner, dvi.create_time
        from dataset_version_info dvi
                 left join dataset_info di on dvi.dataset_id = di.id
        <where>
            dvi.id in
            <foreach collection="datasetVersionIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>

    <update id="updateVectorEmbeddingSuccess">
        update dataset_version_info
        set vector_index_state = #{newStatus}
        where id = #{datasetVersionId} and vector_index_state = #{oldStatus}
    </update>

    <update id="updateVectorEmbeddingFailed">
        update dataset_version_info
        set vector_index_state = #{newStatus}, vector_error_msg = #{errorMsg}
        where id = #{datasetVersionId} and  vector_index_state = #{oldStatus}
    </update>

    <update id="updateVectorExportStatusCAS">
        update dataset_version_info
        set vector_index_state = #{newStatus}
        where id = #{datasetVersionId} and vector_index_state = #{oldStatus}
    </update>

    <update id="updateInvertedIndexExportStatusCAS">
        update dataset_version_info
        set inverted_index_state = #{newStatus}
        where id = #{datasetVersionId} and inverted_index_state = #{oldStatus}
    </update>
</mapper>