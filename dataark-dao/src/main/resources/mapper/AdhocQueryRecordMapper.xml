<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaohongshu.data.dataark.dao.mapper.AdhocQueryRecordMapper">
    
    <resultMap id="BaseResultMap" type="com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="adhoc_id" jdbcType="BIGINT" property="adhocId"/>
        <result column="query_type" jdbcType="VARCHAR" property="queryType"/>
        <result column="dataset_version_ids" jdbcType="VARCHAR" property="datasetVersionIds"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="condition" jdbcType="VARCHAR" property="condition"/>
        <result column="rows" jdbcType="INTEGER" property="rows"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="creator"
                property="creator"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select id, adhoc_id, query_type, dataset_version_ids, state, `condition`, rows, error_msg,
        creator, create_time, update_time from adhoc_query_record
        <where>
            <if test="adhocId != null">
                and adhoc_id = #{adhocId}
            </if>
            <if test="queryTypes != null and queryTypes.size() > 0">
                and query_type in
                <foreach collection="queryTypes" separator="," open="(" close=")" item="queryType">
                    #{queryType,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
</mapper>