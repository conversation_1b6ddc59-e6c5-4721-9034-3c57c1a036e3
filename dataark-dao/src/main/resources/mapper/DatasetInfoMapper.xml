<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaohongshu.data.dataark.dao.mapper.DatasetInfoMapper">
    <resultMap id="BaseResultMap" type="com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="media_type" jdbcType="VARCHAR" property="mediaType"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="is_raw" jdbcType="TINYINT" property="isRaw"/>
        <result column="max_version_id" jdbcType="BIGINT" property="maxVersionId"/>
        <result column="create_by"
                property="createBy"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="update_by"
                property="updateBy"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="owner"
                property="owner"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
    </resultMap>

    <resultMap id="DatasetWrapFlattenResultMap" type="com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetWrapFlattenDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="media_type" jdbcType="VARCHAR" property="mediaType"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="is_raw" jdbcType="TINYINT" property="isRaw"/>
        <result column="max_version_id" jdbcType="BIGINT" property="maxVersionId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="language" jdbcType="VARCHAR" property="language"/>
        <result column="owner"
                property="owner"
                typeHandler="com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler"
                javaType="com.xiaohongshu.dataverse.common.pojo.SimpleUser"
        />
        <result column="tokens" jdbcType="BIGINT" property="tokens"/>
        <result column="size" jdbcType="BIGINT" property="size"/>
        <result column="records" jdbcType="BIGINT" property="records"/>
        <result column="token_task_status" jdbcType="VARCHAR" property="tokenTaskStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <update id="updateMaxVersionIdById" parameterType="com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO">
        update dataset_info
        <set>
            <if test="maxVersionId!=null and maxVersionId!=''">
                max_version_id = #{maxVersionId},
            </if>
            <if test="updateBy!=null">
                update_by = #{updateBy,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBasicInfoById" parameterType="com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO">
        update dataset_info
        <set>
            name = #{name,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            media_type = #{mediaType,jdbcType=VARCHAR},
            content_type = #{contentType,jdbcType=VARCHAR},
            is_raw = #{isRaw,jdbcType=TINYINT},
            update_by = #{updateBy,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler},
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateOwnerById" parameterType="com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO">
        update dataset_info
        <set>
            owner = #{owner,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler},
            update_by = #{updateBy,typeHandler=com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler},
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectFlattenList" resultMap="DatasetWrapFlattenResultMap">
        select
            a.id, a.name, a.media_type, a.content_type, a.is_raw, a.max_version_id, b.status, b.language, a.owner, b.tokens, b.size, b.records, b.token_task_status, a.create_time
        from
            dataset_info a
        left join
            dataset_version_info b
        on
            a.max_version_id = b.id
        <where>
            a.max_version_id is not null
            <if test="name != null">
                and a.name like CONCAT('%', #{name}, '%')
            </if>
            <if test="id != null">
                and a.id = #{id}
            </if>
            <if test="userId != null">
                and a.owner ->> '$.userId' = #{userId}
            </if>
            <if test="mediaType != null">
                and a.media_type = #{mediaType}
            </if>
            <if test="contentType != null">
                and a.content_type = #{contentType}
            </if>
            <if test="language != null">
                and b.language = #{language}
            </if>
            <if test="status != null">
                and b.status = #{status}
            </if>
            <if test="userId1 != null">
                and a.owner ->> '$.userId' = #{userId1}
            </if>
        </where>
        order by a.create_time desc, a.id desc
    </select>
</mapper>