<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaohongshu.data.dataark.dao.mapper.BinidxTaskMapper">

    <select id="pageList" resultType="com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO">
        SELECT id, dataset_version_id, scene, sample_type, sample_id, job_id, job_name,
            job_url, output_path, status, start_time, end_time, creator, create_time, update_time
        FROM binidx_task
        <where>
            <if test="datasetVersionId != null and datasetVersionId != ''">
                and dataset_version_id = #{datasetVersionId}
            </if>
            and job_id is not null and job_id != ''
        </where>
        order by create_time desc
    </select>
</mapper>