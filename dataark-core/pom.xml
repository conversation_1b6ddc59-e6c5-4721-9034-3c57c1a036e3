<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaohongshu.data</groupId>
        <artifactId>dataark</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>dataark-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.xiaohongshu.data</groupId>
            <artifactId>dataark-dao</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>thrift-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>dataverse-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.red.data.infra</groupId>
            <artifactId>stalker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu.fls.thrift</groupId>
            <artifactId>lib-thrift-oa-public</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>thrift-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>thrift-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>pai_dlc20201203</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu.sec</groupId>
            <artifactId>kms-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu.infra.midware</groupId>
            <artifactId>redschedule-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu.data</groupId>
            <artifactId>capella-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dataverse.infra</groupId>
            <artifactId>soda-http-proxy</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.gravitino</groupId>
            <artifactId>gravitino-client-java-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
            <version>3.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.24.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.parquet</groupId>
            <artifactId>parquet-avro</artifactId>
            <version>1.15.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>1.11.4</version> <!-- 使用适合你项目的版本 -->
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.16.1</version>
        </dependency>
        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.4.10</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaohongshu.infra.midware</groupId>
            <artifactId>redis-spring</artifactId>
        </dependency>

    </dependencies>

</project>