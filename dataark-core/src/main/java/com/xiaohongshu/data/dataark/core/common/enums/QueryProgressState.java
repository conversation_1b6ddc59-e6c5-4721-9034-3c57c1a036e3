package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/24
 */
@Getter
public enum QueryProgressState {

    RUNNING("RUNNING", "正在运行SQL"),
    KILLED("KILLED", "被终止"),
    ERROR("ERROR", "运行错误"),
    FINISHED("FINISHED", "运行结束");

    private final String code;
    private final String desc;

    QueryProgressState(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
