package com.xiaohongshu.data.dataark.core.rpc.capella;

import com.dataverse.infra.soda.Soda;
import com.xiaohongshu.data.capella.spi.BaseHttpResult;
import com.xiaohongshu.data.capella.spi.QuerySPI;
import com.xiaohongshu.data.dataark.core.rpc.HttpClientProvider;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;

@Service
public class CapellaApiCaller {

    @Value("${third_party.capella.domain}")
    private String capellaDomain;

    public static final String SCENE = "dataverse";

    private static final String TOKEN = "ZGF0YXZlcnNlOnpmeVAmZXhycGNkemVSQGM=";

    public static final String X_CAPELLA_AUTH = "X-CAPELLA-AUTH";

    private QuerySPI querySPI;

    @PostConstruct
    private void init() {
        querySPI = new Soda(capellaDomain, HttpClientProvider.httpClient()).addGlobalHeader(X_CAPELLA_AUTH, TOKEN)
                .create(QuerySPI.class);
    }

    /**
     * 同步执行SQL，返回执行结果
     * 异步执行SQL，返回queryId
     * @param customQueryId 提交至yarn的tag id, 便于检索
     * @param runSQLRequest sql查询请求对象
     * @return
     * @throws Exception
     */
    public QuerySPI.RunSQLResponse runSql(String customQueryId, QuerySPI.RunSQLRequest runSQLRequest) throws Exception {
        BaseHttpResult<QuerySPI.RunSQLResponse> result = querySPI.runSql(SCENE, customQueryId, runSQLRequest);
        boolean success = result.getSuccess();
        if (!success) {
            throw new ServiceException(result.getMsg());
        }
        return Objects.requireNonNull(result.getData());
    }

    /**
     * 获取查询进度
     * @param queryId QueryId
     * @return progress
     * @throws Exception when request failed
     */
    public QuerySPI.ProgressResp queryProgress(String queryId) throws Exception {
        BaseHttpResult<QuerySPI.ProgressResp> result = querySPI.progress(queryId);
        boolean success = result.getSuccess();
        if (!success) {
            throw new ServiceException(result.getMsg());
        }
        return result.getData();
    }

    /**
     * 获取查询结果
     *
     * @param queryId QueryId
     * @return result
     * @throws Exception when request failed
     */
    public QuerySPI.AsyncQueryResult queryResult(String queryId) throws Exception {
        BaseHttpResult<QuerySPI.AsyncQueryResult> result = querySPI.obtainResults(queryId);
        boolean success = result.getSuccess();
        if (!success) {
            throw new ServiceException(result.getMsg());
        }
        return result.getData();
    }

    public QuerySPI.SqlLogV2Response queryLogV2(String queryId) throws Exception {
        BaseHttpResult<QuerySPI.SqlLogV2Response> sqlLogsV2 = querySPI.sqlLogsV2(queryId);
        boolean success = sqlLogsV2.getSuccess();
        if (!success) {
            throw new ServiceException(sqlLogsV2.getMsg());
        }
        return sqlLogsV2.getData();
    }

    /**
     * 取消查询
     *
     * @param cancelRequest 取消请求
     * @throws Exception when request failed
     */
    public Boolean cancel(QuerySPI.CancelRequest cancelRequest) throws Exception {
        BaseHttpResult<Object> result = querySPI.cancel(cancelRequest);
        boolean success = result.getSuccess();
        if (!success) {
            throw new ServiceException(result.getMsg());
        }
        return true;
    }
}
