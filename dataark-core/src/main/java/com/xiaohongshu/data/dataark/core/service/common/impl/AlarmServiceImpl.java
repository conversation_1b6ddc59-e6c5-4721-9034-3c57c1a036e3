package com.xiaohongshu.data.dataark.core.service.common.impl;

import com.xiaohongshu.data.dataark.core.common.constant.LogConstants;
import com.xiaohongshu.data.dataark.core.config.apollo.ApolloCommonConfig;
import com.xiaohongshu.data.dataark.core.rpc.crossroad.CrossroadApiCaller;
import com.xiaohongshu.data.dataark.core.service.common.AlarmService;
import com.xiaohongshu.data.dataark.core.utils.HostNameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
public class AlarmServiceImpl implements AlarmService {

    private static final Logger log = LoggerFactory.getLogger(AlarmServiceImpl.class);
    @Resource
    private ApolloCommonConfig apolloCommonConfig;

    @Resource
    private CrossroadApiCaller crossroadApiCaller;


    @Override
    public void alarmVChat(String content) {
        String msg = buildMsgRequestBody(StringUtils.EMPTY, StringUtils.EMPTY, content);
        try {
            crossroadApiCaller.alarmRobot(msg);
        } catch (Exception e) {
            log.error("alarmVChat error : {}", e.getMessage(), e);
        }
    }

    /**
     * 构建 消息体
     * @param errorMessage 错误信息
     * @return dc请求体-json
     */
    private String buildMsgRequestBody(String className, String method, String errorMessage) {

        String env = StringUtils.isEmpty(apolloCommonConfig.getEnv()) ?
                "unknown" : apolloCommonConfig.getEnv();
        String hostName = HostNameUtils.getInetAddress().getHostAddress();
        String traceId = MDC.get(LogConstants.TRACE_ID);
        return buildAlarmMsg(env, hostName, traceId, className, method, errorMessage);
    }

    /**
     * 构建 告警信息
     */
    private String buildAlarmMsg(String env, String hostName, String traceId,
                                 String className, String method, String errorMessage) {
        return "<font color=#FF0000 > **dataark[" + env + "] 接口异常** </font> \n" +
                "<font color=#0000FF > **hostName: ** </font>" + hostName +
                "<font color=#0000FF >;     **traceId: ** </font>" + traceId + "\n" +
                "<font color=#0000FF > **接口: ** </font>" + className + "#" + method + "\n \n" +
                "<font color=#008000 > *------------------------------------------------------------------------------\n * </font>" +
                errorMessage;
    }

}
