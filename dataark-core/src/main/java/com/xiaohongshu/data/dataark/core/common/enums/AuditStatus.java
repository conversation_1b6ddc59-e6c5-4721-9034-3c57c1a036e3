package com.xiaohongshu.data.dataark.core.common.enums;

import java.util.Arrays;
import java.util.List;

public enum AuditStatus {
    IN_REVIEW(1, "审批中"),
    AUDIT_PASS(2, "审核通过"),
    /**
     * 审核拒绝状态暂时没用
     */
    AUDIT_REFUSE(3, "审核拒绝"),
    AUDIT_TERMINATE(4, "已终止"),
    WITHDRAWAL(5, "撤回待发起"),
    DELETE(6, "已终止");

    private int code;
    private String desc;

    AuditStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<String> REFUSED_STATUS = Arrays.asList(AuditStatus.AUDIT_REFUSE.name(), AuditStatus.AUDIT_TERMINATE.name(),
            AuditStatus.WITHDRAWAL.name(), AuditStatus.DELETE.name());

    public static List<String> FINISH_STATUS = Arrays.asList(AuditStatus.AUDIT_PASS.name(), AuditStatus.AUDIT_REFUSE.name(), AuditStatus.AUDIT_TERMINATE.name(),
            AuditStatus.WITHDRAWAL.name(), AuditStatus.DELETE.name());

}
