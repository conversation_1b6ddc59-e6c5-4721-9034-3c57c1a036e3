package com.xiaohongshu.data.dataark.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.data.dataark.core.common.enums.*;
import com.xiaohongshu.data.dataark.core.common.model.dto.ProgressDTO;
import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.data.dataark.core.es.EsService;
import com.xiaohongshu.data.dataark.core.service.*;
import com.xiaohongshu.data.dataark.core.utils.DateUtils;
import com.xiaohongshu.data.dataark.dao.entity.AdhocDO;
import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionInfoDetailDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionSimpleDO;
import com.xiaohongshu.data.dataark.dao.mapper.AdhocMapper;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.xiaohongshu.data.dataark.core.common.enums.AdhocQueryType.INVERTED;
import static com.xiaohongshu.data.dataark.core.common.enums.AdhocQueryType.PRECISE;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
@Service
@Slf4j
public class AdhocServiceImpl implements AdhocService {

    @Resource
    private DatasetVersionInfoService datasetVersionInfoService;

    @Resource
    private AdhocQueryRecordService adhocQueryRecordService;

    @Resource
    private AdhocMapper adhocMapper;

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Resource
    private VectorService vectorService;

    @Resource
    private EsService esService;

    @Resource
    private WebSearchService webSearchService;

    @Override
    public PageResult<DatasetVersionDetailVO> getDatasetVersionList(String queryType, String name, Integer version, String mediaType,
                                                                    String contentType, String label, String userId, Integer pageIndex, Integer pageSize) {
        boolean queryInverted = PRECISE.getCode().equals(queryType)
                || AdhocQueryType.INVERTED.getCode().equals(queryType);
        boolean queryVector = AdhocQueryType.VECTOR.getCode().equals(queryType);
        IPage<DatasetVersionInfoDetailDO> adhocListPage = datasetVersionInfoService.adhocListPage(name, version, mediaType,
                contentType, label, userId, queryInverted, queryVector, pageIndex, pageSize);
        List<DatasetVersionDetailVO> datasetVersionDetailVOS = adhocListPage.getRecords().stream().map(d -> {
            DatasetVersionDetailVO datasetVersionDetailVO = new DatasetVersionDetailVO();
            datasetVersionDetailVO.setId(d.getId());
            datasetVersionDetailVO.setDatasetId(d.getDatasetId());
            datasetVersionDetailVO.setName(d.getName());
            datasetVersionDetailVO.setCode(d.getCode());
            datasetVersionDetailVO.setVersion(d.getVersion());
            datasetVersionDetailVO.setMediaType(d.getMediaType());
            datasetVersionDetailVO.setContentType(d.getContentType());
            datasetVersionDetailVO.setIsRaw(d.getIsRaw());
            datasetVersionDetailVO.setLanguage(d.getLanguage());
            datasetVersionDetailVO.setStatus(d.getStatus());
            datasetVersionDetailVO.setCreateTime(DateUtils.format(d.getCreateTime(), DateUtils.YYYY_MM_DD_HHMMSS));
            if (TokenTaskStatus.SUCCESS.getCode().equals(d.getTokenTaskStatus())) {
                // token除以10的9次方保留两位小数
                String token = String.format("%.2f", d.getTokens() / Math.pow(10, 9));
                // size除以1024的3次方保留两位小数
                String size = String.format("%.2f", d.getSize() / Math.pow(1024, 3));
                datasetVersionDetailVO.setStatistics(token + "B/" + size + "GB/" + d.getRecords());
            } else {
                datasetVersionDetailVO.setStatistics(TokenTaskStatus.fromCode(d.getTokenTaskStatus()).getDesc());
            }
            datasetVersionDetailVO.setOwner(d.getOwner());
            return datasetVersionDetailVO;
        }).collect(Collectors.toList());
        return PageResult.build(pageIndex, pageSize, adhocListPage.getTotal(), datasetVersionDetailVOS);
    }

    @Override
    public Long createAdhoc(SimpleUser user) {
        AdhocDO adhocDO = new AdhocDO();
        adhocDO.setOwnerId(user.getUserId());
        adhocDO.setOwner(user);
        adhocDO.setCreatorId(user.getUserId());
        adhocDO.setCreator(user);
        adhocMapper.insert(adhocDO);
        return adhocDO.getId();
    }

    @Override
    public void deleteAdhoc(Long adhocId, SimpleUser user) {
        AdhocDO adhocDO = adhocMapper.selectById(adhocId);
        if (adhocDO == null) {
            throw new ServiceException("Adhoc not found with id: " + adhocId);
        }
        if (!adhocDO.getOwnerId().equals(user.getUserId())) {
            throw new ServiceException("无权限删除该Adhoc");
        }
        adhocMapper.deleteById(adhocId);
    }

    @Override
    public void saveAdhoc(AdhocRequest request, SimpleUser user) {
        AdhocDO adhocDO = adhocMapper.selectById(request.getAdhocId());
        if (adhocDO == null) {
            throw new ServiceException("Adhoc not found with id: " + request.getAdhocId());
        }
        if (!adhocDO.getOwnerId().equals(user.getUserId())) {
            throw new ServiceException("无权限修改该Adhoc");
        }
        checkNameExists(request.getAdhocId(), request.getName());
        adhocDO.setName(request.getName());
        adhocDO.setQueryType(request.getQueryType());
        adhocDO.setDatasetVersionIds(JsonUtil.toString(request.getDatasetVersionIds()));
        adhocDO.setCondition(request.getCondition().trim());
        adhocDO.setRows(request.getRows());
        adhocDO.setSave(true);
        adhocDO.setFolderId(request.getFolderId());
        adhocDO.setModifierId(user.getUserId());
        adhocDO.setModifier(user);
        adhocDO.setUpdateTime(LocalDateTime.now());
        adhocMapper.updateById(adhocDO);
    }

    @Override
    public AdhocVO getAdhocDetail(Long adhocId) {
        AdhocDO adhocDO = adhocMapper.selectById(adhocId);
        if (adhocDO == null) {
            throw new ServiceException("Adhoc not found with id: " + adhocId);
        }
        AdhocVO adhocVO = new AdhocVO();
        adhocVO.setAdhocId(adhocDO.getId());
        adhocVO.setName(adhocDO.getName());
        adhocVO.setQueryType(adhocDO.getQueryType());
        adhocVO.setDatasetVersionIds(JsonUtil.toList(adhocDO.getDatasetVersionIds(), Long.class));
        adhocVO.setCondition(adhocDO.getCondition());
        adhocVO.setRows(adhocDO.getRows());
        adhocVO.setFolderId(adhocDO.getFolderId());
        adhocVO.setOwner(adhocDO.getOwner());
        adhocVO.setCreateTime(DateUtils.format(adhocDO.getCreateTime(), DateUtils.YYYY_MM_DD_HHMMSS));
        return adhocVO;
    }

    private boolean isDsl(String condition) {
        if (StringUtils.isEmpty(condition)) {
            return false;
        }
        try {
            JSONObject.parseObject(condition);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private EsQueryGrammar getEsQueryType(String condition) {
        if (isDsl(condition)) {
            return EsQueryGrammar.DSL;
        }
        if (StringUtils.isNotEmpty(condition) && condition.contains(":")) {
            return EsQueryGrammar.URI;
        }
        return EsQueryGrammar.TEXT;
    }

    public List<EsSearchResultVO> esSearch(List<DatasetVersionInfoDO> datasetVersionInfoDOS, String condition, AdhocQueryType queryType, Long rows) {
        // 校验是同一个集群
        Set<String> clusters = datasetVersionInfoDOS.stream().map(DatasetVersionInfoDO::getInvertedCluster).collect(Collectors.toSet());
        if (clusters.size() > 1) {
            throw new ServiceException("不支持跨ES集群查询多个索引");
        }
        // 不能有空集群
        if (clusters.contains(null)) {
            throw new ServiceException("ES集群信息为空");
        }

        String esCluster = clusters.iterator().next();
        List<String> indexNames = datasetVersionInfoDOS.stream().map(v -> esService.indexNameFormat(v.getId()))
                .collect(Collectors.toList());

        EsQueryGrammar esQueryGrammar = getEsQueryType(condition);

        if (PRECISE.equals(queryType)) {
            // 精确匹配只支持查询text字段
            if (!EsQueryGrammar.TEXT.equals(esQueryGrammar)) {
                throw new ServiceException("DSL或者URL Search不支持选择精确匹配，请通过查询语句调整匹配规则");
            }
            if (StringUtils.isEmpty(condition)) {
                throw new ServiceException("请输入查询条件");
            }
            return esService.textMatchPhrase(esCluster, indexNames, condition, rows, null);
        } else {
            switch (esQueryGrammar) {
                case DSL:
                    return esService.dslQuery(esCluster, indexNames, condition, rows, null);
                case URI:
                    return esService.uriQuery(esCluster, indexNames, condition, rows, null);
                default:
                    if (StringUtils.isNotEmpty(condition)) {
                        return esService.textMatch(esCluster, indexNames, condition, rows, null);
                    } else {
                        return esService.matchAll(esCluster, indexNames, rows, null);
                    }
            }
        }
    }

    @Override
    public AdhocQueryResultVO query(AdhocQueryRequest request, SimpleUser user) {
        AdhocQueryResultVO adhocQueryResultVO = new AdhocQueryResultVO();
        List<DatasetVersionInfoDO> datasetVersionInfoDOS = new ArrayList<>();
        boolean needDataset = !AdhocQueryType.WEB.getCode().equals(request.getQueryType());
        if (needDataset) {
            List<Long> datasetVersionIds = request.getDatasetVersionIds();
            if (CollectionUtils.isEmpty(datasetVersionIds)) {
                throw new ServiceException("Dataset version IDs cannot be empty");
            }
            boolean invertedTypeQuery = AdhocQueryType.INVERTED_QUERY_TYPES.contains(request.getQueryType());
            boolean vectorTypeQuery = AdhocQueryType.VECTOR_QUERY_TYPES.contains(request.getQueryType());
            datasetVersionInfoDOS = datasetVersionInfoService.selectByIds(datasetVersionIds);
            datasetVersionInfoDOS.forEach(datasetVersionInfo -> {
                if (datasetVersionInfo == null) {
                    throw new ServiceException("Dataset version not exist");
                }
                if (invertedTypeQuery && !InvertedIndexState.SUCCESS.getCode().equals(datasetVersionInfo.getInvertedIndexState())) {
                    throw new ServiceException("倒排索引状态异常，无法进行查询, datasetVersionId: " + datasetVersionInfo.getId());
                }
                if (vectorTypeQuery && !VectorIndexState.SUCCESS.getCode().equals(datasetVersionInfo.getVectorIndexState())) {
                    throw new ServiceException("向量索引状态异常，无法进行查询, datasetVersionId: " + datasetVersionInfo.getId());
                }
            });
        }
        AdhocQueryRecordDO adhocQueryRecordDO = AdhocQueryRequest.convert2AdhocQueryRecordDO(request);
        AdhocQueryType queryType = AdhocQueryType.fromCode(request.getQueryType());
        switch (queryType) {
            case PRECISE:
            case INVERTED:
                List<EsSearchResultVO> esSearchResults = esSearch(datasetVersionInfoDOS, request.getCondition(), queryType, request.getRows());
                adhocQueryResultVO.setSearchResult(esSearchResults);
                break;
            case VECTOR:
                // 向量索引查询逻辑
                List<VectorResultVO> results = vectorService.query(request, datasetVersionInfoDOS);
                adhocQueryResultVO.setSearchResult(results);
                break;
            case WEB:
                String queryId = webSearchService.query(request, user);
                adhocQueryResultVO.setQueryId(queryId);
                adhocQueryRecordDO.setState(CapellaQueryState.RUNNING.getState());
                adhocQueryRecordDO.setQueryId(adhocQueryResultVO.getQueryId());
                request.setAddQueryRecord(true);
                break;
            default:
                throw new ServiceException("Unsupported query type: " + request.getQueryType());
        }
        if (request.isAddQueryRecord()) {
            adhocQueryRecordDO.setCreator(user);
            adhocQueryRecordService.insert(adhocQueryRecordDO);
            adhocQueryResultVO.setAdhocQueryRecordId(adhocQueryRecordDO.getId());
        }
        return adhocQueryResultVO;
    }

    @Override
    public PageResult<AdhocQueryRecordVO> getQueryRecords(Long adhocId, String folderType, Integer pageIndex, Integer pageSize) {
        List<String> queryTypes = AdhocQueryType.getQueryTypes(folderType);

        IPage<AdhocQueryRecordDO> adhocQueryRecordPage = adhocQueryRecordService.getAdhocQueryRecordPage(adhocId, queryTypes, pageIndex, pageSize);
        if (adhocQueryRecordPage == null || adhocQueryRecordPage.getTotal() == 0) {
            return PageResult.build(pageIndex, pageSize, 0L, new ArrayList<>());
        }

        Map<Long, DatasetVersionSimpleDO> datasetVersionSimpleDOMap;
        boolean needDataset = !FolderType.web.name().equals(folderType);
        if (needDataset) {
            List<Long> datasetVersionIds = adhocQueryRecordPage.getRecords().stream()
                    .flatMap(it -> JsonUtil.toList(it.getDatasetVersionIds(), Long.class).stream())
                    .distinct().collect(Collectors.toList());
            List<DatasetVersionSimpleDO> datasetVersionSimpleDOS = datasetVersionInfoMapper.selectDatasetVersionSimpleBatchIds(datasetVersionIds);
            // 将datasetVersionSimpleDO转换为Map<Long, DatasetVersionSimpleDO>，方便后续查询
            datasetVersionSimpleDOMap = datasetVersionSimpleDOS.stream()
                    .collect(Collectors.toMap(DatasetVersionSimpleDO::getId, it -> it, (existing, replacement) -> existing));
        } else {
            datasetVersionSimpleDOMap = new HashMap<>();
        }

        List<AdhocQueryRecordVO> adhocQueryRecordVOS = adhocQueryRecordPage.getRecords().stream().map(it -> {
            AdhocQueryRecordVO adhocQueryRecordVO = new AdhocQueryRecordVO();
            adhocQueryRecordVO.setAdhocQueryRecordId(it.getId());
            adhocQueryRecordVO.setAdhocId(it.getAdhocId());
            adhocQueryRecordVO.setQueryType(it.getQueryType());
            adhocQueryRecordVO.setState(it.getState());
            adhocQueryRecordVO.setCondition(it.getCondition());
            adhocQueryRecordVO.setRows(it.getRows());
            adhocQueryRecordVO.setCreator(it.getCreator());
            adhocQueryRecordVO.setCreateTime(DateUtils.format(it.getCreateTime(), DateUtils.YYYY_MM_DD_HHMMSS));
            if (needDataset) {
                List<Long> recordDatasetVersionIds = JsonUtil.toList(it.getDatasetVersionIds(), Long.class);
                adhocQueryRecordVO.setDatasetVersionIds(recordDatasetVersionIds);
                if (CollectionUtils.isNotEmpty(recordDatasetVersionIds)) {
                    List<DatasetVersionSimpleDO> datasetVersionSimpleDOList = recordDatasetVersionIds.stream()
                            .map(datasetVersionSimpleDOMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    adhocQueryRecordVO.setDatasetVersionSimpleDOS(datasetVersionSimpleDOList);
                }
            }
            return adhocQueryRecordVO;
        }).collect(Collectors.toList());


        return PageResult.build(pageIndex, pageSize, adhocQueryRecordPage.getTotal(), adhocQueryRecordVOS);
    }

    @Override
    public AdhocQueryRecordVO getQueryRecordDetail(Long adhocQueryRecordId) {
        AdhocQueryRecordDO adhocQueryRecordDO = adhocQueryRecordService.selectById(adhocQueryRecordId);
        if (adhocQueryRecordDO == null) {
            throw new ServiceException("Adhoc query record not found with id: " + adhocQueryRecordId);
        }
        AdhocQueryRecordVO adhocQueryRecordVO = new AdhocQueryRecordVO();
        adhocQueryRecordVO.setAdhocQueryRecordId(adhocQueryRecordDO.getId());
        adhocQueryRecordVO.setAdhocId(adhocQueryRecordDO.getAdhocId());
        adhocQueryRecordVO.setQueryType(adhocQueryRecordDO.getQueryType());
        adhocQueryRecordVO.setDatasetVersionIds(JsonUtil.toList(adhocQueryRecordDO.getDatasetVersionIds(), Long.class));
        adhocQueryRecordVO.setCondition(adhocQueryRecordDO.getCondition());
        adhocQueryRecordVO.setRows(adhocQueryRecordDO.getRows());
        adhocQueryRecordVO.setState(adhocQueryRecordDO.getState());
        adhocQueryRecordVO.setErrorMsg(adhocQueryRecordDO.getErrorMsg());
        adhocQueryRecordVO.setCreator(adhocQueryRecordDO.getCreator());
        adhocQueryRecordVO.setCreateTime(DateUtils.format(adhocQueryRecordDO.getCreateTime(), DateUtils.YYYY_MM_DD_HHMMSS));
        return adhocQueryRecordVO;
    }

    @Override
    public void renameAdhoc(Long adhocId, String name, SimpleUser user) {
        AdhocDO adhocDO = adhocMapper.selectById(adhocId);
        if (adhocDO == null) {
            throw new ServiceException("Adhoc not found with id: " + adhocId);
        }
        if (!adhocDO.getOwnerId().equals(user.getUserId())) {
            throw new ServiceException("无权限修改该Adhoc");
        }
        // 检查名称是否已存在
        checkNameExists(adhocId, name);
        adhocDO.setName(name);
        adhocDO.setModifierId(user.getUserId());
        adhocDO.setModifier(user);
        adhocDO.setUpdateTime(LocalDateTime.now());
        adhocMapper.updateById(adhocDO);
    }

    @Override
    public void queryDownload(Long adhocQueryRecordId, SimpleUser user) {
        AdhocQueryRecordDO adhocQueryRecordDO = adhocQueryRecordService.selectById(adhocQueryRecordId);
        if (adhocQueryRecordDO == null) {
            throw new ServiceException("Adhoc query record not found with id: " + adhocQueryRecordId);
        }
        if (!"success".equals(adhocQueryRecordDO.getState())) {
            throw new ServiceException("查询记录状态异常，无法下载");
        }
        // todo 根据查询记录进行查询下载

    }

    @Override
    public List<AdhocDO> selectByFolderId(Long folderId) {
        LambdaQueryWrapper<AdhocDO> wrapper = Wrappers.<AdhocDO>lambdaQuery()
                .eq(AdhocDO::getFolderId, folderId)
                .eq(AdhocDO::isSave, true);
        return adhocMapper.selectList(wrapper);
    }

    @Override
    public AdhocDO selectById(Long adhocId) {
        return adhocMapper.selectById(adhocId);
    }

    @Override
    public void updateById(AdhocDO adhocDO) {
        adhocMapper.updateById(adhocDO);
    }

    @Override
    public List<AdhocDO> search(String keyword, String userId,List<String> queryTypes) {
        return adhocMapper.search(keyword, userId, queryTypes);
    }

    @Override
    public AdhocQueryProgressVO queryProgress(AdhocQueryProgressRequest request, SimpleUser user) {
        String queryId = request.getQueryId();
        AdhocQueryRecordDO adhocQueryRecordDO = adhocQueryRecordService.selectById(request.getAdhocQueryRecordId());
        if (StringUtils.isEmpty(queryId)) {
            queryId = adhocQueryRecordDO.getQueryId();
        }

        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("Query ID cannot be empty");
        }
        AdhocQueryProgressVO adhocQueryProgressVO = webSearchService.queryProgress(queryId);
        if (CapellaQueryState.unfinishedStatus().contains(adhocQueryRecordDO.getState())) {
            // 如果是未完成状态，直接返回查询进度
            adhocQueryProgressVO.setState(adhocQueryRecordDO.getState());
        }

        Map<String, Object> progressInfo = adhocQueryProgressVO.getProgressInfo();
        if (Objects.nonNull(progressInfo) && !progressInfo.isEmpty()) {
            Optional.ofNullable(progressInfo.get("state")).ifPresent(o -> {
                String info = o.toString();
                // if hive 0(+106)/107 completed(running)/total
                //Map 1: 0(+106)/107\tMap 2: 0(+56)/56\tReducer 3: 0/636\tReducer 4: 0/1
                String[] jobs = info.split("\\t");
                if (jobs.length > 0 && (jobs[0].startsWith("Map") || jobs[0].startsWith("Reducer"))) {
                    for (String job : jobs) {
                        // 忽略 "-/-"
                        if (job.contains("-/-")) {
                            continue;
                        }
                        String[] hive = job.split(":", 2);
                        buildProgress(hive, progressInfo);
                    }
                } else if (jobs.length > 0 && (jobs[0].startsWith("Job Progress"))) {
                    jobs = jobs[0].split(" {2}");
                    for (String job : jobs) {
                        String[] spark = StringUtils.strip(job, "[]").split(":", 2);
                        buildProgress(spark, progressInfo);
                    }
                }
            });
            Optional.ofNullable(progressInfo.get("completedSQL")).ifPresent(o -> {
                String[] sqlInfo = o.toString().split("of");
                int completed = Integer.parseInt(sqlInfo[0].trim());
                int total = Integer.parseInt(sqlInfo[1].trim());
                ProgressDTO sqlProgress = ProgressDTO.builder()
                        .running(total - completed)
                        .completed(completed)
                        .total(total)
                        .build();
                progressInfo.put("completedSQL", sqlProgress);
            });
        }

        return adhocQueryProgressVO;
    }

    @Override
    public AdhocQueryDataVO queryData(AdhocQueryDataRequest request, SimpleUser user) {
        String queryId = request.getQueryId();
        AdhocQueryRecordDO adhocQueryRecordDO = adhocQueryRecordService.selectById(request.getAdhocQueryRecordId());
        if (StringUtils.isEmpty(queryId)) {
            queryId = adhocQueryRecordDO.getQueryId();
        }

        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("Query ID cannot be empty");
        }

        if (!CapellaQueryState.FINISHED.getState().equals(adhocQueryRecordDO.getState())) {
            throw new ServiceException("查询记录状态异常，无法获取数据");
        }

        AdhocQueryDataVO adhocQueryDataVO = webSearchService.queryData(queryId, request.getPageIndex(), request.getPageSize(), user);
        adhocQueryDataVO.setTotal(adhocQueryDataVO.getRows());

        return adhocQueryDataVO;
    }

    @Override
    public AdhocHtmlInfoVO getHtmlInfoData(AdhocHtmlInfoRequest request, SimpleUser user) {
        String queryId = "";
        AdhocQueryRecordDO adhocQueryRecordDO = adhocQueryRecordService.selectById(request.getAdhocQueryRecordId());
        if (StringUtils.isEmpty(queryId)) {
            queryId = adhocQueryRecordDO.getQueryId();
        }
        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("Query ID cannot be empty");
        }
        if (!CapellaQueryState.FINISHED.getState().equals(adhocQueryRecordDO.getState())) {
            throw new ServiceException("查询记录状态异常，无法获取数据");
        }
        AdhocHtmlInfoVO adhocHtmlInfoVO = new AdhocHtmlInfoVO();
        adhocHtmlInfoVO.setAdhocQueryRecordId(request.getAdhocQueryRecordId());
        adhocHtmlInfoVO.setHtmlResult(webSearchService.getHtmlInfoData(queryId, request.getHtmlParams()));
        return adhocHtmlInfoVO;
    }

    @Override
    public Boolean cancelQuery(AdhocCancelQueryRequest request, SimpleUser user) {
        String queryId = "";
        AdhocQueryRecordDO adhocQueryRecordDO = adhocQueryRecordService.selectById(request.getAdhocQueryRecordId());
        if (StringUtils.isEmpty(queryId)) {
            queryId = adhocQueryRecordDO.getQueryId();
        }
        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("Query ID cannot be empty");
        }
        boolean success = webSearchService.cancelQuery(queryId, user);
        if (success){
            adhocQueryRecordDO.setState(CapellaQueryState.CANCELLED.getState());
            adhocQueryRecordService.updateStateById(adhocQueryRecordDO.getId(), adhocQueryRecordDO.getState());
        }

        return success;
    }

    private void checkNameExists(Long adhocId, String name) {
        LambdaQueryWrapper<AdhocDO> wrapper = Wrappers.<AdhocDO>lambdaQuery()
                .eq(AdhocDO::getName, name)
                .ne(AdhocDO::getId, adhocId)
                .last("limit 1");
        Long l = adhocMapper.selectCount(wrapper);
        if (l > 0) {
            throw new ServiceException("临时查询名称已存在: " + name);
        }
    }


    private static void buildProgress(String[] job, Map<String, Object> progressInfo) {
        String key = job[0];
        String[] hiveJobs = job[1].trim().split("/");
        int total = Integer.parseInt(hiveJobs[1]);
        int running = 0;
        int completed = 0;
        int retry = 0;
        try {
            if (StringUtils.isNumeric(hiveJobs[0])) {
                completed = Integer.parseInt(hiveJobs[0]);
            } else {
                Matcher matcher = Pattern.compile(".*?(?=\\()").matcher(hiveJobs[0]); // 左括号前的字符串
                if (matcher.find()) {
                    completed = Integer.parseInt(matcher.group(0));
                }
                String pattern = "\\(\\+([^\\)]+)\\)"; // 括号内字符串
                matcher = Pattern.compile(pattern).matcher(hiveJobs[0]);
                while (matcher.find()) {
                    String find = matcher.group(1);
                    if (find.contains(",")) {
                        String[] strs = find.split(",");
                        running = Integer.parseInt(strs[0]);
                        retry = -Integer.parseInt(strs[1]);
                    } else {
                        int num = Integer.parseInt(matcher.group(1));
                        if (num > 0) {
                            running = num;
                        } else {
                            retry = -num;
                        }
                    }
                }
            }
            ProgressDTO progress = ProgressDTO.builder()
                    .running(running)
                    .completed(completed)
                    .retry(retry)
                    .total(total)
                    .build();
            progressInfo.put(key, progress);
        } catch (Exception e) {
            log.error("Parse progress info error", e);
        }
    }


}
