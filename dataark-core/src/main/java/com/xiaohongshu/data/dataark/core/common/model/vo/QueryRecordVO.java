package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xiaohongshu.data.dataark.dao.handlers.SimpleUserHandler;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryRecordVO {

    /**
     * 查询记录id
     */
    private Long id;

    /**
     * 数据集ID
     */
    private Long datasetId;

    /**
     * 数据集版本
     */
//    private Long datasetVersion;

    /**
     * 数据集版本ID
     */
    private Long datasetVersionId;

    /**
     * 采样任务id
     */
    private Long sampleId;

    /**
     * 采样任务生成路径
     */
    private String samplePath;

    /**
     * 查询类型,preview或sample
     */
    private String queryType;

    /**
     * 查询ID,查询服务生成
     */
    private String queryId;

    /**
     * 查询条件
     */
    private String queryCondition;

    /**
     * 查询状态,待补充枚举值
     */
    private String state;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 提交sql
     */
    private String submitSql;

    /**
     * 执行sql
     */
    private String executedSql;

    /**
     * 执行进度
     */
    private Double progress;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 结果行数
     */
    private Long rows;

    /**
     * 创建人
     */
    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser createBy;

    /**
     * 更新人
     */
    @TableField(typeHandler = SimpleUserHandler.class)
    private SimpleUser updateBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
