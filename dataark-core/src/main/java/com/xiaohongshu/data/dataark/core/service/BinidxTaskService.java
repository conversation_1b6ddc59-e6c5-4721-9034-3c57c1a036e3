package com.xiaohongshu.data.dataark.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;

import java.util.List;

/**
 * <AUTHOR>
 * @email ying<PERSON><EMAIL>
 * @date 2025/4/21
 */
public interface BinidxTaskService {

    int insertRecord(BinidxTaskDO binidxTaskDO);

    IPage<BinidxTaskDO> pageList(Page<BinidxTaskDO> page, Long datasetVersionId);

    List<BinidxTaskDO> selectUnfinishedSampleBinidxTasks(Long datasetVersionId);

    BinidxTaskDO selectById(Long binidxTaskId);

    void updateRecord(BinidxTaskDO binidxTaskDO);

    void deleteById(Long binidxTaskId);

    List<BinidxTaskDO> selectUnfinishedBinidxTasks();

    List<BinidxTaskDO> selectSuccessSampleAllRecord(Long datasetVersionId);
}
