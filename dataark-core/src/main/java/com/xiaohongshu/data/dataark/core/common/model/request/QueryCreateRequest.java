package com.xiaohongshu.data.dataark.core.common.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCreateRequest {
    /**
     * 数据集id
     */
    private Long datasetId;

    /**
     * 数据集版本
     */
//    private String version;

    /**
     * 数据集版本id
     */
    private Long datasetVersionId;

    /**
     * 随机查询的行数，不支持分页
     */
    private Long rows;

    /**
     * 查询类型，preview或sample
     */
    private String queryType;

    /**
     * 查询条件，抽样支持指定查询条件，hive 语法
     */
    private String condition;
}
