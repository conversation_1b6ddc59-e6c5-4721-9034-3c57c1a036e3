package com.xiaohongshu.data.dataark.core.pai;

import com.aliyun.pai_dlc20201203.Client;
import com.aliyun.pai_dlc20201203.models.CreateJobResponse;
import com.aliyun.pai_dlc20201203.models.GetJobRequest;
import com.aliyun.pai_dlc20201203.models.GetJobResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xiaohongshu.data.dataark.core.common.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/16
 */
@Component
public class Sample {

    @Autowired
    private Client client;

    public void test() {
        com.aliyun.pai_dlc20201203.models.CreateJobRequest.CreateJobRequestDataSources dataSources0 = new com.aliyun.pai_dlc20201203.models.CreateJobRequest.CreateJobRequestDataSources()
                .setUri("bmcpfs://cpfs-070u4c0yzkkideju0yp-vpc-eqfmgy.ap-southeast-1.cpfs.aliyuncs.com/")
                .setMountPath("/prodcpfs");
        com.aliyun.pai_dlc20201203.models.CreateJobRequest.CreateJobRequestDataSources dataSources1 = new com.aliyun.pai_dlc20201203.models.CreateJobRequest.CreateJobRequestDataSources()
                .setUri("bmcpfs://cpfs-076w8n5luq3rzig3i44-vpc-0pfehr.ap-southeast-1.cpfs.aliyuncs.com/")
                .setMountPath("/cpfs");
        com.aliyun.pai_dlc20201203.models.ResourceConfig jobSpec0ResourceConfig = new com.aliyun.pai_dlc20201203.models.ResourceConfig()
                .setMemory("80Gi")
                .setSharedMemory("80Gi")
                .setGPU("0")
                .setCPU("16");
        com.aliyun.pai_dlc20201203.models.JobSpec jobSpec0 = new com.aliyun.pai_dlc20201203.models.JobSpec()
                .setResourceConfig(jobSpec0ResourceConfig)
                .setImage("ali-sg-acr-registry-vpc.ap-southeast-1.cr.aliyuncs.com/xhs-llm/pytorch:ngc2408-xiaoming-vllm")
                .setType("Worker")
                .setPodCount(20L);
        com.aliyun.pai_dlc20201203.models.CreateJobRequest createJobRequest = new com.aliyun.pai_dlc20201203.models.CreateJobRequest()
                .setJobSpecs(java.util.Arrays.asList(
                        jobSpec0
                ))
                .setDisplayName("qh_test_binidx_03")
                .setJobType("PyTorchJob")
                .setUserCommand(Constants.SCRIPT)
                .setDataSources(java.util.Arrays.asList(
                        dataSources0,
                        dataSources1
                ))
                .setWorkspaceId("109821")
                .setPriority(6)
                .setResourceId("quotazw00zr1wmuj")
                .setAccessibility("PUBLIC");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        java.util.Map<String, String> headers = new java.util.HashMap<>();
        try {
            // 复制代码运行请自行打印 API 的返回值
            CreateJobResponse jobWithOptions = client.createJobWithOptions(createJobRequest, headers, runtime);
            System.out.println("====== jobId:" + jobWithOptions.getBody().getJobId());
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }
//    dlctkwe880jb1wtp
    public void getJobInfo(String jobId) {
        GetJobRequest getJobRequest = new GetJobRequest();
        RuntimeOptions runtime = new RuntimeOptions();
        Map<String, String> headers = new HashMap<>();
        try {
            // 复制代码运行请自行打印 API 的返回值
            GetJobResponse jobWithOptions = client.getJobWithOptions(jobId, getJobRequest, headers, runtime);
            System.out.println(jobWithOptions.getBody().getStatus());
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }
}
