package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionSimpleDO;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email y<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/10
 */
@Data
public class AdhocQueryRecordVO {

    private Long adhocQueryRecordId;

    private Long adhocId;

    private String queryType;

    private List<Long> datasetVersionIds;

    private List<DatasetVersionSimpleDO> datasetVersionSimpleDOS;

    private String condition;

    private Long rows;

    private String state;

    private String errorMsg;

    private SimpleUser creator;

    private String createTime;

}
