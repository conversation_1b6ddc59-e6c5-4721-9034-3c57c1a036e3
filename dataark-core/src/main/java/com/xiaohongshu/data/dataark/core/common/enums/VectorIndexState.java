package com.xiaohongshu.data.dataark.core.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
@Getter
public enum VectorIndexState {

    NONE("none", "未创建"),
    EMBEDDING("embedding", "向量化中"),
    EMBEDDED("embedded", "向量化完成"),
    EXPORTING("exporting", "导出中"),
    SUCCESS("success", "创建成功"),
    FAILED("failed", "创建失败");

    private final String code;
    private final String desc;

    VectorIndexState(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final List<String> ALLOW_CREATE_INDEX_STATUS = Lists.newArrayList(NONE.getCode(), FAILED.getCode());

}
