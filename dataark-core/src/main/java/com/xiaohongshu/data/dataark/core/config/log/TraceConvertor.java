package com.xiaohongshu.data.dataark.core.config.log;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.xiaohongshu.data.dataark.core.common.constant.LogConstants;

import java.util.Map;

/**
 * 定义 logback 日志规则
 */
public class TraceConvertor extends ClassicConverter {

    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        Map<String,String> map =  iLoggingEvent.getMDCPropertyMap();
        String msg = iLoggingEvent.getFormattedMessage();
        return formatLog(msg, map);
    }

    /**
     * 构建 新增变量的格式
     * @param msg 日志信息
     * @param properties mdc属性
     * @return 日志内容
     */
    private String formatLog(String msg, Map<String,String> properties) {
        StringBuilder sb = new StringBuilder();
        sb.append(msg);
        appendKey(sb, LogConstants.TRACE_ID, properties);
        appendKey(sb, LogConstants.SPAN_ID, properties);
        return sb.toString();
    }

    /**
     * 往StringBuilder尾部新增||key=value
     * @param sb StringBuilder
     * @param key key
     * @param properties MDC属性
     */
    private void appendKey(StringBuilder sb, String key, Map<String,String> properties) {
        String value = properties.get(key);
        if (null != value) {
            sb.append("||").append(key).append("=").append(value);
        }
    }
}
