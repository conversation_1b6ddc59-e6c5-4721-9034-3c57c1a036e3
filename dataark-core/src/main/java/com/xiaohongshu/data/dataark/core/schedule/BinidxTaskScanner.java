package com.xiaohongshu.data.dataark.core.schedule;

import com.aliyun.pai_dlc20201203.models.GetJobResponseBody;
import com.xiaohongshu.data.dataark.core.common.enums.BinidxSceneType;
import com.xiaohongshu.data.dataark.core.common.enums.PaiTaskStatus;
import com.xiaohongshu.data.dataark.core.pai.service.PaiService;
import com.xiaohongshu.data.dataark.core.rpc.crossroad.CrossroadApiCaller;
import com.xiaohongshu.data.dataark.core.service.BinidxTaskService;
import com.xiaohongshu.data.dataark.core.service.common.AlarmService;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Service
@Slf4j
public class BinidxTaskScanner {

    @Resource
    private PaiService paiService;

    @Resource
    private BinidxTaskService binidxTaskService;

    @Resource
    private AlarmService alarmService;

    @Resource
    private CrossroadApiCaller crossroadApiCaller;

    @Resource
    private DatasetInfoMapper datasetInfoMapper;

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @RedSchedule(value = "sync-binidx-task-status", cron = "0 * * * * ?", desc = "同步binidx任务状态", autoFillAppid = true)
    public void syncBinidxTaskStatusJob() {
        try {
            doBiz();
        } catch (Exception e) {
            log.error("syncBinidxTaskStatusJob error: ", e);
            throw e;
        }
    }

    private void doBiz() {
        log.info("syncBinidxTaskStatusJob start");
        // 查询未完成的任务
        List<BinidxTaskDO> unfinishedBinidxTasks = binidxTaskService.selectUnfinishedBinidxTasks();
        if (CollectionUtils.isEmpty(unfinishedBinidxTasks)) {
            log.info("没有未完成的任务");
            return;
        }
        // 查询任务状态
        for (BinidxTaskDO unfinishedBinidxTask : unfinishedBinidxTasks) {
            try {
                GetJobResponseBody binidxJobInfo = paiService.getPaiJobInfo(unfinishedBinidxTask.getJobId());
                if (binidxJobInfo == null) {
                    log.warn("获取任务信息为null，jobId: {}", unfinishedBinidxTask.getJobId());
                    continue;
                }
                String status = binidxJobInfo.getStatus();
                unfinishedBinidxTask.setStatus(status);
                if (PaiTaskStatus.FINISH_STATUS.contains(status)) {
                    // 结束状态设置结束时间
                    unfinishedBinidxTask.setEndTime(LocalDateTime.now());
                    SimpleUser user = JsonUtil.parseObject(unfinishedBinidxTask.getCreator(), SimpleUser.class);
                    // 通知用户，需要申请通知服务号
                    crossroadApiCaller.sendMd2Wechat(
                            Collections.singleton(user.getEmail()),
                            buildMsg(unfinishedBinidxTask));
                }
                binidxTaskService.updateRecord(unfinishedBinidxTask);
            } catch (Exception e) {
                log.error("获取binidx任务状态失败，binidxTaskId: {}, jobId: {}, error: ", unfinishedBinidxTask.getId(), unfinishedBinidxTask.getJobId(), e);
                alarmService.alarmVChat("获取binidx任务状态失败");
            }
        }
        log.info("syncBinidxTaskStatusJob end");
    }

    private String buildMsg(BinidxTaskDO binidxTaskDO) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(binidxTaskDO.getDatasetVersionId());
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        return new StringBuilder("## 【binidx任务执行完成】通知 \n")
                .append("- 数据集: ").append(datasetInfoDO.getName()).append("\n")
                .append("- 数据集版本: ").append(datasetVersionInfoDO.getVersion()).append("\n")
                .append("- 场景: ").append(BinidxSceneType.getCodeOf(binidxTaskDO.getScene()).getDesc()).append("\n")
                .append("- 任务名称: ").append("[").append(binidxTaskDO.getJobName()).append("](").append(binidxTaskDO.getJobUrl()).append(")\n")
                .append("- 任务状态: ").append(PaiTaskStatus.getByCode(binidxTaskDO.getStatus()).getDesc()).append("\n")
                .toString();
    }

}
