package com.xiaohongshu.data.dataark.core.utils;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/11
 */
public class ECDSAUtils {

    private static final String SIGN_ALGORITHM = "SHA256withECDSA";
    private static final String EC_ALGORITHM = "EC";

    public static SimpleUser getUserInfo(String signedUserinfo, String publicKey) throws Exception {
        String[] elements = signedUserinfo.split("\\.");
        String signature = elements[0];
        String userinfoBase64 = elements[1];
        boolean verify = verify(userinfoBase64, signature, publicKey);
        if (!verify) {
            throw new RuntimeException("signature verify failed.");
        }
        String userinfo = new String(Base64.getDecoder().decode(userinfoBase64));
        Map<String, String> result = JSON.parseObject(userinfo, new TypeReference<Map<String, String>>() {
        });
        long exp = Long.parseLong(result.get("exp"));
        if (exp < System.currentTimeMillis()) {
            throw new RuntimeException("userinfo expired.");
        }
        SimpleUser simpleUser = new SimpleUser();
        simpleUser.setUserId(result.get("email").split("@")[0]);
        simpleUser.setDisplayName(result.get("displayName"));
        simpleUser.setEmail(result.get("email"));
        return simpleUser;
    }

    /**
     * 验证数字签名
     *
     * @param data            待验证的数据
     * @param signatureBase64 签名值
     * @param publicKeyBase64 公钥
     * @return 验证是否成功
     */
    public static boolean verify(String data, String signatureBase64, String publicKeyBase64) throws Exception {
        Signature signer = Signature.getInstance(SIGN_ALGORITHM);
        PublicKey publicKey = getPublicKey(publicKeyBase64);
        signer.initVerify(publicKey);
        signer.update(data.getBytes());
        byte[] signatureBytes = Base64.getDecoder().decode(signatureBase64);
        return signer.verify(signatureBytes);
    }

    private static PublicKey getPublicKey(String base64Key)
            throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory keyFactory = KeyFactory.getInstance(EC_ALGORITHM);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(base64Key));
        return keyFactory.generatePublic(keySpec);
    }

    public static void main(String[] args) throws Exception {
        String signedUserinfo = "MEYCIQDN9RIepIn2U+mCUeAIrTN6ZDHBfhXU06j9y+xcgXlL/wIhAODAI4yOPpI050lQqvXSLGlOjLK/+CPrQ45MbZSjCQtP.eyJkaXNwbGF5TmFtZSI6IuiWr+WQjSjlp5PlkI0pIiwidGh1bWJBdmF0YXIiOiJodHRwczovL3dld29yay5xcGljLmNuL3d3cGljM2F6LzE5ODAyN19tZHEtLUlJLVF1ZUVMMndfMTIzNzUzNzE4MS8xMDAiLCJuYW1lIjoi5aeT5ZCNIiwiZW1haWxBbGlhcyI6InRlc3RAeGlhb2hvbmdzaHUuY29tIiwidXNlck5hbWVBbGlhcyI6IuiWr+WQjSIsImF2YXRhciI6Imh0dHBzOi8vd2V3b3JrLnFwaWMuY24vd3dwaWMzYXovMTk4MDI3X21kcS0tSUktUXVlRUwyd18xMjM3NTM3MTgxLzAiLCJleHAiOiIxODU4NTc3MzQwNDQ1IiwidXNlcklkIjoiNjIzZDg0ZTcyMzAwMDAwMDAwMDAwMDA2Iiwibm9uY2UiOiJEY3BTZmJtTiIsImVtYWlsIjoiYWJjQHhpYW9ob25nc2h1LmNvbSJ9";
        String publicKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEfExiI65J9RreJmNC+VSnszO7a6+VIh8dK5KfrIF4I+YDga6be4CL468NjYUWvNFYm9xZsRiIbmiZMcXEOFC6MA==";
        SimpleUser userInfo = getUserInfo(signedUserinfo, publicKey);
        System.out.println(userInfo);
    }

}
