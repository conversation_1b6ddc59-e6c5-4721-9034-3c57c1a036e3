package com.xiaohongshu.data.dataark.core.manager.oa;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.data.dataark.core.manager.oa.pojo.ApproveParam;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc;
import com.xiaohongshu.fls.rpc.finance.workflow.process.req.OaRpcStartProcessReq;
import com.xiaohongshu.fls.rpc.finance.workflow.process.resp.OaRpcProcessInfoResp;
import com.xiaohongshu.fls.rpc.finance.workflow.process.response.OaRpcProcessCurrentInfoResp;
import com.xiaohongshu.infra.rpc.base.Context;
import com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory;
import com.xiaohongshu.infra.rpc.core.registry.ThriftServerAddressProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * @author: longya
 * @since: 2023/9/2 16:42
 * @description:
 */
@Component
@Slf4j
public class OaFlowManager {

    @Resource
    private OaFlowRuntimeProcessRpc.Iface oaFlowRuntimeProcessRpc;

    public OaRpcProcessInfoResp startProcess(String formType, String userId, ApproveParam fields) {
        OaRpcStartProcessReq startProcessReq = new OaRpcStartProcessReq();
        startProcessReq.setFormType(formType);
        startProcessReq.setBusinessId(UUID.randomUUID().toString());
        startProcessReq.setUserId(userId);
        startProcessReq.setJsonData(JSON.toJSONString(fields));
        try {
            OaRpcProcessCurrentInfoResp response = oaFlowRuntimeProcessRpc.startProcess(new Context(), startProcessReq);
            if (response.getResponse().success) {
                OaRpcProcessInfoResp currentInfoResp = response.getProcessCurrentRpcResp();
                log.info("OA申请成功,工单：{}", currentInfoResp.getFormId());
                return currentInfoResp;
            } else {
                log.error("OA申请异常:{}", response.getResponse().msg);
                throw new ServiceException("OA申请异常:" + response.getResponse().msg);
            }
        } catch (TException e) {
            log.error("OA申请异常:", e);
            throw new ServiceException("OA申请异常:" + e.getMessage());
        }
    }

}
