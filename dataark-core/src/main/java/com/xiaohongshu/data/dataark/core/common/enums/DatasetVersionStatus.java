package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum DatasetVersionStatus {

    DRAFT("draft", "草稿"),
    UNVERIFIED("unverified", "待验证"),
    VERIFIED("verified", "已验证"),
    DEPLOYING("deploying", "发布中"),
    DEPLOYED("deployed", "已发布"),
    DEPRECATED("deprecated", "已废弃");

    private String status;
    private String nameCn;

    DatasetVersionStatus(String status, String nameCn) {
        this.status = status;
        this.nameCn = nameCn;
    }

    // 使用 @JsonFormat 标记为对象输出
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", status);
        map.put("nameCn", nameCn);
        return map;
    }

    public static DatasetVersionStatus getByStatus(String status) {
        for (DatasetVersionStatus datasetVersionStatus : values()) {
            if (datasetVersionStatus.getStatus().equals(status)) {
                return datasetVersionStatus;
            }
        }
        return null;
    }
}
