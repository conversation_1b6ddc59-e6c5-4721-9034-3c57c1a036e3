package com.xiaohongshu.data.dataark.core.common.enums;

public enum MediaType {

    TEXT("TEXT", "文本"),
    MULTIMODAL("MULTIMODAL", "多模"),
    ;

    private String code;
    private String nameCn;

    MediaType(String code, String nameCn) {
        this.code = code;
        this.nameCn = nameCn;
    }

    public static MediaType getByNameCn(String mediaType) {
        for (MediaType mediaTypeEnum : MediaType.values()) {
            if (mediaTypeEnum.getNameCn().equals(mediaType)) {
                return mediaTypeEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getNameCn() {
        return nameCn;
    }
}
