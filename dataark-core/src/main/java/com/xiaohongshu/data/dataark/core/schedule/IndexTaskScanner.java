package com.xiaohongshu.data.dataark.core.schedule;

import com.aliyun.pai_dlc20201203.models.GetJobResponseBody;
import com.xiaohongshu.data.dataark.core.common.enums.IndexType;
import com.xiaohongshu.data.dataark.core.common.enums.InvertedIndexState;
import com.xiaohongshu.data.dataark.core.common.enums.PaiTaskStatus;
import com.xiaohongshu.data.dataark.core.common.enums.VectorIndexState;
import com.xiaohongshu.data.dataark.core.pai.service.PaiService;
import com.xiaohongshu.data.dataark.core.service.DatasetVersionInfoService;
import com.xiaohongshu.data.dataark.core.service.IndexTaskService;
import com.xiaohongshu.data.dataark.core.service.common.AlarmService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.IndexTaskDO;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/12
 */
@Service
@Slf4j
public class IndexTaskScanner {

    @Resource
    private PaiService paiService;

    @Resource
    private IndexTaskService indexTaskService;

    @Resource
    private DatasetVersionInfoService datasetVersionInfoService;

    @Resource
    private AlarmService alarmService;

    @RedSchedule(value = "sync-embedding-task-status", cron = "0 0/2 * * * ?", desc = "同步embedding任务状态", autoFillAppid = true)
    @Transactional(rollbackFor = Exception.class)
    public void syncEmbeddingTaskStatusJob() {
        try {
            doBiz();
        } catch (Exception e) {
            log.error("syncEmbeddingTaskStatusJob error: ", e);
            throw e;
        }
    }

    public void invertedTaskHandleFinishStatus(Long datasetVersionId, String paiTaskStatusCode) {
        PaiTaskStatus paiTaskStatus = PaiTaskStatus.getByCode(paiTaskStatusCode);
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(datasetVersionId);
        if (Objects.isNull(datasetVersionInfoDO)) {
            log.warn("datasetVersionInfo不存在了, datasetVersionId:{}", datasetVersionId);
            return;
        }
        if (!InvertedIndexState.WAIT_FOR_SYNC.getCode().equalsIgnoreCase(datasetVersionInfoDO.getInvertedIndexState())){
            log.warn("Inverted task state is not WAIT_FOR_SYNC, datasetVersionId: {}, taskId: {}", datasetVersionId, datasetVersionInfoDO.getInvertedIndexState());
            return;
        }
        InvertedIndexState nextState = PaiTaskStatus.SUCCEEDED.equals(paiTaskStatus) ?
                InvertedIndexState.PENDING : InvertedIndexState.FAILED;

        int affected = datasetVersionInfoService.updateInvertedIndexExportStatusCAS(datasetVersionId, nextState.getCode(), InvertedIndexState.WAIT_FOR_SYNC.getCode());
        if (affected != 1) {
            log.error("倒排索引状态更新行数异常, 更新行数: {}, datasetVersionId: {}", affected, datasetVersionId);
            throw new ServiceException("更新索引导出状态失败");
        }
    }

    public void doBiz() {
        log.info("Start to sync embedding task status");
        List<IndexTaskDO> unfinishedPaiTasks = indexTaskService.selectUnfinishedPaiTasks();
        if (CollectionUtils.isEmpty(unfinishedPaiTasks)) {
            log.info("没有未完成的embedding任务");
            return;
        }
        // 查询任务状态
        for (IndexTaskDO unfinishedPaiTask : unfinishedPaiTasks) {
            try {
                GetJobResponseBody paiJobInfo = paiService.getPaiJobInfo(unfinishedPaiTask.getTaskId());
                if (paiJobInfo == null) {
                    log.warn("获取任务信息为null，jobId: {}", unfinishedPaiTask.getTaskId());
                    continue;
                }
                String status = paiJobInfo.getStatus();
                unfinishedPaiTask.setStatus(status);
                if (PaiTaskStatus.FINISH_STATUS.contains(status)) {
                    // 结束状态设置结束时间
                    unfinishedPaiTask.setEndTime(LocalDateTime.now());

                    String indexType = unfinishedPaiTask.getIndexType();
                    if (IndexType.INVERTED.getCode().equalsIgnoreCase(indexType)) {
                        // 更新倒排任务状态
                        invertedTaskHandleFinishStatus(unfinishedPaiTask.getDatasetVersionId(), status);
                    }
                    // 更新向量任务状态
                    else if (PaiTaskStatus.SUCCEEDED.getCode().equals(status)) {
                        // 成功的任务，设置embedding状态为已完成
                        datasetVersionInfoService.updateVectorEmbeddingSuccess(unfinishedPaiTask.getDatasetVersionId(),
                                VectorIndexState.EMBEDDED.getCode(), VectorIndexState.EMBEDDING.getCode());
                        log.info("Embedding task succeeded, datasetVersionId: {}, jobId: {}", unfinishedPaiTask.getDatasetVersionId(), unfinishedPaiTask.getTaskId());
                    } else {
                        // 失败或停止的任务，设置embedding状态为未完成
                        String errorMsg = "embedding task " + status + ", indexTaskId: " + unfinishedPaiTask.getId();
                        datasetVersionInfoService.updateVectorEmbeddingFailed(unfinishedPaiTask.getDatasetVersionId(),
                                VectorIndexState.FAILED.getCode(), errorMsg, VectorIndexState.EMBEDDING.getCode());
                        log.warn("Embedding task failed or stopped, datasetVersionId: {}, jobId: {}", unfinishedPaiTask.getDatasetVersionId(), unfinishedPaiTask.getTaskId());
                    }
                }
                unfinishedPaiTask.setUpdateTime(LocalDateTime.now());
                indexTaskService.updateById(unfinishedPaiTask);
            } catch (Exception e) {
                log.error("获取embedding任务状态失败，indexTaskId: {}, jobId: {}, error: ", unfinishedPaiTask.getId(), unfinishedPaiTask.getTaskId(), e);
                alarmService.alarmVChat("获取embedding任务状态失败, indexTaskId:" + unfinishedPaiTask.getId());
            }
        }
        log.info("Finish syncing embedding task status");
    }

}
