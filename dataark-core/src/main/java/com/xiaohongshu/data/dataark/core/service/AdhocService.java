package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.data.dataark.dao.entity.AdhocDO;
import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
public interface AdhocService {
    PageResult<DatasetVersionDetailVO> getDatasetVersionList(String queryType, String name, Integer version, String mediaType,
                                                             String contentType, String label, String userId, Integer pageIndex, Integer pageSize);

    Long createAdhoc(SimpleUser user);

    void deleteAdhoc(Long adhocId, SimpleUser user);

    void saveAdhoc(AdhocRequest request, SimpleUser user);

    AdhocVO getAdhocDetail(Long adhocId);

    AdhocQueryResultVO query(AdhocQueryRequest request, SimpleUser user);

    PageResult<AdhocQueryRecordVO> getQueryRecords(Long adhocId,String folderType, Integer pageIndex, Integer pageSize);

    AdhocQueryRecordVO getQueryRecordDetail(Long adhocQueryRecordId);

    void renameAdhoc(Long adhocId, String name, SimpleUser user);

    void queryDownload(Long adhocQueryRecordId, SimpleUser user);

    List<AdhocDO> selectByFolderId(Long folderId);

    AdhocDO selectById(Long adhocId);

    void updateById(AdhocDO adhocDO);

    List<AdhocDO> search(String keyword, String userId, List<String> queryTypes);

    AdhocQueryProgressVO queryProgress(AdhocQueryProgressRequest request, SimpleUser user);

    AdhocQueryDataVO queryData(AdhocQueryDataRequest request, SimpleUser user);

    AdhocHtmlInfoVO getHtmlInfoData(AdhocHtmlInfoRequest request, SimpleUser user);

    Boolean cancelQuery(AdhocCancelQueryRequest request, SimpleUser user);
}
