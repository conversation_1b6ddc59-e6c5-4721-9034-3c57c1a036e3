package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum Region {
    CN_SHANGHAI("cn-shanghai","阿里云", "阿里云上海", "lsh-oss-gpt-spam"),
    AP_SOUTHEAST_1("ap-southeast-1", "阿里云","阿里云新加坡", "lsg-oss-chatgpt-agi-hcfs"),
    CN_SHANGHAI_1("cn-shanghai", "阿里云","阿里云上海", "lsh-oss-gpt-spam");

    private String region;
    private String cloud;
    private String displayName;
    private String bucket;

    Region(String region, String cloud, String displayName, String bucket) {
        this.region = region;
        this.cloud = cloud;
        this.displayName = displayName;
        this.bucket = bucket;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("region", region);
        map.put("cloud", cloud);
        map.put("displayName", displayName);
        map.put("bucket", bucket);
        return map;
    }

    public static Region fromBucket(String bucket) {
        for (Region region : Region.values()) {
            if (region.getBucket().equals(bucket)) {
                return region;
            }
        }
        throw new IllegalArgumentException("不支持的桶路径: " + bucket);
    }

    public static Region fromRegion(String region){
        for (Region value : Region.values()) {
            if (value.getRegion().equalsIgnoreCase(region)) {
                return value;
            }
        }
        throw new IllegalArgumentException("不支持的region: " + region);
    }

}
