package com.xiaohongshu.data.dataark.core.service.impl;

import com.red.data.dg.shaded.fastjson2.JSONObject;
import com.red.data.dg.shaded.fastjson2.JSONWriter;
import com.xiaohongshu.data.capella.spi.QuerySPI;
import com.xiaohongshu.data.dataark.core.common.enums.Region;
import com.xiaohongshu.data.dataark.core.common.model.dto.AdhocHtmlParamsDTO;
import com.xiaohongshu.data.dataark.core.common.model.request.AdhocQueryRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryDataVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryProgressVO;
import com.xiaohongshu.data.dataark.core.config.RedisCache;
import com.xiaohongshu.data.dataark.core.rpc.capella.CapellaApiCaller;
import com.xiaohongshu.data.dataark.core.service.WebSearchService;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2025/7/11 17:02
 */

@Service
@Slf4j
public class WebSearchServiceImpl implements WebSearchService {

    @Resource
    private QueryServiceImpl queryService;

    @Resource
    private CapellaApiCaller capellaApiCaller;

    @Resource
    private RedisCache redisCache;

    @Value("${web.search.sql:select * from iceberg_ali.llm_ods.iceberg_cc_pipeline_url_filter WHERE %s limit %d;}")
    private String webSearchSql;

    @Value("${kyuubi.session.engine.share.level:USER}")
    private String kyuubiEngineShareLevel;

    @Override
    public String query(AdhocQueryRequest request, SimpleUser loginUser) {
        if (StringUtils.isEmpty(request.getCondition())) {
            String errMsg = "页面数据查询条件不能为空";
            log.error(errMsg);
            throw new ServiceException(errMsg);
        }
        String sql = String.format(webSearchSql, request.getCondition(), request.getRows());
        Map<String, Object> conf = new HashMap<>();
        conf.put("spark.kyuubi.engine.share.level", kyuubiEngineShareLevel);
        conf.put("spark.kyuubi.engine.share.level.user2connection.convert", false);
        return queryService.submitCapellaQuery(sql, false, loginUser, "platform=dataark",
                "spark", Region.CN_SHANGHAI_1.getRegion(), conf);
    }

    @Override
    public AdhocQueryProgressVO queryProgress(String queryId) {
        if (StringUtils.isEmpty(queryId)) {
            String errMsg = "capella进度查询参数queryId不能为空";
            log.error(errMsg);
            throw new ServiceException(errMsg);
        }
        try {
            QuerySPI.ProgressResp resp = capellaApiCaller.queryProgress(queryId);
            AdhocQueryProgressVO adhocQueryProgressVO = JSONObject.parseObject(JSONObject.toJSONString(resp), AdhocQueryProgressVO.class);
            adhocQueryProgressVO.setQueryId(queryId);
            log.info("capella进度查询接口调用成功, adhocQueryProgressVO: {}",JSONObject.toJSONString(adhocQueryProgressVO));
            return adhocQueryProgressVO;
        } catch (Exception e) {
            String errMsg = "capella进度查询接口调用失败:" + queryId;
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
    }

    @Override
    public AdhocQueryDataVO queryData(String queryId, Integer pageIndex, Integer pageSize, SimpleUser user) {
        String queryDataResultStr = redisCache.getWebDataResult(queryId);
        if (StringUtils.isEmpty(queryDataResultStr)) {
            log.warn("查询ID: {} 的数据结果不存在", queryId);
            throw new ServiceException("查询ID: " + queryId + " 的数据结果不存在，请重新查询");
        }

        AdhocQueryDataVO adhocQueryDataVO = JSONObject.parseObject(queryDataResultStr, AdhocQueryDataVO.class);
        int total = adhocQueryDataVO.getRows();
        int startIndex = (pageIndex - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);
        List<Map<String, Object>> data = new ArrayList<>();
        for (int index = startIndex; index <= endIndex; index++) {
            String dataStr = redisCache.getWebData(queryId, index);
            if (StringUtils.isEmpty(dataStr)) {
                log.warn("查询ID: {} 的数据索引: {} 不存在", queryId, index);
                continue;
            }
            Map<String, Object> row = JSONObject.parseObject(dataStr, Map.class);
            row.put("htmlParams", new AdhocHtmlParamsDTO(index));
            row.remove("html");
            data.add(row);
        }
        adhocQueryDataVO.setData(data);
        return adhocQueryDataVO;
    }

    @Override
    public AdhocQueryDataVO queryData(String queryId) {
        if (StringUtils.isEmpty(queryId)) {
            String errMsg = "capella进度查询参数queryId不能为空";
            log.error(errMsg);
            throw new ServiceException(errMsg);
        }
        try {
            QuerySPI.AsyncQueryResult asyncQueryResult = capellaApiCaller.queryResult(queryId);
            // https://github.com/alibaba/fastjson2/issues/1773
            AdhocQueryDataVO adhocQueryDataVO = JSONObject.parseObject(JSONObject.toJSONString(asyncQueryResult, JSONWriter.Feature.LargeObject), AdhocQueryDataVO.class);

            for (int i = 0; i < adhocQueryDataVO.getRows(); i++) {
                Map<String, Object> row = adhocQueryDataVO.getData().get(i);
                row.put("htmlParams", new AdhocHtmlParamsDTO(i));
            }

            return adhocQueryDataVO;
        } catch (Exception e) {
            String errMsg = "capella获取查询结果接口调用失败";
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
    }

    @Override
    public String getHtmlInfoData(String queryId, AdhocHtmlParamsDTO htmlParams) {
        String dataStr = redisCache.getWebData(queryId, htmlParams.getDataIndex());
        if (StringUtils.isEmpty(dataStr)) {
            log.warn("查询ID: {} 的数据索引: {} 不存在", queryId, htmlParams.getDataIndex());
            throw new ServiceException("查询ID: " + queryId + " 的数据索引: "
                    + htmlParams.getDataIndex() + " 不存在，请重新查询");
        }
        log.info("查询ID: {} 的数据索引: {} 的数据内容: {}", queryId, htmlParams.getDataIndex(), dataStr);
        Map<String, Object> row = JSONObject.parseObject(dataStr, Map.class);
        if (row.containsKey("html")) {
            return row.get("html").toString();
        }
        String errMsg = "查询ID: " + queryId + " 的数据索引: " + htmlParams.getDataIndex() + " 不包含html信息";
        log.error(errMsg);
        throw new ServiceException(errMsg);
    }

    @Override
    public Boolean cancelQuery(String queryId, SimpleUser user) {
        try {
            log.info("开始取消页面查询, queryId: {}", queryId);
            QuerySPI.CancelRequest cancelRequest = new QuerySPI.CancelRequest(queryId);
            capellaApiCaller.cancel(cancelRequest);
        } catch (Exception e) {
            String errMsg = "capella 取消查询接口调用失败：" + e.getMessage();
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
        return true;
    }


}
