package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryResultVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.CalculateTokenListVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.InvertedIndexExportVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.VectorEmbeddingExportVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/23
 */
public interface OpenApiService {
    List<CalculateTokenListVO> getCnShCalculateTokenList();

    List<CalculateTokenListVO> getSgpShCalculateTokenList();

    Integer lockCalculateToken(LockCalculateTokenRequest request);

    void saveTokenResult(SaveTokenResultRequest request);

    VectorEmbeddingExportVO getVectorEmbeddingExportTask();

    Integer lockVectorExport(LockIndexExportRequest request);

    void saveVectorResult(SaveIndexResultRequest request);

    InvertedIndexExportVO getInvertedIndexExportTask();

    Integer lockInvertedIndexExport(LockIndexExportRequest request);

    void saveInvertedIndexResult(SaveIndexResultRequest request);

    void initDataset(MultipartFile file);

    AdhocQueryResultVO adhocQuery(AdhocQueryRequest request);
}
