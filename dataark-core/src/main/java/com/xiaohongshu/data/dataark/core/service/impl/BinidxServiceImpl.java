package com.xiaohongshu.data.dataark.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.core.common.constant.Constants;
import com.xiaohongshu.data.dataark.core.common.enums.*;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxGenerateScriptRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxPublishTaskRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxSampleTaskRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxTaskConf;
import com.xiaohongshu.data.dataark.core.common.model.vo.BinidxTaskListVO;
import com.xiaohongshu.data.dataark.core.config.apollo.ApolloCommonConfig;
import com.xiaohongshu.data.dataark.core.manager.oa.OaEmployeeManager;
import com.xiaohongshu.data.dataark.core.manager.oa.OaFlowManager;
import com.xiaohongshu.data.dataark.core.manager.oa.pojo.DatasetPublishApproveParam;
import com.xiaohongshu.data.dataark.core.manager.oa.pojo.OaDatasourceParam;
import com.xiaohongshu.data.dataark.core.pai.config.PaiApolloConfig;
import com.xiaohongshu.data.dataark.core.pai.pojo.RegionConfig;
import com.xiaohongshu.data.dataark.core.pai.req.CreatePaiJobReq;
import com.xiaohongshu.data.dataark.core.pai.service.PaiService;
import com.xiaohongshu.data.dataark.core.service.ApproveRecordService;
import com.xiaohongshu.data.dataark.core.service.BinidxService;
import com.xiaohongshu.data.dataark.core.service.BinidxTaskService;
import com.xiaohongshu.data.dataark.core.service.common.AlarmService;
import com.xiaohongshu.data.dataark.core.utils.DateUtils;
import com.xiaohongshu.data.dataark.dao.entity.*;
import com.xiaohongshu.data.dataark.dao.enums.DataSourceType;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.QueryRecordMapper;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/18
 */
@Service
@Slf4j
public class BinidxServiceImpl implements BinidxService {

    @Resource
    private ApolloCommonConfig apolloCommonConfig;

    @Resource
    private PaiApolloConfig paiApolloConfig;

    @Resource
    private PaiService paiService;

    @Resource
    private OaEmployeeManager oaEmployeeManager;

    @Resource
    private OaFlowManager oaFlowManager;

    @Resource
    private ApproveRecordService approveRecordService;

    @Resource
    private BinidxTaskService binidxTaskService;

    @Resource
    private AlarmService alarmService;

    @Resource
    private DatasetInfoMapper datasetInfoMapper;

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Resource
    private QueryRecordMapper queryRecordMapper;

    public static final String BINIDX_SAMPLE_TASK_NAME_PREFIX = "binidx_dataark_sample_";
    public static final String BINIDX_PUBLISH_TASK_NAME_PREFIX = "binidx_dataark_publish_";

    @Override
    public List<String> getCpfsType() {
        return Arrays.stream(CpfsType.values())
                .map(CpfsType::name)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, RegionConfig> getPaiResourceConfig() {
        return paiApolloConfig.getPaiRegionConfig();
    }

    @Override
    public String generateScript(BinidxGenerateScriptRequest req) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(req.getDatasetVersionId());
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        if (Objects.isNull(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        String datasetCacheName = datasetInfoDO.getCode() + "_" + datasetVersionInfoDO.getVersion() + "_" + System.currentTimeMillis();
        // 根据场景获取路径
        String inputPath;
        if (BinidxSceneType.SAMPLE.getCode().equals(req.getScene()) && SampleType.SAMPLE.getCode().equals(req.getSampleType())) {
            // 获取采样路径
            if (Objects.isNull(req.getSampleId())) {
                throw new ServiceException("采样数据打包，采样ID不能为空");
            }
            QueryRecordDO sampleRecord = queryRecordMapper.selectById(req.getSampleId());
            if (Objects.isNull(sampleRecord)) {
                throw new ServiceException("采样数据不存在");
            }
            inputPath = sampleRecord.getSamplePath();
        } else {
            inputPath = datasetVersionInfoDO.getOssPath();
        }
        // 去掉输入路径的桶前缀
        inputPath = paiService.normalizeOssPath(inputPath);
        return paiService.generateUserCommand(datasetCacheName, inputPath, req.getCpfsType(), apolloCommonConfig.getEnv(),
                req.getScene(), datasetVersionInfoDO.getRegion(), datasetVersionInfoDO.getFileType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String sampleGenerateTask(BinidxSampleTaskRequest binidxSampleTaskRequest, SimpleUser user) {
        // 采样任务最多一个去执行
        Long datasetVersionId = binidxSampleTaskRequest.getDatasetVersionId();
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(datasetVersionId);
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        if (Objects.isNull(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        String jobName = BINIDX_SAMPLE_TASK_NAME_PREFIX + datasetInfoDO.getCode() + "_" + datasetVersionInfoDO.getVersion() + "_" + System.currentTimeMillis();
        BinidxTaskDO sampleBinidxTaskDO = binidxSampleTaskRequest.toBinidxTaskDO();
        BinidxTaskConf binidxTaskConf = binidxSampleTaskRequest.getBinidxTaskConf();
        sampleBinidxTaskDO.setCreator(JsonUtil.toString(user));
        // 生成脚本和路径
        String datasetCacheName = datasetInfoDO.getCode() + "_" + datasetVersionInfoDO.getVersion() + "_" + System.currentTimeMillis();
        // 根据场景获取路径
        String inputPath;
        if (SampleType.SAMPLE.getCode().equals(binidxSampleTaskRequest.getSampleType())) {
            // 获取采样路径
            if (Objects.isNull(binidxSampleTaskRequest.getSampleId())) {
                throw new ServiceException("采样数据打包，采样ID不能为空");
            }
            QueryRecordDO sampleRecord = queryRecordMapper.selectById(binidxSampleTaskRequest.getSampleId());
            if (Objects.isNull(sampleRecord)) {
                throw new ServiceException("采样数据不存在");
            }
            inputPath = sampleRecord.getSamplePath();
        } else {
            inputPath = datasetVersionInfoDO.getOssPath();
        }
        // 生成的文件地址
        String outputPath = generateOutputPath(binidxTaskConf.getCpfsType(), BinidxSceneType.SAMPLE.getCode(), datasetCacheName);
        sampleBinidxTaskDO.setOutputPath(outputPath);
        // 生成执行脚本
        String script = generateScript(datasetCacheName, inputPath, datasetVersionInfoDO, binidxTaskConf.getCpfsType(), BinidxSceneType.SAMPLE.getCode());
        binidxTaskConf.setScript(script);
        // 生成CreatePaiJobReq
        CreatePaiJobReq createPaiJobReq = CreatePaiJobReq.generateReq(jobName, paiApolloConfig.getImageUrl(binidxTaskConf.getRegion())
                , paiApolloConfig.getWorkSpaceConfig(binidxTaskConf.getRegion(), binidxTaskConf.getWorkSpaceId()), binidxTaskConf);
        String binidxJobId = paiService.createPaiJob(createPaiJobReq);
        sampleBinidxTaskDO.setJobId(binidxJobId);
        sampleBinidxTaskDO.setJobName(jobName);
        sampleBinidxTaskDO.setJobUrl(generatePaiUrl(binidxTaskConf.getRegion(), binidxTaskConf.getWorkSpaceId(), binidxJobId));
        sampleBinidxTaskDO.setStatus(PaiTaskStatus.CREATING.getCode());
        sampleBinidxTaskDO.setStartTime(LocalDateTime.now());
        // 保存任务信息
        binidxTaskService.insertRecord(sampleBinidxTaskDO);
        return binidxJobId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String publishGenerateTask(BinidxPublishTaskRequest req, SimpleUser user) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(req.getDatasetVersionId());
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new RuntimeException("数据集版本不存在");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        if (Objects.isNull(datasetInfoDO)) {
            throw new RuntimeException("数据集不存在");
        }
        // 判断数据集是否处于已验证状态
        if (!DatasetVersionStatus.UNVERIFIED.getStatus().equals(datasetVersionInfoDO.getStatus())) {
            throw new RuntimeException("只有待验证状态的数据集可以发布");
        }
        datasetVersionInfoDO.setDataSources(JSONObject.toJSONString(req.getDataSources()));
        datasetVersionInfoDO.setProcessingTasks(req.getProcessingTasks());
        datasetVersionInfoDO.setVerificationReport(req.getVerificationReport());
        datasetVersionInfoDO.setStatus(DatasetVersionStatus.DEPLOYING.getStatus());
        datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
        Long binidxTaskDOId = null;
        if (req.getGenerateBinidx()) {
            BinidxTaskDO publishBinidxTaskDO = req.toBinidxTaskDO();
            publishBinidxTaskDO.setCreator(JsonUtil.toString(user));
            // 生成BinidxTaskDO记录
            binidxTaskService.insertRecord(publishBinidxTaskDO);
            binidxTaskDOId = publishBinidxTaskDO.getId();
        }
        // 生成OA工单所需参数
        Map<String, String> emailToUserId = oaEmployeeManager.emailToUserId(Collections.singleton(user.getEmail()));
        List<OaDatasourceParam> oaDatasourceParams = req.getDataSources().stream().map(it -> {
            OaDatasourceParam oaDatasourceParam = new OaDatasourceParam();
            if (DataSourceType.platform.equals(it.getType())) {
                oaDatasourceParam.setSourceType("平台内");
                DatasetVersionInfoDO sourceDatasetVersionInfoDO = datasetVersionInfoMapper.selectById(it.getDatasetVersionId());
                DatasetInfoDO dataset = datasetInfoMapper.selectById(sourceDatasetVersionInfoDO.getDatasetId());
                oaDatasourceParam.setDisplayName(dataset.getName() + "_" + sourceDatasetVersionInfoDO.getVersion());
                // 后续拼上url
                oaDatasourceParam.setUrl(generateDatasetVersionInfoUrl(sourceDatasetVersionInfoDO.getDatasetId(), sourceDatasetVersionInfoDO.getId()));
            } else {
                oaDatasourceParam.setSourceType("平台外");
                oaDatasourceParam.setDisplayName(it.getSourceUrl());
                oaDatasourceParam.setUrl(it.getSourceUrl());
            }
            oaDatasourceParam.setDescription(it.getSourceDescription());
            return oaDatasourceParam;
        }).collect(Collectors.toList());
        DatasetPublishApproveParam param = DatasetPublishApproveParam.builder()
                .datasetNameVersion(datasetInfoDO.getName() + "_" + datasetVersionInfoDO.getVersion())
                .datasetUrl(generateDatasetVersionInfoUrl(datasetInfoDO.getId(), datasetVersionInfoDO.getId()))
                .description(datasetInfoDO.getDescription())
                .versionDescription(datasetVersionInfoDO.getDescription())
                .owner(datasetInfoDO.getOwner().getDisplayName())
                .approveRemark(req.getApprovalRemark())
                .verificationReport(req.getVerificationReport())
                .dataSources(oaDatasourceParams)
                .processingTasks(req.getProcessingTasks())
                .build();
        String formId = oaFlowManager.startProcess(OaFormType.DATASET_PUBLISH_BINIDX.getTicketCode(), emailToUserId.get(user.getEmail()), param).getFormId();
        // 写入数据库
        approveRecordService.insertRecord(approveRecordService.buildPublishBinidxRecord(formId, OaFormType.DATASET_PUBLISH_BINIDX, datasetVersionInfoDO.getId(), binidxTaskDOId, user));
        // 把发布填的信息更新到dataset_version
        datasetVersionInfoMapper.updateById(datasetVersionInfoDO);
        return formId;
    }


    @Override
    public PageResult<BinidxTaskListVO> list(Long datasetVersionId, Integer pageIndex, Integer pageSize) {
        // 分页查询binidx任务信息
        Page<BinidxTaskDO> page = new Page<>(pageIndex, pageSize);
        IPage<BinidxTaskDO> binidxTaskDOIPage = binidxTaskService.pageList(page, datasetVersionId);
        // 转换为BinidxTaskListVO
        List<BinidxTaskListVO> binidxTaskListVOList = binidxTaskDOIPage.getRecords().stream()
                .map(it -> {
                    BinidxTaskListVO binidxTaskListVO = new BinidxTaskListVO();
                    binidxTaskListVO.setId(it.getId());
                    binidxTaskListVO.setJobId(it.getJobId());
                    binidxTaskListVO.setJobName(it.getJobName());
                    binidxTaskListVO.setScene(it.getScene());
                    binidxTaskListVO.setCreator(JsonUtil.parseObject(it.getCreator(), SimpleUser.class));
                    binidxTaskListVO.setStatus(it.getStatus());
                    binidxTaskListVO.setStatusDisplay(PaiTaskStatus.getStatusDisplay(it.getStatus()));
                    if (Objects.nonNull(it.getStartTime())) {
                        binidxTaskListVO.setStartTime(DateUtils.format(it.getStartTime(), DateUtils.YYYY_MM_DD_HHMM));
                    }
                    if (Objects.nonNull(it.getEndTime())) {
                        binidxTaskListVO.setEndTime(DateUtils.format(it.getEndTime(), DateUtils.YYYY_MM_DD_HHMM));
                    }
                    binidxTaskListVO.setPaiUrl(it.getJobUrl());
                    binidxTaskListVO.setOutputPath(it.getOutputPath());
                    return binidxTaskListVO;
                })
                .collect(Collectors.toList());
        return PageResult.build(pageIndex, pageSize, binidxTaskDOIPage.getTotal(), binidxTaskListVOList);
    }

    @Override
    public List<BinidxTaskDO> searchSampleAllRecord(Long datasetVersionId) {
        // 查询数据集全量采样执行成功的纪录
        List<BinidxTaskDO> successSampleAllRecords = binidxTaskService.selectSuccessSampleAllRecord(datasetVersionId);
        return successSampleAllRecords.stream().filter(it -> StringUtils.isNotEmpty(it.getOutputPath())).collect(Collectors.toList());
    }

    @Override
    public void approvePass(ApproveRecordDO recordDO) {
        DatasetVersionInfoDO datasetVersionInfo = datasetVersionInfoMapper.selectById(recordDO.getDatasetVersionId());
        Long binidxTaskId = recordDO.getBinidxTaskId();
        if (Objects.isNull(binidxTaskId)) {
            // 不生成binidx作业，只需要把状态改成已发布即可
            // 将数据集状态改为已发布
            datasetVersionInfo.setStatus(DatasetVersionStatus.DEPLOYED.getStatus());
            datasetVersionInfo.setUpdateTime(LocalDateTime.now());
            datasetVersionInfoMapper.updateById(datasetVersionInfo);
            return;
        }
        // 生成binidx作业
        BinidxTaskDO publishBinidxTaskDO = binidxTaskService.selectById(binidxTaskId);
        if (Objects.isNull(publishBinidxTaskDO)) {
            // 不可能出现
            log.error("binidx任务不存在，binidxTaskId: {}", binidxTaskId);
            throw new ServiceException("binidx任务不存在");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfo.getDatasetId());
        //1.创建binidx作业
        String jobName = BINIDX_PUBLISH_TASK_NAME_PREFIX + datasetInfoDO.getCode() + "_" + datasetVersionInfo.getVersion() + "_" + System.currentTimeMillis();
        // 生成CreatePaiJobReq
        BinidxTaskConf binidxTaskConf = JsonUtil.parseObject(publishBinidxTaskDO.getJobConf(), BinidxTaskConf.class);
        // 根据场景获取路径
        String inputPath = datasetVersionInfo.getOssPath();
        // 生成执行脚本
        String script;
        String outputPath;
        if (Objects.isNull(binidxTaskConf.getBinidxTaskId())) {
            String datasetCacheName = datasetInfoDO.getCode() + "_" + datasetVersionInfo.getVersion() + "_" + System.currentTimeMillis();
            outputPath = generateOutputPath(binidxTaskConf.getCpfsType(), BinidxSceneType.PUBLISH.getCode(), datasetCacheName);
            script = generateScript(datasetCacheName, inputPath, datasetVersionInfo, binidxTaskConf.getCpfsType(), BinidxSceneType.PUBLISH.getCode());
        } else {
            // cp
            BinidxTaskDO binidxTaskDO = binidxTaskService.selectById(binidxTaskConf.getBinidxTaskId());
            if (Objects.isNull(binidxTaskDO)) {
                throw new ServiceException("binidx任务不存在");
            }
            String source = binidxTaskDO.getOutputPath();
            // 获取source的最后一个/后面的内容
            if (source.endsWith("/")) {
                source = source.substring(0, source.length() - 1);
            }
            String[] split = source.split("/");
            String cacheName = split[split.length - 1];
            outputPath = generatePublishCPOutputPath(binidxTaskConf.getCpfsType(), BinidxSceneType.PUBLISH.getCode(), cacheName);
            script = generateCpScript(source, binidxTaskConf.getCpfsType(), BinidxSceneType.PUBLISH.getCode());
        }
        binidxTaskConf.setScript(script);
        publishBinidxTaskDO.setOutputPath(outputPath);
        publishBinidxTaskDO.setJobConf(JsonUtil.toString(binidxTaskConf));
        CreatePaiJobReq createPaiJobReq = CreatePaiJobReq.generateReq(jobName, paiApolloConfig.getImageUrl(binidxTaskConf.getRegion())
                , paiApolloConfig.getWorkSpaceConfig(binidxTaskConf.getRegion(), binidxTaskConf.getWorkSpaceId()), binidxTaskConf);
        try {
            String binidxJobId = paiService.createPaiJob(createPaiJobReq);
            publishBinidxTaskDO.setJobId(binidxJobId);
            publishBinidxTaskDO.setJobName(jobName);
            publishBinidxTaskDO.setJobUrl(generatePaiUrl(binidxTaskConf.getRegion(), binidxTaskConf.getWorkSpaceId(), binidxJobId));
            publishBinidxTaskDO.setStatus(PaiTaskStatus.CREATING.getCode());
            publishBinidxTaskDO.setStartTime(LocalDateTime.now());
            // 更新任务信息
            binidxTaskService.updateRecord(publishBinidxTaskDO);
            // 将数据集状态改为已发布
            datasetVersionInfo.setStatus(DatasetVersionStatus.DEPLOYED.getStatus());
            datasetVersionInfo.setPublishOutputPath(outputPath);
            datasetVersionInfo.setUpdateTime(LocalDateTime.now());
            datasetVersionInfoMapper.updateById(datasetVersionInfo);
        } catch (Exception e) {
            log.error("binidx任务创建失败，binidxTaskId: {}", binidxTaskId, e);
            // 将数据集状态改为待验证
            datasetVersionInfo.setStatus(DatasetVersionStatus.UNVERIFIED.getStatus());
            datasetVersionInfo.setUpdateTime(LocalDateTime.now());
            datasetVersionInfoMapper.updateById(datasetVersionInfo);
            // 不删除binidx_task记录，保留失败信息，后面手动删除
            alarmService.alarmVChat("binidx任务创建失败，binidxTaskId: " + binidxTaskId + ", 错误信息: " + e.getMessage());
        }
    }

    @Override
    public void approveRefuse(ApproveRecordDO recordDO) {
        //1. 将数据集状态从发布中改为待验证
        DatasetVersionInfoDO datasetVersionInfo = datasetVersionInfoMapper.selectById(recordDO.getDatasetVersionId());
        datasetVersionInfo.setStatus(DatasetVersionStatus.UNVERIFIED.getStatus());
        datasetVersionInfo.setUpdateTime(LocalDateTime.now());
        datasetVersionInfoMapper.updateById(datasetVersionInfo);
        //2. 删除binidx任务记录
        Long binidxTaskId = recordDO.getBinidxTaskId();
        if (Objects.nonNull(binidxTaskId)) {
            binidxTaskService.deleteById(binidxTaskId);
        }
    }

    public String generatePaiUrl(String region, String workSpaceId, String jobId) {
        return Constants.PAI_SGP_URL
                .replace("${workspaceId}", workSpaceId)
                .replace("${jobId}", jobId);
    }

    public String generateDatasetVersionInfoUrl(Long datasetId, Long datasetVersionId) {
        return apolloCommonConfig.getDataarkDomain() + "/dataark/datasetDetails?datasetId=" + datasetId + "&datasetVersionId=" + datasetVersionId;
    }

    public String generateOutputPath(String cpfsType, String scene, String cacheName) {
        return String.format("/%s/data/dataark/%s/%s/%s_qwen_with_st", cpfsType, apolloCommonConfig.getEnv(), scene, cacheName);
    }

    public String generatePublishCPOutputPath(String cpfsType, String scene, String cacheName) {
        return String.format("/%s/data/dataark/%s/%s/%s", cpfsType, apolloCommonConfig.getEnv(), scene, cacheName);
    }

    public String generateScript(String datasetCacheName, String inputPath, DatasetVersionInfoDO datasetVersionInfoDO, String cpfsType, String scene) {
        // 去掉输入路径的桶前缀
        inputPath = paiService.normalizeOssPath(inputPath);
        return paiService.generateUserCommand(datasetCacheName, inputPath, cpfsType, apolloCommonConfig.getEnv(),
                scene, datasetVersionInfoDO.getRegion(), datasetVersionInfoDO.getFileType());
    }

    public String generateCpScript(String sourcePath, String cpfsType, String scene) {
        return paiService.generateCpCommand(sourcePath, cpfsType, apolloCommonConfig.getEnv(), scene);
    }
}
