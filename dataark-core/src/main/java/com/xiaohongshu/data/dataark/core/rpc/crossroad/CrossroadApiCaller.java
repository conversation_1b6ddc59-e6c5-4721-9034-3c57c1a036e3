package com.xiaohongshu.data.dataark.core.rpc.crossroad;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaohongshu.dataverse.common.alert.RequestSupport;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2/28/23
 */
@Slf4j
@Component
public class CrossroadApiCaller extends RequestSupport {

    @Value("${api.crossroad.domain}")
    private String domain;

    @Value("${api.crossroad.token}")
    private String token;

    @Value("${alarm.vc.robot:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52c12fb9-81dc-4645-8f16-3500fcb02c78}")
    private String alarmUrl;

    @Autowired
    private OkHttpClient httpClient;

    /**
     * "DataArk平台通知" 的AgentID
     */
    private final static int AGENT_ID = 1000645;

    public void sendMd2Wechat(Collection<String> emails, String markdown) throws Exception {
        if (CollectionUtils.isEmpty(emails)) {
            throw new RuntimeException("user emails cannot be empty!");
        }
        String url = String.format("%s/crossroad/api/v1/invoke/QYWX02001", domain);
        String body = new JSONObject().fluentPut("touser", String.join("|", emails))
                .fluentPut("msgtype", "markdown")
                .fluentPut("agentid", AGENT_ID)
                .fluentPut("markdown", new JSONObject().fluentPut("content", markdown))
                .toJSONString();
        Request request = new Request.Builder().addHeader("token", token)
                .url(url)
                .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body))
                .build();
        Response response = withRetry3(() -> httpClient.newCall(request).execute());
        if (response.isSuccessful()) {
            String resp = Objects.requireNonNull(response.body()).string();
            JSONObject data = JSONObject.parseObject(resp);
            int errcode = data.getIntValue("errorCode");
            if (errcode != 0) {
                throw new Exception("crossroad request failed, response:" + data);
            }
        } else {
            throw new Exception("crossroad request failed, " + response);
        }
    }

    /**
     * 发送markdown消息到企微机器人
     *
     * @param robotHook 机器人hook地址
     * @param markdown  md content
     * @throws IOException 调用失败
     */
    public void sendMarkdownToRobot(String robotHook, String markdown) throws Exception {
        log.info("send robot : {} ", robotHook);
        if (StringUtils.isEmpty(robotHook)) {
            throw new RuntimeException("robotHook Url cannot be empty!");
        }
        String body = new JSONObject().fluentPut("msgtype", "markdown")
                .fluentPut("markdown", new JSONObject().fluentPut("content", markdown))
                .toJSONString();
        Request request = new Request.Builder()
                .url(robotHook)
                .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body))
                .build();
        String resp = withRetry3(() -> Objects.requireNonNull(httpClient.newCall(request).execute().body()).string());
        JSONObject r = JSON.parseObject(resp);
        if (r.getIntValue("errcode") != 0) {
            log.error("Markdown推送机器人失败: {} - {}", robotHook, markdown);
            throw new RuntimeException("Markdown推送机器人失败: " + r.getIntValue("errcode") + ":"
                    + r.getString("errmsg"));
        }
    }

    public void alarmRobot(String content) throws Exception {
        sendMarkdownToRobot(alarmUrl, content);
    }
}
