package com.xiaohongshu.data.dataark.core.config.apollo;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * apollo的通用配置项
 */
@Slf4j
@Data
@Configuration
public class ApolloCommonConfig {

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${pai.allow:true}")
    private boolean realPai;

    @Value("${dataark.domain:}")
    private String dataarkDomain;

    @Value("${allow.visit:false}")
    private Boolean allowVisit;

    @Value("${allow.visit.user:}")
    private List<String> allowVisitUser = Lists.newArrayList();

    @Value("${dataark.admin:}")
    private List<String> dataarkAdmin = Lists.newArrayList();

    @Value("${third_party.oa.url:}")
    private String oaUrl;

    @Value("${skip.register.check:false}")
    private boolean skipRegisterCheck;

}
