package com.xiaohongshu.data.dataark.core.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/9
 */
@Getter
public enum AdhocQueryType {

    PRECISE("precise", "精确查询"),
    INVERTED("inverted", "倒排索引"),
    VECTOR("vector", "向量索引"),
    WEB("web", "页面查询");

    private final String code;
    private final String desc;

    AdhocQueryType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdhocQueryType fromCode(String code) {
        for (AdhocQueryType type : AdhocQueryType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown AdhocQueryType code: " + code);
    }

    public static final List<String> INVERTED_QUERY_TYPES = Lists.newArrayList(INVERTED.getCode(), PRECISE.getCode());

    public static final List<String> VECTOR_QUERY_TYPES = Lists.newArrayList(VECTOR.getCode());


    public static List<String> getQueryTypes(String folderType) {
        FolderType type = FolderType.valueOf(folderType.toLowerCase());
        switch (type) {
            case adhoc:
                return  Lists.newArrayList(INVERTED.getCode(), PRECISE.getCode(),VECTOR.getCode());
            case web:
                return Lists.newArrayList(WEB.getCode());
        }
        throw new IllegalArgumentException("Unknown FolderType: " + folderType);
    }
}
