package com.xiaohongshu.data.dataark.core.client;

import com.alibaba.fastjson.JSONObject;
import com.xiaohongshu.data.dataark.core.common.model.dto.RedCityRestResultDTO;
import com.xiaohongshu.data.dataark.core.utils.HttpUtils;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/8/13 17:47
 */

@Service
@Slf4j
public class RedCityDataArkClient {


    @Value("${redCity.domain:}")
    private String redCityDomain;

    @Value("${redCity.dataark.appId:}")
    private String appId;

    @Value("${redCity.dataark.asnId:}")
    private String asnId;

    @Value("${redCity.dataark.appSecret:}")
    private String appSecret;

    @Value("${redCity.dataark.accessToken:}")
    private String accessToken;


    public void sendMarkdownToUserChat(String email, String markdown) {
        sendMarkdown(markdown, Collections.singletonList(email));
    }

    public boolean sendMarkdown(String markdown, List<String> accountEmailList) {
        return this.sendMessageToPersons(markdown, 10, accountEmailList);
    }

    public boolean sendMessageToPersons(String messageContent, int messageType, List<String> accountEmailList) {
        Map<String, Object> params = new HashMap();
        params.put("asnId", this.asnId);
        params.put("messageContent", messageContent);
        params.put("messageType", messageType);
        params.put("accountList", accountEmailList);
        params.put("businessId", System.currentTimeMillis());
        return this.call("redcity:asn.asnSendMessageToPerson:v1", params);
    }

    private boolean call(String apiAlias, Map<String, Object> bizParams) {
        Map<String, Object> apiParams = new HashMap();
        apiParams.put("apiAlias", apiAlias);
        apiParams.put("bizParams", JsonUtil.toString(bizParams));
        apiParams.put("appId", this.appId);
        apiParams.put("appAccessToken", this.createAppAccessToken());
        apiParams.put("userAccessToken", this.accessToken);

        try {
            String redCityRestResultDTOStr = post(this.redCityDomain + "/openapis/open/api/call", apiParams);
            return JSONObject.parseObject(redCityRestResultDTOStr, RedCityRestResultDTO.class).isSuccess();
        } catch (Exception var5) {
            var5.printStackTrace();
            System.out.println("Error calling RedCity API: " + var5.getMessage());
            log.error("Error calling RedCity API: {}", var5.getMessage(), var5);
            return false;
        }
    }

    public String createAppAccessToken() {
        Map<String, Object> params = new HashMap();
        params.put("appId", appId);
        params.put("appSecret", appSecret);
        return post(this.redCityDomain + "/openapis/open/token/createAppAccessToken", params);
    }

    public String post(String url , Map<String, Object> params){
        String requestBody = JsonUtil.toString(params);
        try {
            String result = HttpUtils.send(new Request.Builder().url(url)
                    .post(RequestBody.create(HttpUtils.JSON_TYPE, requestBody))
                    .build());
            log.info("POST request to {} with body {} returned: {}", url, requestBody, result);
            return result;
        } catch (Throwable e) {
            log.error("Error sending POST request to {}: {}", url, e.getMessage(), e);
            throw new RuntimeException("Error sending POST request to " + url, e);
        }
    }

}
