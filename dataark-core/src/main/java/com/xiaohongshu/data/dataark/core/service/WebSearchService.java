package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.dto.AdhocHtmlParamsDTO;
import com.xiaohongshu.data.dataark.core.common.model.request.AdhocQueryRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryDataVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryProgressVO;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

/**
 * <AUTHOR>
 * @Date 2025/7/11 17:02
 */

public interface WebSearchService {
    String query(AdhocQueryRequest request, SimpleUser loginUser);

    AdhocQueryProgressVO queryProgress(String queryId);

    AdhocQueryDataVO queryData(String queryId, Integer pageIndex, Integer pageSize, SimpleUser user);

    AdhocQueryDataVO queryData(String queryId);

    String getHtmlInfoData(String queryId, AdhocHtmlParamsDTO htmlParams);

    Boolean cancelQuery(String queryId, SimpleUser user);
}
