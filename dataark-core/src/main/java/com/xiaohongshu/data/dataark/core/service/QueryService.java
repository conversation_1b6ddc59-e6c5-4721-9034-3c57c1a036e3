package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.QueryCreateRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.QueryRecordVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.QueryResultVO;
import com.xiaohongshu.data.dataark.core.common.model.request.SampleQueryCreateRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.RegisterCheckVO;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

import java.util.List;

public interface QueryService {

    Long createQueryRecord(QueryCreateRequest request, SimpleUser loginUser);

    Long updateQueryStatus(Long queryRecordId);

    QueryRecordVO getQueryRecord(Long id);

    QueryRecordVO getLatestQueryRecord(Long datasetVersionId);

    QueryRecordVO getLatestSampleQueryRecord(Long sampleId);

    QueryResultVO getQueryResult(Long id);

    RegisterCheckVO registerCheck(Long id, Long datasetVersionId);

    List<QueryRecordVO> getSampleRecordList(Long datasetVersionId);

    // 用于调试
//    String submitCapellaQuery(Long queryRecordId, SimpleUser loginUser);

    Long createSampleQuery(SampleQueryCreateRequest request, SimpleUser loginUser);

    Long sampleDownload(SampleQueryCreateRequest request, SimpleUser loginUser);

    void cancelSample(SampleQueryCreateRequest request, SimpleUser loginUser);
}
