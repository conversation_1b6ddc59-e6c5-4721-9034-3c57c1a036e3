//package com.xiaohongshu.data.dataark.core.milvus.config;
//
//import io.milvus.v2.client.ConnectConfig;
//import io.milvus.v2.client.MilvusClientV2;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//
///**
// * <AUTHOR>
// * @email <EMAIL>
// * @date 2025/6/13
// */
//@Configuration
////@Profile(value = {"prod", "staging"})
//public class MilvusConfig {
//
//    @Value("${third_party.milvus.uri:}")
//    private String milvusUri;
//
//    @Value("${third_party.milvus.token:}")
//    private String milvusToken;
//
//    @Value("${third_party.milvus.database:}")
//    private String milvusDatabase;
//
//    @Bean
//    public MilvusClientV2 milvusClient() {
//        ConnectConfig connectConfig = ConnectConfig.builder()
//                .uri(milvusUri)
//                .token(milvusToken)
//                .dbName(milvusDatabase)
//                .build();
//        return new MilvusClientV2(connectConfig);
//    }
//
//}
