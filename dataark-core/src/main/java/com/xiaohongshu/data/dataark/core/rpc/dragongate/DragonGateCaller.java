package com.xiaohongshu.data.dataark.core.rpc.dragongate;

import com.red.data.dg.client.ClientEnv;
import com.red.data.dg.client.DragonGateClient;
import com.red.data.dg.common.EngineType;
import com.red.data.dg.common.QueryRegistration;
import com.red.data.dg.response.QueryProgressResponse;
import com.red.data.dg.shaded.fastjson2.JSON;
import com.red.data.dg.shaded.fastjson2.TypeReference;
import com.red.data.dg.shaded.okhttp3.Credentials;
import com.red.data.dg.shaded.okhttp3.OkHttpClient;
import com.red.data.dg.shaded.okhttp3.Request;
import com.xiaohongshu.data.dataark.core.common.model.dragongate.QueryProgress;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class DragonGateCaller {

    private static final String DEFAULT_CLUSTER_TAG = "Data:Dataverse-V2:ali2";
//    private static final String DEFAULT_CLUSTER_TAG = "Data:Dataverse-Test:ali2:1";

    private static final String PLATFORM = "dataark-" + System.getProperty("spring.profiles.active");

    private static final String PLATFORM_ACCOUNT = "dragon-dataark";

    private static final String AUTH_CREDENTIAL = "iChM2YLTfENOOs/xq8dJvQ";

    private static final String AUTH_REQUIRED_PREFIX = "Authorization required: ";

    private static final Integer HIVE_PRIORITY = 40;

    private static final Integer SYNC_TIMES_OUT = 10 * 60;

    private DragonGateClient dragonGateClient;

    @PostConstruct
    public void init() {
        String env = System.getProperty("spring.profiles.active");
        ClientEnv clientEnv = ClientEnv.BETA;
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .addInterceptor(chain -> {
                    String credential = Credentials.basic(PLATFORM_ACCOUNT, AUTH_CREDENTIAL);
                    Request authenticatedRequest = chain.request().newBuilder()
                            .header("Authorization", credential)
                            .build();
                    return chain.proceed(authenticatedRequest);
                })
                .connectTimeout(Duration.ofSeconds(300))
                .writeTimeout(Duration.ofSeconds(300))
                .callTimeout(Duration.ofSeconds(300))
                .readTimeout(Duration.ofSeconds(300))
                .build();
        dragonGateClient = new DragonGateClient(httpClient, clientEnv);
        log.info("init dragon client, env: {}, clientEnv: {}", env, clientEnv);
    }

    public String runSqlAsync(EngineType engineType, String sql, String userEmail, int priority,
                              boolean download, Map<String, String> conf) {

        QueryRegistration queryRegistration = QueryRegistration.builder()
                .platform(PLATFORM)
                .clusterTag(DEFAULT_CLUSTER_TAG)
                .attemptClusterTag(DEFAULT_CLUSTER_TAG)
                .engineType(engineType)
                .query(sql)
                .download(download)
                .queryPriority(priority)
                .asynchronous(true)
                .proxyUserEmail(userEmail)
                .user(PLATFORM_ACCOUNT)
                .credential(AUTH_CREDENTIAL)
                .userDefinedProperties(conf)
                .build();
        log.info("runSqlAsync, queryRegistration: {}", JSON.toJSONString(queryRegistration));
        String queryId = dragonGateClient.runQuery(queryRegistration).getQueryId();
        log.info("runSqlAsync, queryId: {}", queryId);
        return queryId;
    }

    /**
     * 获取查询进度
     * @param queryId
     * @return
     * @throws Exception
     */
    public QueryProgress queryProgress(String queryId) {
        QueryProgressResponse queryProgressResponse = dragonGateClient.getQueryProgress(queryId);
        QueryProgress queryProgress = new QueryProgress();
        if (StringUtils.isNotBlank(queryProgressResponse.getError())) {
            Matcher matcher = Pattern.compile(AUTH_REQUIRED_PREFIX + "(\\{.*\\})").matcher(queryProgressResponse.getError());
            if (matcher.find()) {
                String authError = matcher.group(0);
                Map<String, Object> authErrorMap = JSON.parseObject(
                        authError.substring(AUTH_REQUIRED_PREFIX.length()), new TypeReference< Map<String, Object>>(){});
                queryProgress.setProgressInfo(authErrorMap);
                queryProgress.setRequireAuth(true);
            }
        } else {
            queryProgress.setProgressInfo(queryProgressResponse.getProgressInfo());
            queryProgress.setProgress(queryProgressResponse.getProgress());
        }
        queryProgress.setState(queryProgressResponse.getState().toString());
        String errorMessage = queryProgressResponse.getError();
        queryProgress.setError(errorMessage);
        queryProgress.setQueryId(queryId);
        queryProgress.setExecutedSql(queryProgressResponse.getExecutedSql());
        log.info("queryProgress, queryId: {}, queryProgressResponse: {}, queryProgress: {}",
                queryId, JSON.toJSONString(queryProgress), JSON.toJSONString(queryProgressResponse));
        if (queryProgress.errorState()) {
            dragonGateClient.closeSession(queryId);
        }
        return queryProgress;
    }

}
