package com.xiaohongshu.data.dataark.core.common.model.vo;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DatasetInfoVO {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 数据集code，英文名，用于生成cpfs目录
     */
    private String code;

    /**
     * 数据集描述
     */
    private String description;

    /**
     * 数据集类型,文本或多模
     */
    private String mediaType;

    /**
     * 数据集内容类型,文本,图片,音频,视频等
     */
    private String contentType;

    /**
     * 是否为原始数据集
     */
    private Boolean isRaw;

    private Long maxVersionId;

    private SimpleUser createBy;

    private SimpleUser updateBy;

    private SimpleUser owner;

    private LocalDateTime createTime;

}
