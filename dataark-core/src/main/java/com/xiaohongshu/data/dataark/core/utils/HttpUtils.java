package com.xiaohongshu.data.dataark.core.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class HttpUtils {
    private static final RestTemplate REST_TEMPLATE = new RestTemplateBuilder()
            .detectRequestFactory(true)
            .setReadTimeout(Duration.ofSeconds(20))
            .build();

    public static JSONObject getJson(String url, Map<String, ?> params) {
        return REST_TEMPLATE.getForObject(url, JSONObject.class, params);
    }


    private static final OkHttpClient OKHTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(120))
            .writeTimeout(Duration.ofSeconds(200))
            .readTimeout(Duration.ofSeconds(200))
            .build();

    public static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    public static String send(Request request) throws IOException {
        try (Response response = OKHTTP_CLIENT.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return Objects.requireNonNull(response.body()).string();
            } else {
                try {
                    // 有可能状态不成功的时候 body 有内容
                    throw new IOException("Unexpected " + response + " body: " + response.body().string());
                } catch (NullPointerException e) {
                    throw new IOException("Unexpected " + response);
                }
            }
        }
    }
}
