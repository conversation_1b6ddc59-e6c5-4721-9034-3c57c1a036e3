package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.DatasetChangeOwnerRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetCommitRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetDeleteRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetVersionIndexRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
public interface DatasetService {

    List<String> contentTypesNameCn(String mediaType);

    List<String> getLanguages();

    List<Map<String, Object>> getStatus();

    List<Map<String, Object>> getRegions();

    DatasetWarpVO createDataset(DatasetWarpVO datasetWarpVO, SimpleUser loginUser);

    Long commitDataset(DatasetCommitRequest commitRequest, SimpleUser loginUser);

    DatasetVersionInfoVO createVersion(DatasetVersionInfoVO datasetVersionInfoVO, SimpleUser loginUser);

    DatasetVersionInfoVO updateDatasetVersionOutput(DatasetVersionInfoVO datasetVersionInfoVO, SimpleUser loginUser);

    Long updateDatasetBasicInfo(DatasetInfoVO datasetInfoVO, SimpleUser loginUser);

    DatasetInfoVO getDatasetBasicInfo(Long id, SimpleUser loginUser);

    Long updateDatasetOwner(DatasetChangeOwnerRequest request, SimpleUser loginUser);

    void deleteDataset(DatasetDeleteRequest request, SimpleUser loginUser);

    Boolean datasetDeletable(Long id, SimpleUser loginUser);

    Boolean cancelRegister(Long datasetVersionId);

    void datasetVersionDeprecate(Long datasetVersionId, SimpleUser simpleUser);

    PageResult<DatasetWrapFlattenVO> flattenDatasetList(String name, Long id, String userId,
                                                        String mediaType, String contentType,
                                                        String language, String status, Boolean isMine,
                                                        Integer pageIndex, Integer pageSize,
                                                        SimpleUser loginUser);

    DatasetVersionsWrapVO getDatasetVersions(Long datasetId, SimpleUser loginUser);

    DatasetVersionsWrapVO getDatasetVersionDetail(Long datasetVersionId);

    Map<String, Object> getPathRegion(String path);

    List<String> getDatasetVersionLabels();

    void createIndex(DatasetVersionIndexRequest request, SimpleUser simpleUser);

    void deleteIndex(DatasetVersionIndexRequest request, SimpleUser simpleUser);

    List<DatasetVersionDetailVO> datasetVersionList(List<Long> datasetVersionIds);
}
