package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.xiaohongshu.data.dataark.core.common.enums.AdhocQueryType;
import com.xiaohongshu.data.dataark.core.common.enums.FolderType;
import com.xiaohongshu.data.dataark.core.common.model.request.FolderRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.MoveRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.FolderChildrenVO;
import com.xiaohongshu.data.dataark.core.service.AdhocService;
import com.xiaohongshu.data.dataark.core.service.FolderService;
import com.xiaohongshu.data.dataark.dao.entity.AdhocDO;
import com.xiaohongshu.data.dataark.dao.entity.FolderDO;
import com.xiaohongshu.data.dataark.dao.mapper.FolderMapper;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.xiaohongshu.data.dataark.core.common.enums.AdhocQueryType.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
@Service
public class FolderServiceImpl implements FolderService {

    @Resource
    private FolderMapper folderMapper;

    @Resource
    private AdhocService adhocService;

    private static final int MAX_LEVEL_LIMIT = 1;

    @Override
    public Long create(FolderRequest request, SimpleUser user) {
        Long parentId = request.getParentId();
        String name = request.getName();
        List<FolderDO> folders = Lists.newArrayList();
        recursiveParentPath(parentId, folders);
        if (CollectionUtils.isNotEmpty(folders)) {
            throw new ServiceException("目录层级超过最大限制：" + MAX_LEVEL_LIMIT);
        }
        checkDuplicateFolderName(parentId, name);
        FolderDO folder = convertCreateFolder(request, user);
        folderMapper.insert(folder);
        return folder.getId();
    }

    @Override
    public void rename(Long folderId, String name, SimpleUser user) {
        FolderDO folderDO = checkAndGetFolder(folderId);
        checkDuplicateFolderName(folderDO.getParentId(), name);
        folderDO.setName(name);
        folderDO.setModifier(user);
        folderDO.setModifierId(user.getUserId());
        folderDO.setUpdateTime(LocalDateTime.now());
        folderMapper.updateById(folderDO);
    }

    @Override
    public void delete(Long folderId, SimpleUser user) {
        // 校验当前目录下是否存在子目录/任务/函数库等
        FolderDO folderDO = checkAndGetFolder(folderId);
        if (folderDO.getParentId() == null) {
            throw new ServiceException("根目录不允许删除");
        }
        if (!folderDO.getCreatorId().equals(user.getUserId())) {
            throw new ServiceException("非创建者不允许删除目录");
        }
        // 当前目录下是否有子目录
        List<FolderDO> childFolders = selectByParentId(folderId);
        // 根据类型查看对应表中是否有文件
        List<AdhocDO> files = adhocService.selectByFolderId(folderId);
        if (CollectionUtils.isNotEmpty(childFolders) || CollectionUtils.isNotEmpty(files)) {
            throw new IllegalArgumentException("非空目录不允许删除");
        }
        folderMapper.deleteById(folderId);
    }

    @Override
    public void move(MoveRequest request, SimpleUser user) {
        Long folderId = request.getParentId();

        FolderDO folderDO = folderMapper.selectById(folderId);
        if (Objects.isNull(folderDO)){
            throw new ServiceException("目录不存在，id：" + folderId);
        }
        if (StringUtils.isEmpty(folderDO.getFolderType()) || !folderDO.getFolderType().equalsIgnoreCase(request.getFolderType())){
            throw new ServiceException("目录类型不匹配");
        }

        AdhocDO adhocDO = adhocService.selectById(request.getId());

        if (Objects.isNull(adhocDO)) {
            throw new ServiceException("adhoc不存在，id：" + request.getId());
        }

        if (FolderType.adhoc.name().equalsIgnoreCase(folderDO.getFolderType())) {
            // 预训练数据集查询
            Set<String> datasetQueryTypes = new HashSet<String>() {{
                add(PRECISE.getCode());
                add(INVERTED.getCode());
                add(VECTOR.getCode());
            }};
            if (!datasetQueryTypes.contains(adhocDO.getQueryType())) {
                throw new ServiceException("目录类型和查询类型不匹配");
            }
        } else if (FolderType.web.name().equalsIgnoreCase(folderDO.getFolderType())) {
            // 网页库查询
            if (!WEB.getCode().equalsIgnoreCase(adhocDO.getQueryType())) {
                throw new ServiceException("目录类型和查询类型不匹配");
            }
        } else {
            throw new ServiceException("目录类型不支持");
        }

        if (!adhocDO.getCreatorId().equals(user.getUserId())) {
            throw new ServiceException("非创建者不允许移动adhoc");
        }
        adhocDO.setFolderId(folderId);
        adhocDO.setModifier(user);
        adhocDO.setModifierId(user.getUserId());
        adhocDO.setUpdateTime(LocalDateTime.now());
        adhocService.updateById(adhocDO);
    }

    @Override
    public FolderChildrenVO show(String folderType, boolean showFile, boolean own, String keyword, SimpleUser user) {
        List<FolderDO> folderDOS = selectAllFolderByType(folderType);
        // 获取根目录
        Optional<FolderDO> rootOption = folderDOS.stream().filter(it -> it.getParentId() == null).findFirst();
        if (!rootOption.isPresent()) {
            throw new ServiceException("根目录不存在，请先创建根目录");
        }
        FolderDO root = rootOption.get();
        FolderChildrenVO rootNode = FolderChildrenVO.createFolderNode(root);
        if (!showFile) {
            // 仅展示目录
            buildChildren(folderDOS, rootNode, false);
            return rootNode;
        }
        // 展示目录下的文件
        if (StringUtils.isNotEmpty(keyword) || own) {
            // 需要搜索
            String userId = "";
            if (own) {
                userId = user.getUserId();
            }
            List<String> queryTypes = AdhocQueryType.getQueryTypes(folderType);
            List<AdhocDO> adhocDOS = adhocService.search(keyword, userId, queryTypes);
            if (CollectionUtils.isEmpty(adhocDOS)) {
                return rootNode;
            }
            // 获取目录下的文件
            Map<Long, List<AdhocDO>> adhocMap = adhocDOS.stream().collect(Collectors.groupingBy(AdhocDO::getFolderId));
            // 获取目录
            List<Long> folderIds = adhocDOS.stream().map(AdhocDO::getFolderId).filter(it -> !Objects.equals(it, rootNode.getId()))
                    .distinct().collect(Collectors.toList());
            List<FolderChildrenVO> folderChildrenVOS = selectByFolderIds(folderIds).stream().map(FolderChildrenVO::createFolderNode).collect(Collectors.toList());
            buildSearchChildren(rootNode, folderChildrenVOS, adhocMap);
            return rootNode;
        } else {
            // 不需要搜索，直接构建目录树
            buildChildren(folderDOS, rootNode, true);
            return rootNode;
        }
    }

    private void buildSearchChildren(FolderChildrenVO rootNode, List<FolderChildrenVO> folderChildrenVOS, Map<Long, List<AdhocDO>> adhocMap) {
        // 给根目录绑定children
        List<FolderChildrenVO> rootChildren = new ArrayList<>();
        List<AdhocDO> adhocDOS = adhocMap.get(rootNode.getId());
        if (CollectionUtils.isNotEmpty(adhocDOS)) {
            List<FolderChildrenVO> adhocNodes = adhocDOS.stream()
                    .map(FolderChildrenVO::createAdhocNode)
                    .collect(Collectors.toList());
            rootChildren.addAll(adhocNodes);
        }
        for (FolderChildrenVO folderChildrenVO : folderChildrenVOS) {
            List<FolderChildrenVO> children = new ArrayList<>();
            List<AdhocDO> childAdhocDOS = adhocMap.get(folderChildrenVO.getId());
            if (CollectionUtils.isNotEmpty(childAdhocDOS)) {
                List<FolderChildrenVO> adhocNodes = childAdhocDOS.stream()
                        .map(FolderChildrenVO::createAdhocNode)
                        .collect(Collectors.toList());
                children.addAll(adhocNodes);
            }
            folderChildrenVO.setChildren(children);
        }
        rootChildren.addAll(folderChildrenVOS);
        rootNode.setChildren(rootChildren);
    }

    private void buildChildren(List<FolderDO> folderDOS, FolderChildrenVO fatherNode, boolean showFile) {
        List<FolderChildrenVO> children = new ArrayList<>();
        // 查询当前目录下的文件
        if (showFile) {
            List<AdhocDO> adhocDOS = adhocService.selectByFolderId(fatherNode.getId());
            if (CollectionUtils.isNotEmpty(adhocDOS)) {
                List<FolderChildrenVO> fileNodes = adhocDOS.stream()
                        .map(FolderChildrenVO::createAdhocNode)
                        .collect(Collectors.toList());
                children.addAll(fileNodes);
            }
        }
        // 查询当前目录下的子目录
        folderDOS.stream().filter(folderDO -> Objects.equals(folderDO.getParentId(), fatherNode.getId()))
                .forEach(folderDO -> {
                    FolderChildrenVO childNode = FolderChildrenVO.createFolderNode(folderDO);
                    // 递归查询子目录
                    buildChildren(folderDOS, childNode, showFile);
                    children.add(childNode);
                });
        fatherNode.setChildren(children);
    }

    private List<FolderDO> selectAllFolderByType(String folderType) {
        LambdaQueryWrapper<FolderDO> wrapper = Wrappers.<FolderDO>lambdaQuery()
                .eq(FolderDO::getFolderType, folderType);
        return folderMapper.selectList(wrapper);
    }

    private FolderDO convertCreateFolder(FolderRequest request, SimpleUser user) {
        FolderDO folderDO = new FolderDO();
        folderDO.setName(request.getName());
        folderDO.setParentId(request.getParentId());
        folderDO.setFolderType(request.getFolderType());
        folderDO.setCreatorId(user.getUserId());
        folderDO.setCreator(user);
        return folderDO;
    }

    private void checkDuplicateFolderName(Long parentId, String name) {
        Optional.ofNullable(selectByParentId(parentId)).ifPresent(folders -> {
            boolean match = folders.stream().anyMatch(i -> i.getName().equals(name));
            if (match) {
                throw new IllegalArgumentException(String.format("当前位置已存在子目录: %s", name));
            }
        });
    }

    /**
     * 向上递归目录，不包含根节点
     *
     * @param id
     * @param folders
     */
    private void recursiveParentPath(Long id, List<FolderDO> folders) {
        FolderDO folder = checkAndGetFolder(id);
        Long parentId = folder.getParentId();
        if (Objects.isNull(parentId)) {
            return;
        }
        folders.add(folder);
        recursiveParentPath(parentId, folders);
    }

    private FolderDO checkAndGetFolder(Long id) {
        FolderDO folder = folderMapper.selectById(id);
        if (folder == null) {
            throw new ServiceException("目录不存在，id：" + id);
        }
        return folder;
    }

    public List<FolderDO> selectByParentId(Long parentId) {
        LambdaQueryWrapper<FolderDO> wrapper = Wrappers.<FolderDO>lambdaQuery()
                .eq(FolderDO::getParentId, parentId);
        return folderMapper.selectList(wrapper);
    }

    public List<FolderDO> selectByFolderIds(List<Long> folderIds) {
        if (CollectionUtils.isEmpty(folderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FolderDO> wrapper = Wrappers.<FolderDO>lambdaQuery()
                .in(FolderDO::getId, folderIds);
        return folderMapper.selectList(wrapper);
    }
}
