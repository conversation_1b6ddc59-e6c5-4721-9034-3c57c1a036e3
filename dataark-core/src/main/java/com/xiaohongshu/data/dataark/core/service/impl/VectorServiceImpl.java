package com.xiaohongshu.data.dataark.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.*;
import com.xiaohongshu.data.dataark.core.common.model.request.AdhocQueryRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.VectorResultVO;
import com.xiaohongshu.data.dataark.core.milvus.service.MilvusService;
import com.xiaohongshu.data.dataark.core.rpc.embedding.EmbeddingCaller;
import com.xiaohongshu.data.dataark.core.service.VectorService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import io.milvus.v2.service.vector.response.SearchResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/13
 */
@Service
public class VectorServiceImpl implements VectorService {

    private static final Logger log = LoggerFactory.getLogger(VectorServiceImpl.class);
    @Resource
    private MilvusService milvusService;

    @Resource
    private EmbeddingCaller embeddingCaller;

    @Override
    public List<VectorResultVO> query(AdhocQueryRequest request, List<DatasetVersionInfoDO> datasetVersionInfoDOS) {
        List<String> collectNames = datasetVersionInfoDOS.stream().map(DatasetVersionInfoDO::getVectorDatasetVersionName).distinct().collect(Collectors.toList());
        List<Float> queryVector = embeddingCaller.getEmbedding(request.getCondition().trim());
        List<SearchResp.SearchResult> results = milvusService.search(collectNames, queryVector, request.getRows());
        return results.stream().map(it -> {
            VectorResultVO vectorResultVO = new VectorResultVO();
            vectorResultVO.setDocid(it.getEntity().get("docid").toString());
            vectorResultVO.setText(it.getEntity().get("text").toString());
            vectorResultVO.setScore(it.getScore());
            JsonPrimitive meta = (JsonPrimitive) it.getEntity().get("meta");
            JSONObject jsonObject = new JSONObject();
            // 先将JsonPrimitive转换为字符串
            String jsonString = meta.getAsString();
            try {
                // 使用Gson解析字符串为JsonObject
                JsonElement jsonElement = JsonParser.parseString(jsonString);
                JsonObject gsonObject = jsonElement.getAsJsonObject();

                // 将Gson JsonObject转换为org.json JSONObject
                for (String key : gsonObject.keySet()) {
                    jsonObject.put(key, gsonObject.get(key).getAsString());
                }
                vectorResultVO.setMeta(jsonObject.toJSONString());
            } catch (Exception e) {
                log.error("Failed to convert JsonPrimitive to JSONObject", e);
                vectorResultVO.setMeta(jsonString);
            }
            return vectorResultVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void dropCollection(String vectorDatasetVersionName) {
        milvusService.dropCollection(vectorDatasetVersionName);
    }
}
