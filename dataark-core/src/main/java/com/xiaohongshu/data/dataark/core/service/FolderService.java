package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.FolderRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.MoveRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.FolderChildrenVO;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
public interface FolderService {
    Long create(FolderRequest request, SimpleUser user);

    void rename(Long folderId, String name, SimpleUser user);

    void delete(Long folderId, SimpleUser user);

    void move(MoveRequest request, SimpleUser user);

    FolderChildrenVO show(String folderType, boolean showFile, boolean own, String keyword, SimpleUser user);
}
