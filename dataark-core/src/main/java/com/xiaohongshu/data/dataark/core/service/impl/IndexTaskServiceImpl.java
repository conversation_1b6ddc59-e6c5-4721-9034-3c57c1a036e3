package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.data.dataark.core.common.enums.PaiTaskStatus;
import com.xiaohongshu.data.dataark.core.common.enums.PlatformType;
import com.xiaohongshu.data.dataark.core.service.IndexTaskService;
import com.xiaohongshu.data.dataark.dao.entity.IndexTaskDO;
import com.xiaohongshu.data.dataark.dao.mapper.IndexTaskMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/11
 */
@Service
public class IndexTaskServiceImpl implements IndexTaskService {

    @Resource
    private IndexTaskMapper indexTaskMapper;

    @Override
    public List<IndexTaskDO> selectUnfinishedPaiTasks() {
        LambdaQueryWrapper<IndexTaskDO> wrapper = Wrappers.<IndexTaskDO>lambdaQuery()
                .eq(IndexTaskDO::getPlatform, PlatformType.pai.name())
                .isNotNull(IndexTaskDO::getTaskId)
                .notIn(IndexTaskDO::getStatus, PaiTaskStatus.FINISH_STATUS);
        return indexTaskMapper.selectList(wrapper);
    }

    @Override
    public int updateById(IndexTaskDO indexTaskDO) {
        return indexTaskMapper.updateById(indexTaskDO);
    }

    @Override
    public int saveIndexTask(IndexTaskDO indexTaskDO) {
        return indexTaskMapper.insert(indexTaskDO);
    }

    @Override
    public IndexTaskDO getLatestEmbeddingSuccessTaskOfDatasetVersion(Long datasetVersionId) {
        LambdaQueryWrapper<IndexTaskDO> wrapper = Wrappers.<IndexTaskDO>lambdaQuery()
                .eq(IndexTaskDO::getDatasetVersionId, datasetVersionId)
                .eq(IndexTaskDO::getPlatform, PlatformType.pai.name())
                .eq(IndexTaskDO::getStatus, PaiTaskStatus.SUCCEEDED.getCode())
                .isNotNull(IndexTaskDO::getOutputPath)
                .orderByDesc(IndexTaskDO::getId)
                .last("LIMIT 1");
        return indexTaskMapper.selectOne(wrapper);
    }

    @Override
    public IndexTaskDO getRunningDataverseExportTask(Long datasetVersionId, String indexExportTaskSource) {
        LambdaQueryWrapper<IndexTaskDO> wrapper = Wrappers.<IndexTaskDO>lambdaQuery()
                .eq(IndexTaskDO::getDatasetVersionId, datasetVersionId)
                .eq(IndexTaskDO::getTaskId, indexExportTaskSource)
                .eq(IndexTaskDO::getPlatform, PlatformType.dataverse.name())
                .eq(IndexTaskDO::getStatus, "RUNNING")
                .orderByDesc(IndexTaskDO::getId)
                .last("LIMIT 1");
        return indexTaskMapper.selectOne(wrapper);
    }

    @Override
    public void deleteIndexTaskByDatasetVersionIdAndIndexType(Long datasetVersionId, String indexType) {
        LambdaQueryWrapper<IndexTaskDO> wrapper = Wrappers.<IndexTaskDO>lambdaQuery()
                .eq(IndexTaskDO::getDatasetVersionId, datasetVersionId)
                .eq(IndexTaskDO::getIndexType, indexType);
        indexTaskMapper.delete(wrapper);
    }
}
