package com.xiaohongshu.data.dataark.core.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

@Getter
public enum CapellaQueryState {

    DRAFT("DRAFT", "草稿"),
    RUNNING("RUNNING", "正在运行SQL"),
    STOPPED("STOPPED", "被暂停"),
    CANCELLED("CANCELLED", "被取消"),
    KILLED("KILLED", "被终止"),
    DOWNLOADING("DOWNLOADING", "下载中"),
    FINISHED("FINISHED", "运行结束");
    private String state;
    private String desc;

    CapellaQueryState(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }


    public static List<String> unfinishedStatus() {
        return Lists.newArrayList(
                CapellaQueryState.DRAFT.getState(),
                CapellaQueryState.DOWNLOADING.getState(),
                CapellaQueryState.RUNNING.getState(),
                CapellaQueryState.STOPPED.getState());
    }

}
