package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

/**
 * @author: longya
 * @since: 2023/9/2 17:18
 * @description:
 */
@Getter
public enum OaFormType {

    DATASET_PUBLISH("AGISJJFB", "数据集发布"),
    DATASET_PUBLISH_BINIDX("AGISJJFB", "数据集发布并生成Binidx")
    ;

    /**
     * 单据类型
     */
    private final String ticketCode;
    private final String desc;

    OaFormType(String ticketCode, String desc) {
        this.ticketCode = ticketCode;
        this.desc = desc;
    }
}
