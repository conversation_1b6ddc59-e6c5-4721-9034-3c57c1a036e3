package com.xiaohongshu.data.dataark.core.snapshot;

import com.google.common.collect.Maps;
import com.xiaohongshu.data.dataark.core.common.enums.OssSnapshotStatus;
import com.xiaohongshu.data.dataark.core.common.enums.Region;
import com.xiaohongshu.data.dataark.core.service.DatasetVersionInfoService;
import com.xiaohongshu.data.dataark.core.utils.OSSUtils;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.xiaohongshu.data.dataark.core.common.enums.Region.AP_SOUTHEAST_1;
import static com.xiaohongshu.data.dataark.core.common.enums.Region.CN_SHANGHAI;

@Component
@Slf4j
public class OssSnapshotService {

    private static final String AGI_DATAARK_BUCKET_NAME = "agi-dataark";
    private static final String ossUtilBinPath = "deploy/linux/ossutil";
    private static final String localOssUtilBinPath = "/tmp/ossutil";

    private static final Map<Region, String> datasetSnapshotBuckets = Maps.newHashMap();

    static {
        datasetSnapshotBuckets.put(CN_SHANGHAI, "agi-dataark");
        datasetSnapshotBuckets.put(AP_SOUTHEAST_1, "agi-dataark-sg");
    }

    @Autowired
    private DatasetVersionInfoService datasetVersionInfoService;

    @RedSchedule(value = "dataset-oss-snapshot", cron = "0 * * * * ?", desc = "数据集oss文件备份", autoFillAppid = true)
    public void datasetVersionSnapshot() {
        DatasetVersionInfoDO pendingSnapshotTask = datasetVersionInfoService.getPendingSnapshotTask();
        if (ObjectUtils.isEmpty(pendingSnapshotTask)) {
            log.info("No pending snapshot task");
            return;
        }
        datasetVersionSnapshot(pendingSnapshotTask);
    }

    public boolean isOssUtilAvailable() {
        try {
            Process process = Runtime.getRuntime().exec("ossutil version");
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (IOException e) {
            // IOException 通常表示命令不存在
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    public void downloadOssutilBinTest() {
        OSSUtils.downloadObject(AGI_DATAARK_BUCKET_NAME, ossUtilBinPath, localOssUtilBinPath);
        try {
            Runtime.getRuntime().exec("chmod 755 " + localOssUtilBinPath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @PostConstruct
    public void downloadOssUtilBin() {
        if (isOssUtilAvailable()) {
            log.info("ossutil is available");
            return;
        }

        Set<String> x86ArchSet = new HashSet<>(Arrays.asList("x86_64", "amd64"));
        if (!"Linux".equalsIgnoreCase(System.getProperty("os.name")) || !x86ArchSet.contains(System.getProperty("os.arch"))) {
            log.warn("ossutil will installed only on the linux x86 architecture");
            // 本地环境手动安装文档
            // https://help.aliyun.com/zh/oss/developer-reference/install-ossutil2?spm=a2c4g.11186623.help-menu-31815.d_5_3_1_0.2fd8300028gPWW&scm=20140722.H_2786111._.OR_help-T_cn~zh-V_1#dda54a7096xfh
            return;
        }

        OSSUtils.downloadObject(AGI_DATAARK_BUCKET_NAME, ossUtilBinPath, localOssUtilBinPath);

        try {
            Runtime.getRuntime().exec("chmod 755 " + localOssUtilBinPath);
            Runtime.getRuntime().exec("mv " + localOssUtilBinPath + " /usr/local/bin/");
        } catch (IOException e) {
            log.error("install ossutil failed", e);
        }

        log.info("ossutil available: " + isOssUtilAvailable());
    }

    public void execOssCpCmd(String src, String dest, String region) throws IOException, InterruptedException {
        Process process = getProcess(src, dest, region);

        // 获取输出
        BufferedReader br = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = br.readLine()) != null) {
            log.info("-> {}", line);
        }
        int exitCode = process.waitFor();
        log.info("Exited with code: {}", exitCode);
        if (exitCode != 0) {
            throw new ServiceException("ossutil cp command failed with exit code: " + exitCode);
        }
    }

    @NotNull
    private static Process getProcess(String src, String dest, String region) throws IOException {
        if (StringUtils.isEmpty(src) || StringUtils.isEmpty(dest) || StringUtils.isEmpty(region)) {
            throw new IllegalArgumentException("Invalid arguments: src, dest or region is empty");
        }
        String cpCmd = "ossutil cp " + src + " " + dest + " --recursive --update";
        log.info(cpCmd);

        ProcessBuilder pb = new ProcessBuilder("/bin/sh", "-c", cpCmd);

        String ossEndpoint = "https://oss-" + region + "-internal.aliyuncs.com";

        // 设置环境变量
        Map<String, String> env = pb.environment();
        env.put("OSS_ACCESS_KEY_ID", OSSUtils.OSS_KEY);
        env.put("OSS_ACCESS_KEY_SECRET", OSSUtils.OSS_SECRET);
        env.put("OSS_ENDPOINT", ossEndpoint);
        env.put("OSS_REGION", region);

        pb.redirectErrorStream(true);
        return pb.start();
    }

    /**
     * @param datasetVersionInfoDO
     * @return 备份文件路径命名规则: oss://agi-dataark/dataset_snapshot/{dataverseVersionId}/{原始路径中的key}
     */
    public String snapshotPathFormat(DatasetVersionInfoDO datasetVersionInfoDO) {

        if (ObjectUtils.isEmpty(datasetVersionInfoDO) || datasetVersionInfoDO.getId() == null) {
            throw new IllegalArgumentException("datasetVersionInfoDO or id is null");
        }
        if (StringUtils.isEmpty(datasetVersionInfoDO.getOssPath())) {
            throw new IllegalArgumentException("datasetVersion " + datasetVersionInfoDO.getId() + " ossPath is null");
        }

        String srcBucket = OSSUtils.parseBucketName(datasetVersionInfoDO.getOssPath());
        // oss 路径key
        String srcKey = datasetVersionInfoDO.getOssPath().replace("oss://" + srcBucket + "/", "");

        Region region = Region.fromRegion(datasetVersionInfoDO.getRegion());
        String destBucket = datasetSnapshotBuckets.get(region);
        if (StringUtils.isEmpty(destBucket)) {
            throw new IllegalArgumentException("No bucket found for region: " + region);
        }

        String env = System.getProperty("spring.profiles.active");
        if ("local".equalsIgnoreCase(env)) {
            env = "sit";
        }
        return  "oss://" + destBucket + "/dataset_snapshot/" + env + "/" + datasetVersionInfoDO.getId() + "/" + srcKey;
    }

    private String appendSlash(String path) {
        if (StringUtils.isEmpty(path) || path.contains(" ")) {
            throw new IllegalArgumentException("Invalid oss path");
        }
        if (!path.endsWith("/")) {
            return path + "/";
        } else {
            return path;
        }
    }

    public void datasetVersionSnapshot(DatasetVersionInfoDO datasetVersionInfoDO) {
        Region region = Region.fromRegion(datasetVersionInfoDO.getRegion());

        String src = appendSlash(datasetVersionInfoDO.getOssPath());
        String dest = appendSlash(datasetVersionInfoDO.getSnapshotPath());

        if (!dest.startsWith("oss://agi-dataark")) {
            throw new IllegalArgumentException("Invalid oss snapshot path " + dest);
        }

        datasetVersionInfoDO.setSnapshotStatus(OssSnapshotStatus.RUNNING.getStatus());
        try {
            execOssCpCmd(src, dest, region.getRegion());
            datasetVersionInfoDO.setSnapshotStatus(OssSnapshotStatus.SUCCESS.getStatus());
        } catch (Exception e) {
            log.error("执行oss cp 命令失败：{}", e.getMessage());
            datasetVersionInfoDO.setSnapshotStatus(OssSnapshotStatus.FAILED.getStatus());
        }
        datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
        datasetVersionInfoService.updateById(datasetVersionInfoDO);
    }

    public void datasetVersionSnapshot(Long datasetVersionId) {

        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(datasetVersionId);
        if (ObjectUtils.isEmpty(datasetVersionInfoDO)) {
            throw new IllegalArgumentException("datasetVersionId: " + datasetVersionId + " not found");
        }
        if (StringUtils.isEmpty(datasetVersionInfoDO.getOssPath())) {
            throw new IllegalArgumentException("datasetVersionId: " + datasetVersionId + " ossPath is empty");
        }
        if (StringUtils.isEmpty(datasetVersionInfoDO.getSnapshotPath())) {
            throw new IllegalArgumentException("datasetVersionId: " + datasetVersionId + " snapshotPath is empty");
        }

        datasetVersionSnapshot(datasetVersionInfoDO);
    }
}
