package com.xiaohongshu.data.dataark.core.schedule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.data.dataark.core.common.enums.CapellaQueryState;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryDataVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryProgressVO;
import com.xiaohongshu.data.dataark.core.config.RedisCache;
import com.xiaohongshu.data.dataark.core.service.AdhocQueryRecordService;
import com.xiaohongshu.data.dataark.core.service.WebSearchService;
import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;
import com.xiaohongshu.data.dataark.dao.mapper.AdhocQueryRecordMapper;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/7/15 16:50
 */
@Service
@Slf4j
public class AdhocQueryStatusRefreshSchedule {

    @Resource
    private AdhocQueryRecordMapper adhocQueryRecordMapper;

    @Resource
    private WebSearchService webSearchService;

    @Resource
    private RedisCache redisCache;

    @RedSchedule(value = "sync-adhoc-query-status-refresh-schedule", desc = "adhoc查询状态刷新", autoFillAppid = true)
    @SneakyThrows(Exception.class)
    public void adhocQueryStatusRefreshSchedule() {
        LocalDateTime after = LocalDateTime.now().minusDays(1);
        LocalDateTime before = LocalDateTime.now().minusMinutes(1);
        LambdaQueryWrapper<AdhocQueryRecordDO> wrapper = Wrappers.<AdhocQueryRecordDO>lambdaQuery()
                .ge(AdhocQueryRecordDO::getCreateTime, after)
                .le(AdhocQueryRecordDO::getUpdateTime, before)
                .in(AdhocQueryRecordDO::getState, CapellaQueryState.unfinishedStatus())
                .last("limit 100");

        List<AdhocQueryRecordDO> runningRecords = adhocQueryRecordMapper.selectList(wrapper);
        if (runningRecords.isEmpty()) {
            log.info("当前暂无未完成查询记录");
            return;
        }

        for (AdhocQueryRecordDO adhocQueryRecordDO : runningRecords) {
            String queryId = adhocQueryRecordDO.getQueryId();
            if (StringUtils.isEmpty(queryId)) {
                log.warn("查询记录ID: {} 的查询ID为空，跳过状态刷新", adhocQueryRecordDO.getId());
                adhocQueryRecordDO.setState(CapellaQueryState.KILLED.getState());
                adhocQueryRecordMapper.updateById(adhocQueryRecordDO);
                continue;
            }

            AdhocQueryProgressVO adhocQueryProgressVO = webSearchService.queryProgress(queryId);
            if (CapellaQueryState.FINISHED.getState().equals(adhocQueryProgressVO.getState())) {
                if (StringUtils.isEmpty(adhocQueryProgressVO.getError())) {
                    adhocQueryRecordDO.setState(adhocQueryProgressVO.getState());
                    AdhocQueryDataVO adhocQueryDataVO = webSearchService.queryData(queryId);
                    for (int index = 0; index < adhocQueryDataVO.getRows(); index++) {
                        String data = JSONObject.toJSONString(adhocQueryDataVO.getData().get(index));
                        redisCache.setWebData(queryId, index, data);
                    }
                    adhocQueryDataVO.setData(new ArrayList<>());
                    redisCache.setWebDataResult(queryId, JSONObject.toJSONString(adhocQueryDataVO));
                } else {
                    // capella 没有ERROR状态，这里模拟ERROR状态
                    adhocQueryRecordDO.setState("ERROR");
                }
            }

//            adhocQueryRecordDO.setState(adhocQueryProgressVO.getState());
            adhocQueryRecordMapper.updateById(adhocQueryRecordDO);
        }


    }
}
