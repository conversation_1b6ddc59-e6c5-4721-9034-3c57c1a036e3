package com.xiaohongshu.data.dataark.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @email y<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/10
 */
public interface AdhocQueryRecordService {

    int insert(AdhocQueryRecordDO adhocQueryRecordDO);

    IPage<AdhocQueryRecordDO> getAdhocQueryRecordPage(Long adhocId, List<String> queryTypes, Integer pageIndex, Integer pageSize);

    AdhocQueryRecordDO selectById(Long adhocQueryRecordId);


    void updateStateById(Long id, String state);
}
