package com.xiaohongshu.data.dataark.core.common.model.request;

import com.xiaohongshu.data.dataark.core.common.enums.BinidxSceneType;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DataSource;
import com.xiaohongshu.data.dataark.dao.entity.dataset.ProcessingTask;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Data
public class BinidxPublishTaskRequest {

    private Long datasetVersionId;

    private List<DataSource> dataSources;

    private List<ProcessingTask> processingTasks;

    private BinidxTaskConf binidxTaskConf;

    private Boolean generateBinidx;

    private String verificationReport;

    private String approvalRemark;

    public BinidxTaskDO toBinidxTaskDO() {
        BinidxTaskDO binidxTaskDO = new BinidxTaskDO();
        binidxTaskDO.setDatasetVersionId(datasetVersionId);
        binidxTaskDO.setScene(BinidxSceneType.PUBLISH.getCode());
        binidxTaskDO.setJobConf(JsonUtil.toString(binidxTaskConf));
        return binidxTaskDO;
    }
}
