package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.dao.entity.IndexTaskDO;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/11
 */
public interface IndexTaskService {
    List<IndexTaskDO> selectUnfinishedPaiTasks();

    int updateById(IndexTaskDO indexTaskDO);

    int saveIndexTask(IndexTaskDO indexTaskDO);

    IndexTaskDO getLatestEmbeddingSuccessTaskOfDatasetVersion(Long datasetVersionId);

    IndexTaskDO getRunningDataverseExportTask(Long datasetVersionId, String indexExportTaskSource);

    void deleteIndexTaskByDatasetVersionIdAndIndexType(Long datasetVersionId, String indexType);
}
