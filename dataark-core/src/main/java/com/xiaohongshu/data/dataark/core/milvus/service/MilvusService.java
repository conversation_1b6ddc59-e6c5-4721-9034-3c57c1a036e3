package com.xiaohongshu.data.dataark.core.milvus.service;

import com.google.common.collect.Lists;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.collection.request.DropCollectionReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/13
 */
@Service
@Slf4j
public class MilvusService {

    @Value("${third_party.milvus.uri:}")
    private String milvusUri;

    @Value("${third_party.milvus.token:}")
    private String milvusToken;

    @Value("${third_party.milvus.database:}")
    private String milvusDatabase;

    private static final List<String> OUTPUT_FIELDS = Lists.newArrayList("docid", "meta", "text");

    public List<SearchResp.SearchResult> search(List<String> collectionNames, List<Float> queryVector, Long rows) {
        MilvusClientV2 milvusClient = getMilvusClient();
        try {
            int topK = rows.intValue();
            if (CollectionUtils.isEmpty(collectionNames) || CollectionUtils.isEmpty(queryVector)) {
                throw new ServiceException("Collection names and query vectors must not be empty");
            }
            FloatVec floatVec = new FloatVec(queryVector);
            List<BaseVector> vectors = new ArrayList<>();
            vectors.add(floatVec);
            List<SearchResp.SearchResult> searchResults = Lists.newArrayList();
            for (String collectionName : collectionNames) {
                SearchReq searchReq = SearchReq.builder()
                        .collectionName(collectionName)
                        .data(vectors)
                        .topK(topK)
                        .outputFields(OUTPUT_FIELDS)
                        .build();
                // 执行搜索
                SearchResp searchResp = milvusClient.search(searchReq);
                if (Objects.nonNull(searchResp) && CollectionUtils.isNotEmpty(searchResp.getSearchResults())) {
                    // 将搜索结果添加到总结果列表中
                    searchResults.addAll(searchResp.getSearchResults().get(0));
                }
            }
            // 根据distinct排序，取topK
            return searchResults.stream().sorted(Comparator.comparing(SearchResp.SearchResult::getScore).reversed())
                    .limit(topK)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to search in collections", e);
            throw new ServiceException("Failed to search in collections");
        } finally {
            closeClient(milvusClient);
        }
    }

    public void dropCollection(String collectionName) {
        MilvusClientV2 milvusClient = getMilvusClient();
        try {
            if (Objects.isNull(collectionName) || collectionName.isEmpty()) {
                throw new ServiceException("Collection name must not be empty");
            }
            DropCollectionReq dropCollectionReq = DropCollectionReq.builder()
                    .collectionName(collectionName)
                    .build();
            milvusClient.dropCollection(dropCollectionReq);
        } catch (Exception e) {
            log.error("Failed to drop collection: {}", collectionName, e);
            throw new ServiceException("Failed to drop collection: " + collectionName);
        } finally {
            closeClient(milvusClient);
        }
    }

    private MilvusClientV2 getMilvusClient() {
        ConnectConfig connectConfig = ConnectConfig.builder()
                .uri(milvusUri)
                .token(milvusToken)
                .dbName(milvusDatabase)
                .build();
        return new MilvusClientV2(connectConfig);
    }

    private void closeClient(MilvusClientV2 milvusClient) {
        if (Objects.nonNull(milvusClient)) {
            try {
                milvusClient.close();
            } catch (Exception e) {
                log.error("Failed to close Milvus client", e);
            }
        }
    }

}
