package com.xiaohongshu.data.dataark.core.pai.req;

import com.xiaohongshu.data.dataark.core.common.model.request.BinidxTaskConf;
import com.xiaohongshu.data.dataark.core.pai.pojo.WorkSpaceConfig;
import lombok.Data;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/17
 */
@Data
public class CreatePaiJobReq {

    private String jobDisplayName;

    private String workSpaceId;

    private String resourceId;

    private String prodCpfsDatasourceId;

    private String cpfsDatasourceId;

    private String imageUrl;

    private String userCommand;

    private Long workerPodCount;

    private Integer cpu;

    private Integer gpu;

    private Integer memory;

    private Integer priority;


    public static CreatePaiJobReq generateReq(String jobDisplayName, String imageUrl, WorkSpaceConfig workSpaceConfig, BinidxTaskConf binidxTaskConf) {
        CreatePaiJobReq req = new CreatePaiJobReq();
        req.setJobDisplayName(jobDisplayName);
        req.setWorkSpaceId(binidxTaskConf.getWorkSpaceId());
        req.setResourceId(binidxTaskConf.getResourceId());
        req.setProdCpfsDatasourceId(workSpaceConfig.getProdcpfsId());
        req.setCpfsDatasourceId(workSpaceConfig.getCpfsId());
        req.setImageUrl(imageUrl);
        req.setUserCommand(binidxTaskConf.getScript());
        req.setWorkerPodCount(binidxTaskConf.getPodCount());
        req.setCpu(binidxTaskConf.getCpu());
        req.setMemory(binidxTaskConf.getMemory());
        req.setPriority(binidxTaskConf.getPriority());
        return req;
    }

}
