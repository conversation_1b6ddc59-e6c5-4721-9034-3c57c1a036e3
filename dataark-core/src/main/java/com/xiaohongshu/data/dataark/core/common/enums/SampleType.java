package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/25
 */
@Getter
public enum SampleType {

    SAMPLE("sample", "采样验证"),
    ALL("all", "全量验证");

    private final String code;
    private final String desc;

    SampleType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
