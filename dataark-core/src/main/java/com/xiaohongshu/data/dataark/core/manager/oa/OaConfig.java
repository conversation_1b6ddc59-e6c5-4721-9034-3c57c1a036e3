package com.xiaohongshu.data.dataark.core.manager.oa;

import com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.PublicEmployeeService;
import com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory;
import com.xiaohongshu.infra.rpc.core.registry.ThriftServerAddressProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/23
 */
@Configuration
@Slf4j
public class OaConfig {

    @Resource
    private ApplicationContext applicationContext;

    @Resource(name = "thriftServerAddressEdsManager")
    private ThriftServerAddressProvider serverAddressProvider;

    @Bean
    public PublicEmployeeService.Iface provideEmployeeOuterService() throws Exception {
        com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory();
        String env = System.getProperty("spring.profiles.active");
        thriftServiceClientProxyFactory.setService("com.xiaohongshu.fls.rpc.enterprise.oa_public.PublicEmployeeService");
        if ("sgp".equalsIgnoreCase(env) || "alsg".equalsIgnoreCase(env)) {
            return new PublicEmployeeService.Client(null, null);
        } else {
            thriftServiceClientProxyFactory.setSocketTimeout(5000);
            thriftServiceClientProxyFactory.setServerAddressProvider(serverAddressProvider);
            thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
            thriftServiceClientProxyFactory.afterPropertiesSet();
        }
        return (PublicEmployeeService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean
    public OaFlowRuntimeProcessRpc.Iface oaFlowRuntimeProcessRpc() {
        String env = System.getProperty("spring.profiles.active");
        try {
            ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
            thriftServiceClientProxyFactory.setService("com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc");
            if ("awssg".equalsIgnoreCase(env) || "alsg".equalsIgnoreCase(env)) {
                return new OaFlowRuntimeProcessRpc.Client(null, null);
            } else {
                thriftServiceClientProxyFactory.setServerAddressProvider(serverAddressProvider);
                thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
                thriftServiceClientProxyFactory.setSocketTimeout(10000);
                thriftServiceClientProxyFactory.setConnectTimeout(10000);
                thriftServiceClientProxyFactory.afterPropertiesSet();
            }
            return (OaFlowRuntimeProcessRpc.Iface) thriftServiceClientProxyFactory.getObject();
        } catch (Exception e) {
            log.error("OaFlowRuntimeProcessRpc init error: ", e);
            return null;
        }
    }

}
