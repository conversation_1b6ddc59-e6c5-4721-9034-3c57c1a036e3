package com.xiaohongshu.data.dataark.core.manager.oa;

import com.google.common.collect.Lists;
import com.xiaohongshu.data.dataark.core.manager.oa.pojo.RedUser;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.PublicEmployeeService;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.request.QueryBaseUserInfoRequest;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.request.SearchUserForSubSystemRequest;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.response.BaseUserInfoWtihXhsIdResponse;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.response.RpcUserInfo;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.response.RpcUserInfoWithXhsId;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.response.SearchUserForSubSystemResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: longya
 * @since: 2023/9/2 17:04
 * @description:
 */
@Component
@Slf4j
public class OaEmployeeManager {

    /**
     * 若更换 需要申请权限
     */
    public static final String SYS_CODE = "rugal";

    @Resource
    private PublicEmployeeService.Iface publicEmployeeService;

    public Map<String, String> emailToUserId(Set<String> emails) {
        return queryBatchRedUsers(new ArrayList<>(emails)).stream().collect(Collectors.toMap(RedUser::getEmail,
                RedUser::getUserId,
                (i1, i2) -> i2));
    }

    public SimpleUser queryRedUsersById(String userId) {
        SearchUserForSubSystemRequest searchUserForSubSystemRequest = new SearchUserForSubSystemRequest();
        searchUserForSubSystemRequest.setKey(userId);
        searchUserForSubSystemRequest.setSysCode("dataverse");

        try {
            SearchUserForSubSystemResponse searchUserForSubSystemResponse = publicEmployeeService.searchUserForSubSystemFromES(new Context(), searchUserForSubSystemRequest);
            if (Objects.isNull(searchUserForSubSystemResponse)) {
                throw new ServiceException("查询用户信息失败,响应为空");
            }
            if (!searchUserForSubSystemResponse.isSuccess()) {
                log.error("查询用户信息失败, {}", searchUserForSubSystemResponse.getError_message());
                throw new ServiceException("查询用户信息失败");
            }
            List<RpcUserInfo> userInfoList = searchUserForSubSystemResponse.getUserInfoList();
            if (CollectionUtils.isEmpty(userInfoList)) {
                log.error("查询用户信息失败，用户不存在");
                throw new ServiceException("查询用户信息失败");
            }

            Optional<SimpleUser> optionalSimpleUser = userInfoList.stream()
                    .filter(e -> e.getEmailPre().equals(userId))
                    .map(userInfo -> {
                        SimpleUser simpleUser = new SimpleUser();
                        simpleUser.setUserId(userInfo.getEmailPre());
                        simpleUser.setEmail(userInfo.getEmail());
                        if (StringUtils.isEmpty(userInfo.getRedName())) {
                            simpleUser.setDisplayName(String.format("%s", userInfo.getUserName()));
                        } else {
                            simpleUser.setDisplayName(String.format("%s(%s)", userInfo.getRedName(), userInfo.getUserName()));
                        }
                        return simpleUser;
                    }).findFirst();

            if (optionalSimpleUser.isPresent()) {
                return optionalSimpleUser.get();
            } else {
                log.error("查询用户信息失败，用户不存在");
                throw new ServiceException("查询用户信息失败");
            }
        } catch (TException e) {
            String errMsg = "os接口调用失败";
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
    }

    public List<RedUser> queryBatchRedUsers(List<String> emails) {
        Context context = new Context();
        QueryBaseUserInfoRequest request = new QueryBaseUserInfoRequest();
        request.setEmailList(emails);
        request.setSysCode(SYS_CODE);
        List<RedUser> redUsers = Lists.newArrayList();
        log.info("Request emails: {}", emails);
        try {
            BaseUserInfoWtihXhsIdResponse response = Objects.requireNonNull(publicEmployeeService.queryUserInfoWithXhsIdByEmail(context,
                    request));
            if (CollectionUtils.isNotEmpty(response.userInfoList)) {
                List<RpcUserInfoWithXhsId> userInfoList = Optional.ofNullable(response.getUserInfoList()).orElse(Lists.newArrayList());
                if (!CollectionUtils.isEmpty(userInfoList)) {
                    userInfoList.forEach(userInfo -> {
                        RedUser redUser = new RedUser();
                        BeanUtils.copyProperties(userInfo, redUser);
                        redUsers.add(redUser);
                    });
                }
            } else {
                log.error("获取red user信息失败: {}", response.error_message);
                throw new ServiceException("获取red user信息失败, 进行重试");
            }
            log.info("Response red users: {}", redUsers);
        } catch (Exception e) {
            log.error("获取red user信息失败: ", e);
        }
        return redUsers;
    }

}
