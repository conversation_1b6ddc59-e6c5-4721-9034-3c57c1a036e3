package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/19
 */
@Getter
public enum BinidxSceneType {

    SAMPLE("sample", "采样验证"),
    PUBLISH("publish", "发布打包");

    private final String code;
    private final String desc;

    BinidxSceneType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BinidxSceneType getCodeOf(String code) {
        for (BinidxSceneType binidxSceneType : values()) {
            if (binidxSceneType.code.equalsIgnoreCase(code)) {
                return binidxSceneType;
            }
        }
        throw new IllegalArgumentException("BinidxSceneType not found for code: " + code);
    }

}
