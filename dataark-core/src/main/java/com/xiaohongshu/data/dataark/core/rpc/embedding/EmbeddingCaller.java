package com.xiaohongshu.data.dataark.core.rpc.embedding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xiaohongshu.dataverse.common.alert.RequestSupport;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/18
 */
@Service
@Slf4j
public class EmbeddingCaller extends RequestSupport {
    @Value("${third_party.embedding.get_embedding:}")
    private String getEmbeddingUrl;

    @Autowired
    private OkHttpClient httpClient;

    public List<Float> getEmbedding(String text) {
        EmbeddingReq embeddingReq = new EmbeddingReq();
        embeddingReq.setSentences(Lists.newArrayList(text));
        try {
            String url = getEmbeddingUrl;
            String body = JsonUtil.toString(embeddingReq);
            Request request = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body))
                    .build();
            Response response = withRetry3(() -> httpClient.newCall(request).execute());
            if (response.isSuccessful()) {
                String resp = Objects.requireNonNull(response.body()).string();
                EmbeddingResp embeddingResp = JsonUtil.parseObject(resp, EmbeddingResp.class);
                if (embeddingResp.isSuccess() && CollectionUtils.isNotEmpty(embeddingResp.getDense_vecs())) {
                    return embeddingResp.getDense_vecs().get(0);
                } else {
                    log.error("get embedding failed, text: {}, response: {}", text, resp);
                    throw new ServiceException("获取文本向量失败，请检查配置是否正确");
                }
            } else {
                log.error("get embedding failed, text: {}, response code: {}, response body: {}", text, response.code(), response.body().string());
                throw new ServiceException("获取文本向量失败，请检查配置是否正确");
            }
        } catch (Exception e) {
            log.error("get embedding failed, text: {}, error: {}", text, e.getMessage(), e);
            throw new ServiceException("获取文本向量失败，请检查配置是否正确");
        }
    }

    @Data
    static class EmbeddingReq{
        private List<String> sentences;
    }

    @Data
    static class EmbeddingResp {
        private boolean success;
        private List<List<Float>> dense_vecs;
    }
}
