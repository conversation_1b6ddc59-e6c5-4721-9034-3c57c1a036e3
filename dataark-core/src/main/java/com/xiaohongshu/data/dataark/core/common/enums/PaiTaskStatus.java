package com.xiaohongshu.data.dataark.core.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Getter
public enum PaiTaskStatus {

    CREATING("Creating", "生成中"),
    QUEUING("Queuing", "生成中"),
    BIDDING("Bidding", "生成中"),
    ENVPREPARING("EnvPreparing", "生成中"),
    SANITYCHECKING("SanityChecking", "生成中"),
    RUNNING("Running", "生成中"),
    RESTARTING("Restarting", "生成中"),
    STOPPING("Stopping", "生成中"),
    SUCCEEDEDRESERVING("SucceededReserving", "生成中"),
    FAILEDRESERVING("FailedReserving", "生成中"),
    SUCCEEDED("Succeeded", "已成功"),
    FAILED("Failed", "已失败"),
    STOPPED("Stopped", "已停止");

    private final String code;
    private final String desc;

    PaiTaskStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getStatusDisplay(String status) {
        if (FINISH_STATUS.contains(status)) {
            PaiTaskStatus paiTaskStatus = getByCode(status);
            return paiTaskStatus.getDesc();
        }
        return "生成中";
    }

    public static PaiTaskStatus getByCode(String code) {
        for (PaiTaskStatus paiTaskStatus : PaiTaskStatus.values()) {
            if (paiTaskStatus.getCode().equals(code)) {
                return paiTaskStatus;
            }
        }
        throw new IllegalArgumentException("Invalid BinidxTaskStatus code: " + code);
    }

    public static final List<String> FINISH_STATUS = Lists.newArrayList(
            PaiTaskStatus.SUCCEEDED.getCode(),
            PaiTaskStatus.FAILED.getCode(),
            PaiTaskStatus.STOPPED.getCode()
    );
}
