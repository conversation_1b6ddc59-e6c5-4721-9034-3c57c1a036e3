package com.xiaohongshu.data.dataark.core.common.model.dragongate;

import lombok.Data;
import java.util.Arrays;
import java.util.Map;

@Data
public class QueryProgress {
    private String queryId;

    private String state;

    private String error;

    private Double progress;

    private Map<String, Object> progressInfo;

    private String executedSql;

    private boolean requireAuth;

    public boolean finished() {
        return "FINISHED".equals(state) && progress >= 100;
    }

    public boolean errorState() {
        return Arrays.asList("CANCELLED", "STOPPED", "KILLED").contains(state);
    }
}
