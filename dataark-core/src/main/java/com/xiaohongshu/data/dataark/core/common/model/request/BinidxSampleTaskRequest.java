package com.xiaohongshu.data.dataark.core.common.model.request;

import com.xiaohongshu.data.dataark.core.common.enums.BinidxSceneType;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Data
public class BinidxSampleTaskRequest {

    private Long datasetVersionId;

    private String sampleType;

    private Long sampleId;

    private BinidxTaskConf binidxTaskConf;

    public BinidxTaskDO toBinidxTaskDO() {
        BinidxTaskDO binidxTaskDO = new BinidxTaskDO();
        binidxTaskDO.setDatasetVersionId(datasetVersionId);
        binidxTaskDO.setScene(BinidxSceneType.SAMPLE.getCode());
        binidxTaskDO.setSampleType(sampleType);
        binidxTaskDO.setSampleId(sampleId);
        binidxTaskDO.setJobConf(JsonUtil.toString(binidxTaskConf));
        return binidxTaskDO;
    }

}
