package com.xiaohongshu.data.dataark.core.gravitino.service;

import com.xiaohongshu.data.dataark.core.common.constant.Constants;
import com.xiaohongshu.data.dataark.core.common.enums.Region;
import org.apache.gravitino.NameIdentifier;
import org.apache.gravitino.file.Fileset;
import org.apache.gravitino.file.FilesetCatalog;
import org.apache.gravitino.file.FilesetChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/21
 */
@Service
public class GravitinoFilesetService {

    @Autowired
    @Qualifier("filesetCatalog")
    private FilesetCatalog filesetCatalog;

    @Autowired
    @Qualifier("filesetCatalogSgp")
    private FilesetCatalog filesetCatalogSgp;

    public static final String SCHEMA = "dataark";

    public Integer createFileset(String datasetCode, String location) {
        NameIdentifier filesetIdentifier = NameIdentifier.of(SCHEMA, datasetCode);
        Fileset fileset;
        fileset  = filesetCatalog.createFileset(filesetIdentifier, "", Fileset.Type.EXTERNAL, location, null);
//        if (location.startsWith(Constants.LSH_OSS_PREFIX)) {
//        } else if (location.startsWith(Constants.SGP_OSS_PREFIX)) {
//            fileset = filesetCatalogSgp.createFileset(filesetIdentifier, "", Fileset.Type.EXTERNAL, location, null);
//        } else {
//            throw new IllegalArgumentException("Invalid location: " + location);
//        }
        return (int) fileset.version();
    }

    public Integer addFilesetVersion(String datasetCode, String location) {
        NameIdentifier filesetIdentifier = NameIdentifier.of(SCHEMA, datasetCode);
        Fileset fileset;
        fileset = filesetCatalog.alterFileset(filesetIdentifier, FilesetChange.updateLocation(location));
//        if (location.startsWith(Constants.LSH_OSS_PREFIX)) {
//        } else if (location.startsWith(Constants.SGP_OSS_PREFIX)) {
//            fileset = filesetCatalogSgp.alterFileset(filesetIdentifier, FilesetChange.updateLocation(location));
//        } else {
//            throw new IllegalArgumentException("Invalid location: " + location);
//        }
        return (int) fileset.version();
    }

    public String getFilesetVersionLocation(String region, String datasetCode, Integer version) {
        NameIdentifier filesetIdentifier = NameIdentifier.of(SCHEMA, datasetCode);
        Fileset fileset;
        fileset = filesetCatalog.loadFileset(filesetIdentifier, version);
//        if (Region.CN_SHANGHAI.getRegion().equals(region)) {
//        } else if (Region.AP_SOUTHEAST_1.getRegion().equals(region)) {
//            fileset = filesetCatalogSgp.loadFileset(filesetIdentifier, version);
//        } else {
//            throw new IllegalArgumentException("不支持的region: " + region);
//        }
        return fileset.storageLocation();
    }

    public boolean dropFileset(String region, String datasetCode) {
        NameIdentifier filesetIdentifier = NameIdentifier.of(SCHEMA, datasetCode);
        return filesetCatalog.dropFileset(filesetIdentifier);
//        if (Region.CN_SHANGHAI.getRegion().equals(region)) {
//        } else if (Region.AP_SOUTHEAST_1.getRegion().equals(region)) {
//            return filesetCatalogSgp.dropFileset(filesetIdentifier);
//        } else {
//            throw new IllegalArgumentException("不支持的region: " + region);
//        }
    }

}
