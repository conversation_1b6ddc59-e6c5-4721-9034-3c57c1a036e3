package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.data.dataark.core.common.enums.AuditStatus;
import com.xiaohongshu.data.dataark.core.common.enums.OaFormType;
import com.xiaohongshu.data.dataark.core.service.ApproveRecordService;
import com.xiaohongshu.data.dataark.dao.entity.ApproveRecordDO;
import com.xiaohongshu.data.dataark.dao.mapper.ApproveRecordMapper;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Service
public class ApproveRecordServiceImpl implements ApproveRecordService {

    @Resource
    private ApproveRecordMapper approveRecordMapper;

    @Override
    public ApproveRecordDO buildPublishBinidxRecord(String formId, OaFormType oaFormType, Long datasetVersionId, Long binidxTaskDOId, SimpleUser user) {
        ApproveRecordDO approveRecordDO = new ApproveRecordDO();
        approveRecordDO.setApproveFormId(formId);
        approveRecordDO.setApproveStatus(AuditStatus.IN_REVIEW.name());
        approveRecordDO.setApproveType(oaFormType.name());
        approveRecordDO.setDatasetVersionId(datasetVersionId);
        approveRecordDO.setBinidxTaskId(binidxTaskDOId);
        approveRecordDO.setCreator(JsonUtil.toString(user));
        return approveRecordDO;
    }

    @Override
    public void insertRecord(ApproveRecordDO approveRecordDO) {
        approveRecordMapper.insert(approveRecordDO);
    }

    @Override
    public ApproveRecordDO selectByApproveFormId(String formNo) {
        LambdaQueryWrapper<ApproveRecordDO> wrapper = Wrappers.<ApproveRecordDO>lambdaQuery()
                .eq(ApproveRecordDO::getApproveFormId, formNo);
        return approveRecordMapper.selectOne(wrapper);
    }

    @Override
    public void updateApproveStatusByApproveFormId(String formNo, String auditStatus) {
        ApproveRecordDO approveRecordDO = selectByApproveFormId(formNo);
        if (approveRecordDO == null) {
            return;
        }
        ApproveRecordDO updateDO = new ApproveRecordDO();
        updateDO.setId(approveRecordDO.getId());
        updateDO.setApproveStatus(auditStatus);
        approveRecordMapper.updateById(updateDO);
    }

    @Override
    public ApproveRecordDO getLatestApproveRecord(Long datasetVersionId) {
        LambdaQueryWrapper<ApproveRecordDO> wrapper = Wrappers.<ApproveRecordDO>lambdaQuery()
                .eq(ApproveRecordDO::getDatasetVersionId, datasetVersionId)
                .orderByDesc(ApproveRecordDO::getCreateTime)
                .last("limit 1");
        return approveRecordMapper.selectOne(wrapper);
    }

}
