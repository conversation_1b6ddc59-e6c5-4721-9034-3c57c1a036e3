package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DecontaminationTaskDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/8/12 11:41
 */

public interface DecontaminationTaskService {

    Boolean createDecontaminationTask(DatasetInfoDO datasetInfoDO, DatasetVersionInfoDO datasetVersionInfoDO);

    Boolean updateDecontaminationTask(String taskId, String status, String errorMsg);

    List<DecontaminationTaskDO> getRunningDecontaminationTasks();

    List<DecontaminationTaskDO> getDecontaminationTasksByDatasetId(Long datasetId);

    Boolean decontaminationTasksDone(Long datasetVersionId);

    Boolean decontaminationTasksDone(DatasetVersionInfoDO datasetVersionInfoDO);

    DecontaminationTaskDO getDecontaminationTaskByTaskId(String taskId);
}
