package com.xiaohongshu.data.dataark.core.rpc;

import okhttp3.OkHttpClient;

import java.time.Duration;

public class HttpClientProvider {
    private HttpClientProvider() {
    }

    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(120))
            .writeTimeout(Duration.ofSeconds(120))
            .callTimeout(Duration.ofSeconds(120))
            .readTimeout(Duration.ofSeconds(120))
            .build();

    public static OkHttpClient httpClient() {
        return httpClient;
    }
}
