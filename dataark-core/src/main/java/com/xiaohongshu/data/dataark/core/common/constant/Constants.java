package com.xiaohongshu.data.dataark.core.common.constant;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/11
 */
public class Constants {

    public static final String LOCAL = "local";

    public static final String PROD = "prod";

    public static final String STAGING = "staging";

    public static final String SIT = "sit";

    public static final String KMS_KEY_ID = "kms.keyId.hosting.t1744805930779";

    public static final String KMS_KEY = "kms.key.hosting.t1744805930779";

    // 后续为了灵活性可以配置成apollo列表，用户可选择，一期指定新加坡
    public static final String PAI_SGP_ENDPOINT = "pai-dlc.ap-southeast-1.aliyuncs.com";

    public static final String PAI_SGP_URL = "https://pai.console.aliyun.com/?regionId=ap-southeast-1&spm=5176.pai-console-inland.0.0.65fb2b6e0rMctV&workspaceId=${workspaceId}#/dlc/jobs/${jobId}/overview";

    public static final String SGP_OSS_PREFIX = "oss://lsg-oss-chatgpt-agi-hcfs/";

    public static final String LSH_OSS_PREFIX = "oss://lsh-oss-gpt-spam/";

    public static final String SCRIPT = "#!/bin/bash\n" +
            "\n" +
            "# ======== 非必要不用改 =======\n" +
            "TASK_SCRIPTS_GIT=/cpfs/user/laite/workspace/pretrain/task_scripts\n" +
            "export PYTHONPATH=$TASK_SCRIPTS_GIT:$PYTHONPATH\n" +
            "\n" +
            "until pip install -i https://mirrors.aliyun.com/pypi/simple/ fastparquet nltk megatron-core==0.6.0 oss2 pyarrow; do\n" +
            "    echo \"pip install failed. Retrying in 5 seconds...\"\n" +
            "    sleep 5\n" +
            "done\n" +
            "\n" +
            "export PROCES_PER_DEVICE=32\n" +
            "\n" +
            "export ACCESS_KEY_ID='LTAI5tDuCoTTh6gu5PK8gFfN'\n" +
            "export ACCESS_KEY_SECRET='******************************'\n" +
            "# 国外oss（新加坡）\n" +
            "export SGP_INTERNAL_ENDPOINT='https://oss-ap-southeast-1-internal.aliyuncs.com'\n" +
            "export SGP_EXTERNAL_ENDPOINT='https://oss-ap-southeast-1.aliyuncs.com'\n" +
            "export SGP_OSS_BUCKET='lsg-oss-chatgpt-agi-hcfs'\n" +
            "# # 国内oss（上海）\n" +
            "export INTERNAL_ENDPOINT='https://oss-cn-shanghai-internal.aliyuncs.com'\n" +
            "export EXTERNAL_ENDPOINT='https://oss-cn-shanghai.aliyuncs.com'\n" +
            "export OSS_BUCKET='lsh-oss-gpt-spam'\n" +
            "\n" +
            "gpt_oss_endpoints=\"oss://LTAI5tDuCoTTh6gu5PK8gFfN:<EMAIL>\"\n" +
            "sgp_oss_endpoints=\"oss://LTAI5tDuCoTTh6gu5PK8gFfN:<EMAIL>\"\n" +
            "\n" +
            "# ======== 更改项，启动配置 =======\n" +
            "# 参数说明：\n" +
            "#     --input_path: 待转成binidx的原始数据路径，可以是oss路径或cpfs路径\n" +
            "#     --output_path: 转成binidx后保存路径\n" +
            "#     --file_type: 输入文件后缀，支持'parquet', 'json', 'jsonl', 'TableSink1'\n" +
            "#     --input_region: 原始数据所在区域，sh表示上海，sgp表示新加坡\n" +
            "#     --output_region: 原始数据所在区域，sh表示上海，sgp表示新加坡\n" +
            "#     --json_keys: 要转换的字段名，默认为\"text\"\n" +
            "#     --backup2oss: 转成binidx后的文件是否要备份到oss，当保存路径为cpfs时生效，保存路径为oss://lsg-oss-chatgpt-agi-hcfs/ods/binidx/\n" +
            "# ================================\n" +
            "\n" +
            "\n" +
            "export part_idx=0\n" +
            "\n" +
            "export cache_name=qiheng_test_v2\n" +
            "export input_path=lsh-oss-gpt-spam/user/xinghai/output/data/infer/merge/business_zh_quiz_v0_pos/\n" +
            "\n" +
            "\n" +
            "dataLockFile=/cpfs/user/xinghai/data/parquet/cache/${cache_name}.txt\n" +
            "\n" +
            "\n" +
            "\n" +
            " while ! test -e $dataLockFile; do\n" +
            "     echo $dataLockFile not exists\n" +
            "     if [ $RANK -eq 0 ]; then \n" +
            "\n" +
            "         echo \"diwnload $input_path to /cpfs/user/xinghai/data/parquet/cache/$cache_name/\"\n" +
            "          juicefs sync --ignore-existing -u -p  1000 \\\n" +
            "             ${sgp_oss_endpoints}/$input_path/ \\\n" +
            "             /cpfs/user/xinghai/data/parquet/cache/$cache_name/\n" +
            "\n" +
            "         juicefs sync --ignore-existing -u -p  1000 \\\n" +
            "             ${sgp_oss_endpoints}/$input_path/ \\\n" +
            "             /cpfs/user/xinghai/data/parquet/cache/$cache_name/\n" +
            "\n" +
            "         echo \"diwnload $input_path to /cpfs/user/xinghai/data/parquet/cache/$cache_name/ ok\"\n" +
            "         echo \"ok\" >> $dataLockFile\n" +
            "     else\n" +
            "         echo \"wait 60s for data at rank $RANK\"\n" +
            "         sleep 60s\n" +
            "     fi\n" +
            " done\n" +
            "\n" +
            "\n" +
            "# 转binidx任务\n" +
            "python $TASK_SCRIPTS_GIT/dpu/main_dist.py \\\n" +
            "    $TASK_SCRIPTS_GIT/binidx/transform_binidx/transform_binidx.py \\\n" +
            "    --input_path  /cpfs/user/xinghai/data/parquet/cache/$cache_name/ \\\n" +
            "    --output_path /prodcpfs/dataark/qh_test/$cache_name \\\n" +
            "    --file_type parquet \\\n" +
            "    --input_region sgp \\\n" +
            "    --output_region sgp \\\n" +
            "    --json_keys text\n" +
            "\n" +
            "if [ $RANK -eq 0 ]; then\n" +
            "    rm -rf /cpfs/user/xinghai/data/parquet/cache/$cache_name/\n" +
            "    rm  $dataLockFile\n" +
            "    echo \"oj8k\"\n" +
            "fi";

}
