package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

@Getter
public enum ContentType {

    WEB("WEB", MediaType.TEXT, "网页"),
    QUESTION("QUESTION", MediaType.TEXT, "问答"),
    D<PERSON>LOG("DIALOG", MediaType.TEXT, "对话"),
    QUESTION_BANK("QUESTION_BANK", MediaType.TEXT, "题库"),
    ENCYCLOPEDIA("ENCYCLOPEDIA", MediaType.TEXT, "百科"),
    BOOK("BOOK", MediaType.TEXT, "书籍"),
    CODE("CODE", MediaType.TEXT, "代码"),
    MIX("MIX", MediaType.TEXT, "混合"),

    // 图文，OCR
    IMAGE_TEXT("IMAGE_TEXT", MediaType.MULTIMODAL, "图文"),
    OCR("OCR", MediaType.MULTIMODAL, "OCR");

    private String code;
    private MediaType mediaType;
    private String nameCn;

    ContentType(String code, MediaType mediaType, String nameCn) {
        this.code = code;
        this.mediaType = mediaType;
        this.nameCn = nameCn;
    }

    public static ContentType getByMediaTypeAndCnName(MediaType mediaType, String nameCn) {
        for (ContentType contentType : values()) {
            if (contentType.getMediaType().equals(mediaType)
                    && contentType.getNameCn().equals(nameCn)) {
                return contentType;
            }
        }
        return null;
    }

}
