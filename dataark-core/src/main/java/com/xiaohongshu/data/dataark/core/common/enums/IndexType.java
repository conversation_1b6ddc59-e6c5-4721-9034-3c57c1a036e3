package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/6
 */
@Getter
public enum IndexType {

    INVERTED("inverted", "倒排索引"),
    VECTOR("vector", "向量索引");

    private final String code;
    private final String desc;

    IndexType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
