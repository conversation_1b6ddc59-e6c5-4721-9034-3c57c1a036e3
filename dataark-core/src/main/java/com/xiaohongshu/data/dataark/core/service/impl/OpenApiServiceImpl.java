package com.xiaohongshu.data.dataark.core.service.impl;

import com.xiaohongshu.data.dataark.core.client.RedCityDataArkClient;
import com.xiaohongshu.data.dataark.core.common.enums.*;
import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.data.dataark.core.es.EsService;
import com.xiaohongshu.data.dataark.core.gravitino.service.GravitinoFilesetService;
import com.xiaohongshu.data.dataark.core.pai.service.PaiService;
import com.xiaohongshu.data.dataark.core.service.*;
import com.xiaohongshu.data.dataark.core.utils.OSSUtils;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DecontaminationTaskDO;
import com.xiaohongshu.data.dataark.dao.entity.IndexTaskDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/23
 */
@Service
@Slf4j
public class OpenApiServiceImpl implements OpenApiService {

    @Resource
    private DatasetVersionInfoService datasetVersionInfoService;

    @Resource
    private DatasetInfoMapper datasetInfoMapper;

    @Resource
    private DatasetInfoService datasetInfoService;

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Resource
    private GravitinoFilesetService gravitinoFilesetService;

    @Resource
    private IndexTaskService indexTaskService;

    @Resource
    private EsService esService;

    @Resource
    private PaiService paiService;

    @Resource
    private AdhocService adhocService;

    @Resource
    private DecontaminationTaskService decontaminationTaskService;

    @Resource
    private RedCityDataArkClient redCityDataArkClient;

    @Override
    public List<CalculateTokenListVO> getCnShCalculateTokenList() {
        List<DatasetVersionInfoDO> calculateTokenList = datasetVersionInfoService.getCnShNeedCalculateTokenList();
        return getCalculateTokenListVOS(calculateTokenList);
    }

    @Override
    public List<CalculateTokenListVO> getSgpShCalculateTokenList() {
        List<DatasetVersionInfoDO> calculateTokenList = datasetVersionInfoService.getSgpNeedCalculateTokenList();
        return getCalculateTokenListVOS(calculateTokenList);
    }

    @NotNull
    private List<CalculateTokenListVO> getCalculateTokenListVOS(List<DatasetVersionInfoDO> calculateTokenList) {
        List<Long> datasetIds = calculateTokenList.stream().map(DatasetVersionInfoDO::getDatasetId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(datasetIds)) {
            return Collections.emptyList();
        }
        List<DatasetInfoDO> datasetInfoDOS = datasetInfoService.selectByIds(datasetIds);
        Map<Long, DatasetInfoDO> datasetInfoDOMap = datasetInfoDOS.stream().collect(Collectors.toMap(DatasetInfoDO::getId, Function.identity()));
        return calculateTokenList.stream().map(it -> {
            CalculateTokenListVO calculateTokenListVO = new CalculateTokenListVO();
            calculateTokenListVO.setDatasetVersionId(it.getId());
            calculateTokenListVO.setOssPath(it.getOssPath());
            calculateTokenListVO.setMediaType(datasetInfoDOMap.get(it.getDatasetId()).getMediaType());
            return calculateTokenListVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer lockCalculateToken(LockCalculateTokenRequest request) {
        return datasetVersionInfoService.updateTokenTaskStatusCAS(request.getDatasetVersionId(), request.getTokenTaskSource()
                , TokenTaskStatus.RUNNING.getCode(), TokenTaskStatus.PENDING.getCode());
    }

    @Override
    public void saveTokenResult(SaveTokenResultRequest request) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(request.getDatasetVersionId());
        if (datasetVersionInfoDO == null) {
            return;
        }
        // 保存token结果
        datasetVersionInfoService.saveTokenResult(request.getDatasetVersionId(), request.getTokens(), request.getRecords(), request.getStatus());
        // 计算文件大小
        long size = OSSUtils.size(datasetVersionInfoDO.getOssPath());
        // 更新文件大小
        DatasetVersionInfoDO updateDO = new DatasetVersionInfoDO();
        updateDO.setId(request.getDatasetVersionId());
        updateDO.setSize(size);
        datasetVersionInfoService.updateById(updateDO);
    }

    @Override
    public InvertedIndexExportVO getInvertedIndexExportTask() {
        DatasetVersionInfoDO invertedIndexExportTask = datasetVersionInfoService.getInvertedIndexExportTask();
        if (invertedIndexExportTask == null) {
            return null;
        }

        DatasetInfoDO datasetInfoDO = datasetInfoService.selectById(invertedIndexExportTask.getDatasetId());

        String ossPath = invertedIndexExportTask.getOssPath();

        if (Region.AP_SOUTHEAST_1.getRegion().equalsIgnoreCase(invertedIndexExportTask.getRegion())) {
            ossPath = paiService.juiceSyncDstTmpPath(invertedIndexExportTask.getId(), false);
        }

        return InvertedIndexExportVO.builder()
                .datasetVersionId(invertedIndexExportTask.getId())
                .ossPath(ossPath)
                .indexName(esService.indexNameFormat(invertedIndexExportTask.getId()))
                .mediaType(datasetInfoDO.getMediaType())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer lockInvertedIndexExport(LockIndexExportRequest request) {
        int update = datasetVersionInfoService.updateInvertedIndexExportStatusCAS(request.getDatasetVersionId(), InvertedIndexState.EXPORTING.getCode(), InvertedIndexState.PENDING.getCode());
        if (update <= 0) {
            return update;
        }

        // 有更新
        IndexTaskDO indexTaskDO = new IndexTaskDO();
        indexTaskDO.setPlatform(PlatformType.dataverse.name());
        indexTaskDO.setIndexType(IndexType.INVERTED.getCode());
        indexTaskDO.setDatasetVersionId(request.getDatasetVersionId());
        indexTaskDO.setStatus("RUNNING");
        indexTaskDO.setStartTime(LocalDateTime.now());
        indexTaskDO.setTaskId(request.getIndexExportTaskSource());
        indexTaskService.saveIndexTask(indexTaskDO);
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInvertedIndexResult(SaveIndexResultRequest request) {
        LocalDateTime now = LocalDateTime.now();
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(request.getDatasetVersionId());
        if (datasetVersionInfoDO == null) {
            throw new ServiceException("dataset version not found, datasetVersionId: " + request.getDatasetVersionId());
        }
        IndexTaskDO indexTaskDO = indexTaskService.getRunningDataverseExportTask(request.getDatasetVersionId(), request.getIndexExportTaskSource());
        if (ObjectUtils.isEmpty(indexTaskDO)) {
            throw new ServiceException("dataverse running export task not found");
        }
        if ("success".equalsIgnoreCase(request.getStatus())) {
            datasetVersionInfoDO.setInvertedIndexState(InvertedIndexState.SUCCESS.getCode());
            datasetVersionInfoDO.setInvertedCluster(request.getCluster());
            indexTaskDO.setStatus("SUCCESS");
        } else {
            datasetVersionInfoDO.setInvertedIndexState(InvertedIndexState.FAILED.getCode());
            indexTaskDO.setStatus("FAILED");
            datasetVersionInfoDO.setInvertedErrorMsg("dataverse export task failed, indexTaskId: " + indexTaskDO.getId());
        }
        datasetVersionInfoDO.setUpdateTime(now);
        indexTaskDO.setEndTime(now);
        indexTaskDO.setUpdateTime(now);
        datasetVersionInfoService.updateById(datasetVersionInfoDO);
        indexTaskService.updateById(indexTaskDO);
    }

    @Override
    public VectorEmbeddingExportVO getVectorEmbeddingExportTask() {
        DatasetVersionInfoDO vectorEmbeddingReportTask = datasetVersionInfoService.getVectorEmbeddingReportTask();
        if (vectorEmbeddingReportTask == null) {
            return null;
        }
        IndexTaskDO indexTaskDO = indexTaskService.getLatestEmbeddingSuccessTaskOfDatasetVersion(vectorEmbeddingReportTask.getId());
        if (indexTaskDO == null) {
            log.error("获取向量导出任务失败，未找到对应的embedding任务，datasetVersionId: {}", vectorEmbeddingReportTask.getId());
            return null;
        }
        VectorEmbeddingExportVO vectorEmbeddingExportVO = new VectorEmbeddingExportVO();
        vectorEmbeddingExportVO.setDatasetVersionId(vectorEmbeddingReportTask.getId());
        vectorEmbeddingExportVO.setEmbeddingPath(indexTaskDO.getOutputPath());
        vectorEmbeddingExportVO.setDatasetVersionName(indexTaskDO.getDatasetVersionName());
        return vectorEmbeddingExportVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer lockVectorExport(LockIndexExportRequest request) {
        int update = datasetVersionInfoService.updateVectorExportStatusCAS(request.getDatasetVersionId(),
                VectorIndexState.EXPORTING.getCode(), VectorIndexState.EMBEDDED.getCode());
        if (update <= 0) {
            return update;
        }
        // 有更新
        IndexTaskDO indexTaskDO = new IndexTaskDO();
        indexTaskDO.setPlatform(PlatformType.dataverse.name());
        indexTaskDO.setIndexType(IndexType.VECTOR.getCode());
        indexTaskDO.setDatasetVersionId(request.getDatasetVersionId());
        indexTaskDO.setStatus("RUNNING");
        indexTaskDO.setStartTime(LocalDateTime.now());
        indexTaskDO.setTaskId(request.getIndexExportTaskSource());
        indexTaskService.saveIndexTask(indexTaskDO);
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVectorResult(SaveIndexResultRequest request) {
        LocalDateTime now = LocalDateTime.now();
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(request.getDatasetVersionId());
        if (datasetVersionInfoDO == null) {
            return;
        }
        // 获取对应的dataverse export任务
        IndexTaskDO indexTaskDO = indexTaskService.getRunningDataverseExportTask(request.getDatasetVersionId(), request.getIndexExportTaskSource());
        if ("success".equals(request.getStatus())) {
            // 向量索引构建成功
            datasetVersionInfoDO.setVectorIndexState(VectorIndexState.SUCCESS.getCode());
            indexTaskDO.setStatus("SUCCESS");
        } else {
            // 向量索引构建失败
            datasetVersionInfoDO.setVectorIndexState(VectorIndexState.FAILED.getCode());
            indexTaskDO.setStatus("FAILED");
            datasetVersionInfoDO.setVectorErrorMsg("dataverse export task failed, indexTaskId: " + indexTaskDO.getId());
        }
        datasetVersionInfoDO.setUpdateTime(now);
        indexTaskDO.setEndTime(now);
        indexTaskDO.setUpdateTime(now);
        datasetVersionInfoService.updateById(datasetVersionInfoDO);
        indexTaskService.updateById(indexTaskDO);
    }

    @SneakyThrows
    @Override
    public void initDataset(MultipartFile file) {
        String line;
        InputStream is = file.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        SimpleUser xinghai = new SimpleUser();
        xinghai.setUserId("zhangdongjie");
        xinghai.setDisplayName("星海(星海)");
        xinghai.setEmail("<EMAIL>");
        int i = 1;
        while ((line = br.readLine()) != null) {
            if (StringUtils.isNotBlank(line)) {
                String[] arr = line.split(",");
                DatasetInfoDO datasetInfoDO = new DatasetInfoDO();
                datasetInfoDO.setName(arr[0].trim());
                datasetInfoDO.setCode(arr[1].trim());
                datasetInfoDO.setDescription(arr[2].trim());
                datasetInfoDO.setMediaType(arr[3].trim());
                datasetInfoDO.setContentType(arr[4].trim());
                datasetInfoDO.setIsRaw(false);
                datasetInfoDO.setCreateBy(xinghai);
                datasetInfoDO.setUpdateBy(xinghai);
                datasetInfoDO.setOwner(xinghai);
                datasetInfoMapper.insert(datasetInfoDO);
                Long datasetId = datasetInfoDO.getId();
                DatasetVersionInfoDO datasetVersionInfoDO = new DatasetVersionInfoDO();
                datasetVersionInfoDO.setDatasetId(datasetId);
                datasetVersionInfoDO.setDescription(arr[6].trim());
                datasetVersionInfoDO.setDataType(arr[8].trim());
                String ossPath = arr[9].trim();
                if (ossPath.endsWith("/")) {
                    ossPath = ossPath.substring(0, ossPath.length() - 1);
                }
                datasetVersionInfoDO.setOssPath(ossPath);
                datasetVersionInfoDO.setFileType(arr[10].trim());
                datasetVersionInfoDO.setLanguage(arr[12].trim());
                datasetVersionInfoDO.setIsCustom(true);
                datasetVersionInfoDO.setDataSources("[]");
                datasetVersionInfoDO.setProcessingTasks(Collections.emptyList());
                datasetVersionInfoDO.setRedocLink(arr[13].trim());
                datasetVersionInfoDO.setStatus(DatasetVersionStatus.DEPLOYED.getStatus());
                if (ossPath.startsWith(OSSUtils.OSS_PREFIX)) {
                    datasetVersionInfoDO.setRegion(Region.CN_SHANGHAI.getRegion());
                } else if (ossPath.startsWith(OSSUtils.SGP_OSS_PREFIX)) {
                    datasetVersionInfoDO.setRegion(Region.AP_SOUTHEAST_1.getRegion());
                }
                datasetVersionInfoDO.setTokenTaskStatus(TokenTaskStatus.PENDING.getCode());
                Integer version = gravitinoFilesetService.createFileset(datasetInfoDO.getCode(), ossPath);
                datasetVersionInfoDO.setVersion(version);
                datasetVersionInfoDO.setCreateBy(xinghai);
                datasetVersionInfoDO.setUpdateBy(xinghai);
                datasetVersionInfoMapper.insert(datasetVersionInfoDO);
                datasetInfoDO.setMaxVersionId(datasetVersionInfoDO.getId());
                datasetInfoMapper.updateById(datasetInfoDO);
                log.info("第{}行数据导入成功", i++);
            }
        }
    }

    @Override
    public AdhocQueryResultVO adhocQuery(AdhocQueryRequest request) {
        SimpleUser user = new SimpleUser();
        user.setUserId("openapi-query");
        return adhocService.query(request, user);
    }

    @Override
    public List<DatasetDecontaminationTaskInfoVO> queryDatasetDecontaminationTasks() {
        List<DecontaminationTaskDO> decontaminationTasks = decontaminationTaskService.getRunningDecontaminationTasks();
        List<DatasetDecontaminationTaskInfoVO> taskInfoVOS = decontaminationTasks.stream().map(task -> {
            DatasetDecontaminationTaskInfoVO taskInfoVO = new DatasetDecontaminationTaskInfoVO();
            taskInfoVO.setTaskId(task.getTaskId());
            taskInfoVO.setFilePath(task.getFilePath());
            return taskInfoVO;
        }).collect(Collectors.toList());
        return taskInfoVOS;
    }

    @Override
    public Boolean dealDatasetDecontaminationTaskCallback(DatasetDecontaminationTaskCallbackRequest request) {
        DecontaminationTaskDO decontaminationTaskDO = decontaminationTaskService.getDecontaminationTaskByTaskId(request.getTaskId());

        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(decontaminationTaskDO.getDatasetVersionId());
        DatasetInfoDO datasetInfoDO = datasetInfoService.selectById(decontaminationTaskDO.getDatasetId());
        decontaminationTaskService.updateDecontaminationTask(request.getTaskId(), request.getStatus(), request.getErrorMsg());

        //通知创建人
        redCityDataArkClient.sendMarkdownToUserChat(datasetVersionInfoDO.getCreateBy().getEmail(), sendDatasetDecontaminationTaskFinishMsg(datasetInfoDO, datasetVersionInfoDO));


        return true;
    }

    private String sendDatasetDecontaminationTaskFinishMsg(DatasetInfoDO datasetInfoDO, DatasetVersionInfoDO datasetVersionInfoDO) {
        StringBuilder sb = new StringBuilder();
        String title = datasetInfoDO.getName() + "-V" + datasetVersionInfoDO.getVersion();
        sb.append("【").append(title).append("】去污染完成通知\n");
        sb.append("数据集: ").append(datasetInfoDO.getName()).append("\n");
        sb.append("版本: ").append(datasetVersionInfoDO.getVersion()).append("\n");
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        sb.append("完成时间: ").append(time).append("\n");
        return sb.toString();
    }


}
