package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.red.data.dg.shaded.fastjson2.JSONObject;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/7/14 11:04
 */

@Data
public class AdhocQueryProgressVO {

    private Long queryHistoryId;

    private String queryId;

    private String queryName;

    private String state;

    private String error;

    private Double progress;

    private Map<String, Object> progressInfo;

    private String executedSql;

    private boolean requireAuth;

    private String appId;

    private String engineType;

    private String queueMessage;

    private JSONObject queueInfo;

    public boolean finished() {
        return "FINISHED".equals(state) && progress >= 100;
    }

    public boolean errorState() {
        //return Arrays.asList("CANCELLED", "STOPPED", "KILLED").contains(state);
        return Arrays.asList("CANCELLED", "STOPPED", "KILLED", "ERROR").contains(state);
    }

}
