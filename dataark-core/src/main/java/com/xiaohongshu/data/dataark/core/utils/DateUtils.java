package com.xiaohongshu.data.dataark.core.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @email ying<PERSON><EMAIL>
 * @date 2025/4/21
 */
public class DateUtils {

    public static final String YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD_HHMM = "yyyy-MM-dd HH:mm";

    /**
     * localDateTime转str
     * @param date 日期
     * @param pattern 格式
     * @return str
     */
    public static String format(LocalDateTime date, String pattern) {
        DateTimeFormatter simpleDateFormat = DateTimeFormatter.ofPattern(pattern);
        return date.format(simpleDateFormat);
    }

}
