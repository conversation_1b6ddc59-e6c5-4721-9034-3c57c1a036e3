package com.xiaohongshu.data.dataark.core.service.impl;

import com.xiaohongshu.data.dataark.core.common.enums.AuditStatus;
import com.xiaohongshu.data.dataark.core.common.enums.OaFormType;
import com.xiaohongshu.data.dataark.core.service.ApproveRecordService;
import com.xiaohongshu.data.dataark.core.service.ApproveService;
import com.xiaohongshu.data.dataark.core.service.BinidxService;
import com.xiaohongshu.data.dataark.dao.entity.ApproveRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/19
 */
@Service
@Slf4j
public class ApproveServiceImpl implements ApproveService {

    @Resource
    private BinidxService binidxService;

    @Resource
    private ApproveRecordService approveRecordService;


    @Override
    public void approvePass(ApproveRecordDO recordDO) {
        if (AuditStatus.FINISH_STATUS.contains(recordDO.getApproveStatus())) {
            log.info("审批已经结束，不再重复处理: {}", recordDO.getApproveFormId());
            return;
        }
        switch (OaFormType.valueOf(recordDO.getApproveType())) {
            case DATASET_PUBLISH_BINIDX:
                binidxService.approvePass(recordDO);
                break;
            default:
                log.warn("不支持的审批类型: {}", recordDO.getApproveType());
                break;
        }
        // 更新审批状态
        approveRecordService.updateApproveStatusByApproveFormId(recordDO.getApproveFormId(), AuditStatus.AUDIT_PASS.name());
    }

    @Override
    public void approveRefuse(ApproveRecordDO recordDO, String auditStatus) {
        if (AuditStatus.FINISH_STATUS.contains(recordDO.getApproveStatus())) {
            log.info("审批已经结束，不再重复处理: {}", recordDO.getApproveFormId());
            return;
        }
        switch (OaFormType.valueOf(recordDO.getApproveType())) {
            case DATASET_PUBLISH_BINIDX:
                binidxService.approveRefuse(recordDO);
                break;
            default:
                log.warn("不支持的审批类型: {}", recordDO.getApproveType());
                break;
        }
        // 更新审批状态
        approveRecordService.updateApproveStatusByApproveFormId(recordDO.getApproveFormId(), auditStatus);
    }
}
