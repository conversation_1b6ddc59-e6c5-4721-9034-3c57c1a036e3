package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.enums.OaFormType;
import com.xiaohongshu.data.dataark.dao.entity.ApproveRecordDO;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

/**
 * <AUTHOR>
 * @email y<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/19
 */
public interface ApproveRecordService {

    ApproveRecordDO buildPublishBinidxRecord(String formId, OaFormType oaFormType, Long datasetVersionId, Long binidxTaskDOId, SimpleUser user);

    void insertRecord(ApproveRecordDO approveRecordDO);

    ApproveRecordDO selectByApproveFormId(String formNo);

    void updateApproveStatusByApproveFormId(String formNo, String auditStatus);

    ApproveRecordDO getLatestApproveRecord(Long datasetVersionId);
}
