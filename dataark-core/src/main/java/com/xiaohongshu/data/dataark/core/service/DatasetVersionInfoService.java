package com.xiaohongshu.data.dataark.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionInfoDetailDO;

import java.util.List;

/**
 * <AUTHOR>
 * @email yingyu<PERSON>@xiaohongshu.com
 * @date 2025/4/23
 */
public interface DatasetVersionInfoService {
    boolean existNotDraftVersion(Long datasetId);

    List<DatasetVersionInfoDO> getCnShNeedCalculateTokenList();

    List<DatasetVersionInfoDO> getSgpNeedCalculateTokenList();

    int updateTokenTaskStatusCAS(Long datasetVersionId, String tokenTaskSource, String newStatus, String oldStatus);

    int saveTokenResult(Long datasetVersionId, Long tokens, Long records, String status);

    DatasetVersionInfoDO selectById(Long datasetVersionId);

    List<DatasetVersionInfoDO> selectByIds(List<Long> datasetVersionIds);

    int updateById(DatasetVersionInfoDO updateDO);

    DatasetVersionInfoDO selectNoDraftByOssPath(String ossPath);

    DatasetVersionInfoDO selectNoDraftByOssPathAndId(String ossPath, Long id);

    List<DatasetVersionInfoDO> selectNoDraftByDatasetId(Long datasetId);

    IPage<DatasetVersionInfoDetailDO> adhocListPage(String name, Integer version, String mediaType, String contentType,
                                                    String label, String userId, boolean queryInverted, boolean queryVector,
                                                    Integer pageIndex, Integer pageSize);

    List<DatasetVersionInfoDetailDO> selectDetailByIds(List<Long> datasetVersionIds);

    int updateVectorEmbeddingSuccess(Long datasetVersionId, String newStatus, String oldStatus);

    int updateVectorEmbeddingFailed(Long datasetVersionId, String newStatus, String errorMsg, String oldStatus);

    DatasetVersionInfoDO getVectorEmbeddingReportTask();

    DatasetVersionInfoDO getInvertedIndexExportTask();

    DatasetVersionInfoDO getPendingSnapshotTask();

    int updateVectorExportStatusCAS(Long datasetVersionId, String newStatus, String oldStatus);

    int updateInvertedIndexExportStatusCAS(Long datasetVersionId, String newStatus, String oldStatus);
}
