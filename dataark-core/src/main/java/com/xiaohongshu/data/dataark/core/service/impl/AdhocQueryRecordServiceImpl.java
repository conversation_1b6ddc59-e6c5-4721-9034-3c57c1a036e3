package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.core.service.AdhocQueryRecordService;
import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;
import com.xiaohongshu.data.dataark.dao.mapper.AdhocQueryRecordMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/10
 */
@Service
public class AdhocQueryRecordServiceImpl implements AdhocQueryRecordService {

    @Resource
    private AdhocQueryRecordMapper adhocQueryRecordMapper;

    @Override
    public int insert(AdhocQueryRecordDO adhocQueryRecordDO) {
        return adhocQueryRecordMapper.insert(adhocQueryRecordDO);
    }

    @Override
    public IPage<AdhocQueryRecordDO> getAdhocQueryRecordPage(Long adhocId, List<String> queryTypes, Integer pageIndex, Integer pageSize) {
        Page<AdhocQueryRecordDO> page = new Page<>(pageIndex, pageSize);
        return adhocQueryRecordMapper.selectPage(page, adhocId, queryTypes);
    }

    @Override
    public AdhocQueryRecordDO selectById(Long adhocQueryRecordId) {
        return adhocQueryRecordMapper.selectById(adhocQueryRecordId);
    }

    @Override
    public void updateStateById(Long id, String state) {
        AdhocQueryRecordDO adhocQueryRecordDO = new AdhocQueryRecordDO();
        adhocQueryRecordDO.setId(id);
        adhocQueryRecordDO.setState(state);
        adhocQueryRecordMapper.updateById(adhocQueryRecordDO);
    }


}
