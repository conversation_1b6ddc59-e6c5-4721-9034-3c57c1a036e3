package com.xiaohongshu.data.dataark.core.common.model;

import com.xiaohongshu.data.dataark.core.common.enums.ContentType;
import com.xiaohongshu.data.dataark.core.common.enums.MediaType;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import lombok.Getter;

import java.util.Objects;

@Getter
public class DatasetType {
    private final MediaType mediaType;
    private final ContentType contentType;

    public DatasetType(MediaType mediaType, ContentType contentType) {
        this.mediaType = mediaType;
        this.contentType = contentType;
    }

    public static DatasetType of(String mediaTypeNameCn, String contentTypeNameCn) {
        MediaType mediaType = MediaType.getByNameCn(mediaTypeNameCn);
        if (Objects.isNull(mediaType)) {
            throw new ServiceException("无效的数据集类型");
        }
        ContentType contentType = ContentType.getByMediaTypeAndCnName(mediaType, contentTypeNameCn);
        if (Objects.isNull(contentType)) {
            throw new ServiceException("无效的数据集内容类型");
        }
        return new DatasetType(mediaType, contentType);
    }
}
