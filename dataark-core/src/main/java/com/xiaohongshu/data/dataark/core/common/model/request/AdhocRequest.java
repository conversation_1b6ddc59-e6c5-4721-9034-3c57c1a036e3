package com.xiaohongshu.data.dataark.core.common.model.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/9
 */
@Data
public class AdhocRequest {

    private Long adhocId;

    private List<Long> adhocIds;

    private String name;

    private String queryType;

    private List<Long> datasetVersionIds;

    private String condition;

    private Long rows;

    private Long folderId;

}
