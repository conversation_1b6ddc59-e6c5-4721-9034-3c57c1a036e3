package com.xiaohongshu.data.dataark.core.schedule;

import com.xiaohongshu.data.dataark.core.common.enums.QueryProgressState;
import com.xiaohongshu.data.dataark.core.service.QueryService;
import com.xiaohongshu.data.dataark.dao.entity.QueryRecordDO;
import com.xiaohongshu.data.dataark.dao.mapper.QueryRecordMapper;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UpdateQueryRecord {

    @Resource
    private QueryService queryService;

    @Resource
    private QueryRecordMapper queryRecordMapper;

    @RedSchedule(value = "sync-query-state", cron = "0/15 * * * * ?", desc = "更新查询任务状态", autoFillAppid = true)
    public void updateQueryRecord() {
        List<QueryRecordDO> runningRecords = queryRecordMapper.selectByStateWithLimit(QueryProgressState.RUNNING.name(), 100);
        log.info("running query records: {}", runningRecords.stream().map(QueryRecordDO::getId).collect(Collectors.toList()));
        for (QueryRecordDO queryRecordDO : runningRecords) {
            queryService.updateQueryStatus(queryRecordDO.getId());
        }
    }
}
