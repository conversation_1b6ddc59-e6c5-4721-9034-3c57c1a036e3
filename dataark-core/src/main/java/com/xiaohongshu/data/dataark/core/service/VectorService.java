package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.AdhocQueryRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.VectorResultVO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import io.milvus.v2.service.vector.response.SearchResp;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/13
 */
public interface VectorService {

    List<VectorResultVO> query(AdhocQueryRequest request, List<DatasetVersionInfoDO> datasetVersionInfoDOS);

    void dropCollection(String vectorDatasetVersionName);
}
