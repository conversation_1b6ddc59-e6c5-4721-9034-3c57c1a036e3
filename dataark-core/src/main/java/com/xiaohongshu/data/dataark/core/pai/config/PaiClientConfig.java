package com.xiaohongshu.data.dataark.core.pai.config;

import com.aliyun.pai_dlc20201203.Client;
import com.aliyun.teaopenapi.models.Config;
import com.xiaohongshu.sec.kms.sdk.KmsClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.xiaohongshu.data.dataark.core.common.constant.Constants.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/17
 */
@Configuration
public class PaiClientConfig {

    @Bean
    @Qualifier("paiClient")
    public Client createClient() throws Exception {
        String accessKeyId = KmsClient.getSecretValue(KMS_KEY_ID);
        String accessKey = KmsClient.getSecretValue(KMS_KEY);
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        Config config = new Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(accessKeyId)
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(accessKey);
        // Endpoint 请参考 https://api.aliyun.com/product/pai-dlc
        config.endpoint = PAI_SGP_ENDPOINT;
        return new Client(config);
    }

}
