package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.User;
import com.xiaohongshu.dataverse.common.pojo.SimpleEmployee;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

import java.util.List;

/**
 * <AUTHOR>
 * @email y<PERSON><PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/28
 */
public interface UserService {
    List<SimpleEmployee> search(String key);

    User userInfo(SimpleUser simpleUser);
}
