package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetWrapFlattenVO {
    private Long id;

    private String name;

    private String mediaType;

    private String contentType;

    private Boolean isRaw;

    private Long maxVersionId;

    private String status;

    private String language;

    private SimpleUser owner;

    private String statistics;

    private String createTime;
}
