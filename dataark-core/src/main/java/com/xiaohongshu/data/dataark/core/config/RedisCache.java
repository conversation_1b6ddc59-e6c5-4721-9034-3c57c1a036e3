package com.xiaohongshu.data.dataark.core.config;

import kotlin.Suppress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/7/10 17:48
 */

@Slf4j
@Component
public class RedisCache {

    @Resource(name = "dataark")
    private Jedis dataarkJedisPool;

    private final static String WEB_DATA_PREFIX = "web_data:";
    private final static int WEB_DATA_EXPIRE_SECONDS = 185 * 24 * 60 * 60;


    private final static String WEB_DATA_RESULT_PREFIX = "web_data_result:";
    private final static int WEB_DATA_RESULT_EXPIRE_SECONDS = 185 * 24 * 60 * 60;

    public String getWebData(String key, Integer index) {
        if (key == null || key.isEmpty()) {
            return null;
        }
        String redisKey = getEvn() + "_" + WEB_DATA_PREFIX + key + "_" + index;
        return dataarkJedisPool.get(redisKey);
    }

    public void setWebData(String key, Integer index, String value) {
        if (key == null || key.isEmpty() || value == null || value.isEmpty()) {
            return;
        }
        String redisKey = getEvn() + "_" + WEB_DATA_PREFIX + key + "_" + index;
        dataarkJedisPool.setex(redisKey, WEB_DATA_EXPIRE_SECONDS, value);
    }


    public String getWebDataResult(String key) {
        if (key == null || key.isEmpty()) {
            return null;
        }
        String redisKey = getEvn() + "_" + WEB_DATA_RESULT_PREFIX + key;
        return dataarkJedisPool.get(redisKey);
    }

    public void setWebDataResult(String key, String value) {
        if (key == null || key.isEmpty() || value == null || value.isEmpty()) {
            return;
        }
        String redisKey = getEvn() + "_" + WEB_DATA_RESULT_PREFIX + key;
        dataarkJedisPool.setex(redisKey, WEB_DATA_RESULT_EXPIRE_SECONDS, value);
    }


    private String getEvn() {
        String env = System.getProperty("spring.profiles.active");
        if ("local".equals(env)) {
            env = "sit";
        }
        if ("prod".equals(env)) {
            env = "pro";
        }
        if ("staging".equals(env)) {
            env = "beta";
        }
        return env;
    }


}
