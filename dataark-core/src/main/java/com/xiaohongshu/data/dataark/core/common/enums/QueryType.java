package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

@Getter
public enum QueryType {
    //preview或sample

    PREVIEW("preview"),
    SAMPLE("sample"),
    SAMPLE_PREVIEW("sample_preview"),
    SAMPLE_DOWNLOAD("sample_download"),
    ;

    private final String value;

    QueryType(String value) {
        this.value = value;
    }

    public static QueryType of(String value) {
        for (QueryType queryType : values()) {
            if (queryType.value.equalsIgnoreCase(value)) {
                return queryType;
            }
        }
        return null;
    }
}
