package com.xiaohongshu.data.dataark.core.gravitino.config;

import org.apache.gravitino.client.GravitinoClient;
import org.apache.gravitino.file.FilesetCatalog;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/3/25
 */
@Configuration
public class GravitinoConfig {

    @Value("${third_party.gravitino.url}")
    private String gravitinoUrl;

    @Value("${third_party.gravitino.metalake}")
    private String metalake;

    @Value("${third_party.gravitino.catalog}")
    private String catalog;

    @Value("${third_party.gravitino.catalog_sgp}")
    private String catalogSgp;

    @Value("${third_party.gravitino.username}")
    private String username;

    @Value("${third_party.gravitino.password}")
    private String password;

    @Bean
    @Qualifier("gravitinoClient")
    public GravitinoClient gravitinoClient() {
        return GravitinoClient.builder(gravitinoUrl)
                .withMetalake(metalake)
                .withRedAuth(username, password).build();
    }

    @Bean
    @Qualifier("filesetCatalog")
    public FilesetCatalog filesetCatalog() {
        return gravitinoClient().loadCatalog(catalog).asFilesetCatalog();
    }

    @Bean
    @Qualifier("filesetCatalogSgp")
    public FilesetCatalog filesetCatalogSgp() {
        return gravitinoClient().loadCatalog(catalogSgp).asFilesetCatalog();
    }

}
