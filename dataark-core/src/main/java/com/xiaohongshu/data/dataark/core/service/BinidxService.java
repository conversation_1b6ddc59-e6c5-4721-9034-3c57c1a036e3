package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.core.common.model.request.BinidxPublishTaskRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxSampleTaskRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.BinidxTaskListVO;
import com.xiaohongshu.data.dataark.core.pai.pojo.RegionConfig;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxGenerateScriptRequest;
import com.xiaohongshu.data.dataark.dao.entity.ApproveRecordDO;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/18
 */
public interface BinidxService {
    List<String> getCpfsType();

    Map<String, RegionConfig> getPaiResourceConfig();

    /**
     * 生成binidx脚本
     * @param req
     * @return
     */
    String generateScript(BinidxGenerateScriptRequest req);

    /**
     * 采样生成binidx任务
     * @param req
     * @param user
     * @return
     */
    String sampleGenerateTask(BinidxSampleTaskRequest req, SimpleUser user);

    /**
     * 发布生成binidx任务
     * @param req
     * @param user
     * @return
     */
    String publishGenerateTask(BinidxPublishTaskRequest req, SimpleUser user);

    void approvePass(ApproveRecordDO recordDO);

    void approveRefuse(ApproveRecordDO recordDO);

    PageResult<BinidxTaskListVO> list(Long datasetVersionId, Integer pageIndex, Integer pageSize);

    List<BinidxTaskDO> searchSampleAllRecord(Long datasetVersionId);
}
