package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.xiaohongshu.data.dataark.dao.entity.dataset.DataSource;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetMetadata;
import com.xiaohongshu.data.dataark.dao.entity.dataset.ProcessingTask;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DatasetVersionInfoVO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 数据集id
     */
    private Long datasetId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 标签
     */
    private String label;

    /**
     * 数据类型，支持oss和iceberg
     */
    private String dataType;

    /**
     * oss 路径、格式或者iceberg 库表名
     */
    private DatasetMetadata metadata;

    /**
     * 快照执行状态
     */
    private String snapshotStatus;

    /**
     * 快照文件路径
     */
    private String snapshotPath;

    /**
     * 语言, 中文、英文、代码、混合
     */
    private String language;

    /**
     * 版本描述
     */
    private String description;

    /**
     * 标识是否使用自定义模式（true=展示 Redoc 链接，false=平台模版）
     */
    private Boolean isCustom;

    /**
     * 仅当 is_custom=true 时必填，内嵌 Redoc 文档链接
     */
    private String redocLink;

    /**
     * 原始数据来源
     */
    private List<DataSource> dataSources;

    /**
     * 数据处理过程
     */
    private List<ProcessingTask> processingTasks;

    /**
     * 数据漏斗说明文档
     */
    private String dataFunnel;

    /**
     * 数据校验说明文档
     */
    private String dataValidation;

    /**
    实验验证说明文档
     */
    private String experimentValidation;

    /**
     * 数据验证报告
     */
    private String verificationReport;

    /**
     * 是否需要去污染
     */
    private Boolean needDecontamination;

    /**
     * 去污染任务状态
     */
    private String decontaminationStatus;

    /**
     * 去污染任务错误信息
     */
    private String decontaminationErrorMsg;

    /**
     * oa链接
     */
    private String oaUrl;

    private String region;

    private Long tokens;

    private Long size;

    private Long records;

    private String dbTableName;

    private String status;

    private String invertedIndexState;

    private String vectorIndexState;

    private SimpleUser createBy;

    private SimpleUser updateBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
