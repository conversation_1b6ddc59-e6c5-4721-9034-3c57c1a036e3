package com.xiaohongshu.data.dataark.core.common.model.request;

import com.xiaohongshu.data.dataark.dao.entity.AdhocQueryRecordDO;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/9
 */
@Data
public class AdhocQueryRequest {

    private Long adhocId;

    private String queryType;

    private List<Long> datasetVersionIds;

    private String condition;

    private Long rows;

    private boolean addQueryRecord;

//    private Integer pageIndex;
//
//    private Integer pageSize;

    public static AdhocQueryRecordDO convert2AdhocQueryRecordDO(AdhocQueryRequest request) {
        AdhocQueryRecordDO adhocQueryRecordDO = new AdhocQueryRecordDO();
        adhocQueryRecordDO.setAdhocId(request.getAdhocId());
        adhocQueryRecordDO.setQueryType(request.getQueryType());
        adhocQueryRecordDO.setDatasetVersionIds(JsonUtil.toString(request.getDatasetVersionIds()));
        adhocQueryRecordDO.setCondition(request.getCondition().trim());
        adhocQueryRecordDO.setRows(request.getRows());
        return adhocQueryRecordDO;
    }
}
