package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.core.common.enums.*;
import com.xiaohongshu.data.dataark.core.service.DatasetVersionInfoService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.dataset.DatasetVersionInfoDetailDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/23
 */
@Service
public class DatasetVersionInfoServiceImpl implements DatasetVersionInfoService {

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Override
    public boolean existNotDraftVersion(Long datasetId) {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getDatasetId, datasetId)
                .ne(DatasetVersionInfoDO::getStatus, DatasetVersionStatus.DRAFT.getStatus())
                .last("LIMIT 1");
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectOne(wrapper);
        return datasetVersionInfoDO != null;
    }

    @Override
    public List<DatasetVersionInfoDO> getCnShNeedCalculateTokenList() {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getTokenTaskStatus, TokenTaskStatus.PENDING.getCode())
                .ne(DatasetVersionInfoDO::getStatus, DatasetVersionStatus.DRAFT.getStatus())
                .eq(DatasetVersionInfoDO::getRegion, Region.CN_SHANGHAI.getRegion())
                .last("LIMIT 50");
        return datasetVersionInfoMapper.selectList(wrapper);
    }

    @Override
    public List<DatasetVersionInfoDO> getSgpNeedCalculateTokenList() {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getTokenTaskStatus, TokenTaskStatus.PENDING.getCode())
                .ne(DatasetVersionInfoDO::getStatus, DatasetVersionStatus.DRAFT.getStatus())
                .eq(DatasetVersionInfoDO::getRegion, Region.AP_SOUTHEAST_1.getRegion())
                .last("LIMIT 50");
        return datasetVersionInfoMapper.selectList(wrapper);
    }

    @Override
    public int updateTokenTaskStatusCAS(Long datasetVersionId, String tokenTaskSource, String newStatus, String oldStatus) {
        return datasetVersionInfoMapper.updateTokenTaskStatusCAS(datasetVersionId, tokenTaskSource, newStatus, oldStatus);
    }

    @Override
    public int saveTokenResult(Long datasetVersionId, Long tokens, Long records, String status) {
        return datasetVersionInfoMapper.saveTokenResult(datasetVersionId, tokens, records, status);
    }

    @Override
    public DatasetVersionInfoDO selectById(Long datasetVersionId) {
        return datasetVersionInfoMapper.selectById(datasetVersionId);
    }

    @Override
    public List<DatasetVersionInfoDO> selectByIds(List<Long> datasetVersionIds) {
        if (CollectionUtils.isEmpty(datasetVersionIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .in(DatasetVersionInfoDO::getId, datasetVersionIds);
        return datasetVersionInfoMapper.selectList(wrapper);
    }

    @Override
    public int updateById(DatasetVersionInfoDO updateDO) {
        return datasetVersionInfoMapper.updateById(updateDO);
    }

    @Override
    public DatasetVersionInfoDO selectNoDraftByOssPath(String ossPath) {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getOssPath, ossPath)
                .ne(DatasetVersionInfoDO::getStatus, DatasetVersionStatus.DRAFT.getStatus())
                .last("LIMIT 1");
        return datasetVersionInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetVersionInfoDO selectNoDraftByOssPathAndId(String ossPath, Long id) {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getOssPath, ossPath)
                .ne(DatasetVersionInfoDO::getId, id)
                .ne(DatasetVersionInfoDO::getStatus, DatasetVersionStatus.DRAFT.getStatus())
                .last("LIMIT 1");
        return datasetVersionInfoMapper.selectOne(wrapper);
    }

    @Override
    public List<DatasetVersionInfoDO> selectNoDraftByDatasetId(Long datasetId) {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getDatasetId, datasetId)
                .ne(DatasetVersionInfoDO::getStatus, DatasetVersionStatus.DRAFT.getStatus());
        return datasetVersionInfoMapper.selectList(wrapper);
    }

    @Override
    public IPage<DatasetVersionInfoDetailDO> adhocListPage(String name, Integer version, String mediaType, String contentType,
                                                     String label, String userId, boolean queryInverted, boolean queryVector,
                                                     Integer pageIndex, Integer pageSize) {
        Page<DatasetVersionInfoDetailDO> page = new Page<>(pageIndex, pageSize);
        return datasetVersionInfoMapper.adhocListPage(page, name, version, mediaType,
                contentType, label, userId, queryInverted, queryVector);
    }

    @Override
    public List<DatasetVersionInfoDetailDO> selectDetailByIds(List<Long> datasetVersionIds) {
        if (CollectionUtils.isEmpty(datasetVersionIds)) {
            return Collections.emptyList();
        }
        return datasetVersionInfoMapper.selectDetailByIds(datasetVersionIds);
    }

    @Override
    public int updateVectorEmbeddingSuccess(Long datasetVersionId, String newStatus, String oldStatus) {
        return datasetVersionInfoMapper.updateVectorEmbeddingSuccess(datasetVersionId, newStatus, oldStatus);
    }

    @Override
    public int updateVectorEmbeddingFailed(Long datasetVersionId, String newStatus, String errorMsg, String oldStatus) {
        return datasetVersionInfoMapper.updateVectorEmbeddingFailed(datasetVersionId, newStatus, errorMsg, oldStatus);
    }

    @Override
    public DatasetVersionInfoDO getVectorEmbeddingReportTask() {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getVectorIndexState, VectorIndexState.EMBEDDED.getCode())
                .last("LIMIT 1");
        return datasetVersionInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetVersionInfoDO getInvertedIndexExportTask() {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getInvertedIndexState, InvertedIndexState.PENDING.getCode())
                .last("LIMIT 1");
        return datasetVersionInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetVersionInfoDO getPendingSnapshotTask() {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getSnapshotStatus, InvertedIndexState.PENDING.getCode())
                .last("LIMIT 1");
        return datasetVersionInfoMapper.selectOne(wrapper);
    }

    @Override
    public int updateVectorExportStatusCAS(Long datasetVersionId, String newStatus, String oldStatus) {
        return datasetVersionInfoMapper.updateVectorExportStatusCAS(datasetVersionId, newStatus, oldStatus);
    }

    @Override
    public int updateInvertedIndexExportStatusCAS(Long datasetVersionId, String newStatus, String oldStatus) {
        return datasetVersionInfoMapper.updateInvertedIndexExportStatusCAS(datasetVersionId, newStatus, oldStatus);
    }
}
