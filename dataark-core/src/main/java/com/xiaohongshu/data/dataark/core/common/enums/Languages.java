package com.xiaohongshu.data.dataark.core.common.enums;

public enum Languages {
    // 中文、英文、代码、混合
    CHINESE("CHINESE", "中文"),
    ENGLISH("ENGLISH", "英文"),
    CODE("CODE", "代码"),
    MIX("MIX", "混合");

    private String code;
    private String nameCn;

    Languages(String code, String nameCn) {
        this.code = code;
        this.nameCn = nameCn;
    }

    public String getCode() {
        return code;
    }

    public String getNameCn() {
       return nameCn;
    }
}
