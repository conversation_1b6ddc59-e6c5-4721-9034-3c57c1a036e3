package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.data.dataark.core.service.DatasetInfoService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/30
 */
@Service
public class DatasetInfoServiceImpl implements DatasetInfoService {

    @Resource
    private DatasetInfoMapper datasetInfoMapper;

    @Override
    public DatasetInfoDO selectNoDraftByName(String name) {
        LambdaQueryWrapper<DatasetInfoDO> wrapper = Wrappers.<DatasetInfoDO>lambdaQuery()
                .eq(DatasetInfoDO::getName, name)
                .isNotNull(DatasetInfoDO::getMaxVersionId)
                .last("limit 1");
        return datasetInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetInfoDO selectNoDraftByCode(String code) {
        LambdaQueryWrapper<DatasetInfoDO> wrapper = Wrappers.<DatasetInfoDO>lambdaQuery()
                .eq(DatasetInfoDO::getCode, code)
                .isNotNull(DatasetInfoDO::getMaxVersionId)
                .last("limit 1");
        return datasetInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetInfoDO selectNoDraftByNameAndId(String name, Long id) {
        LambdaQueryWrapper<DatasetInfoDO> wrapper = Wrappers.<DatasetInfoDO>lambdaQuery()
                .eq(DatasetInfoDO::getName, name)
                .ne(DatasetInfoDO::getId, id)
                .isNotNull(DatasetInfoDO::getMaxVersionId)
                .last("limit 1");
        return datasetInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetInfoDO selectNoDraftByCodeAndId(String code, Long id) {
        LambdaQueryWrapper<DatasetInfoDO> wrapper = Wrappers.<DatasetInfoDO>lambdaQuery()
                .eq(DatasetInfoDO::getCode, code)
                .ne(DatasetInfoDO::getId, id)
                .isNotNull(DatasetInfoDO::getMaxVersionId)
                .last("limit 1");
        return datasetInfoMapper.selectOne(wrapper);
    }

    @Override
    public DatasetInfoDO selectById(Long id) {
        return datasetInfoMapper.selectById(id);
    }

    @Override
    public List<DatasetInfoDO> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DatasetInfoDO> wrapper = Wrappers.<DatasetInfoDO>lambdaQuery()
                .in(DatasetInfoDO::getId, ids);
        return datasetInfoMapper.selectList(wrapper);
    }
}
