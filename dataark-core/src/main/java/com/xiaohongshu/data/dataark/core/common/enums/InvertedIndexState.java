package com.xiaohongshu.data.dataark.core.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @email ying<PERSON><EMAIL>
 * @date 2025/6/6
 */
@Getter
public enum InvertedIndexState {

    NONE("none", "未创建"),
    WAIT_FOR_SYNC("wait_for_sync", "等待同步"),
    PENDING("pending", "等待创建"),
    EXPORTING("exporting", "导出中"),
    SUCCESS("success", "创建成功"),
    FAILED("failed", "创建失败"),;

    private final String code;
    private final String desc;

    InvertedIndexState(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final List<String> ALLOW_CREATE_INDEX_STATUS = Lists.newArrayList(NONE.getCode(), FAILED.getCode());


}
