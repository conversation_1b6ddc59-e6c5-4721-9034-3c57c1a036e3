package com.xiaohongshu.data.dataark.core.service.impl;

import com.xiaohongshu.data.dataark.core.service.DatasetExploreTaskService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetExploreTaskDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetExploreTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DatasetExploreTaskServiceImpl implements DatasetExploreTaskService {
    @Resource
    private DatasetExploreTaskMapper datasetExploreTaskMapper;

    @Override
    public boolean createExploreTask(Long datasetId, Long datasetVersionId, String metaName) {
        DatasetExploreTaskDO datasetExploreTaskDO = new DatasetExploreTaskDO();
        datasetExploreTaskDO.setDatasetId(datasetId);
        datasetExploreTaskDO.setDatasetVersionId(datasetVersionId);
        datasetExploreTaskDO.setMetaName(metaName);
        datasetExploreTaskDO.setStatus("RUNNING");
        datasetExploreTaskMapper.insert(datasetExploreTaskDO);
        return true;
    }

    @Override
    public boolean createExploreTaskBatch(Long datasetId, Long datasetVersionId, List<String> metaNames) {
        if (CollectionUtils.isEmpty(metaNames)) {
            log.warn("metaNames is empty, skip batch insert");
            return false;
        }

        List<DatasetExploreTaskDO> taskList = new ArrayList<>();
        for (String metaName : metaNames) {
            DatasetExploreTaskDO datasetExploreTaskDO = new DatasetExploreTaskDO();
            datasetExploreTaskDO.setDatasetId(datasetId);
            datasetExploreTaskDO.setDatasetVersionId(datasetVersionId);
            datasetExploreTaskDO.setMetaName(metaName);
            datasetExploreTaskDO.setStatus("RUNNING");
            taskList.add(datasetExploreTaskDO);
        }

        int result = datasetExploreTaskMapper.insertBatch(taskList);
        if (result != metaNames.size()) {
            log.error("Batch insert explore tasks failed, datasetId: {}, datasetVersionId: {}, count: {}, result: {}",
                    datasetId, datasetVersionId, metaNames.size(), result);
            throw new RuntimeException("Batch insert explore tasks failed");
        }
        log.info("Batch insert explore tasks, datasetId: {}, datasetVersionId: {}, count: {}, result: {}",
                datasetId, datasetVersionId, metaNames.size(), result);
        return true;
    }
}
