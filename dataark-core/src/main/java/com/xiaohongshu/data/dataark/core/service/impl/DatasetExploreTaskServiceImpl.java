package com.xiaohongshu.data.dataark.core.service.impl;

import com.xiaohongshu.data.dataark.core.service.DatasetExploreTaskService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetExploreTaskDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetExploreTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DatasetExploreTaskServiceImpl implements DatasetExploreTaskService {
    @Resource
    private DatasetExploreTaskMapper datasetExploreTaskMapper;

    @Override
    public boolean createExploreTask(Long datasetId, Long datasetVersionId, String metaName) {
        DatasetExploreTaskDO datasetExploreTaskDO = new DatasetExploreTaskDO();
        datasetExploreTaskDO.setDatasetId(datasetId);
        datasetExploreTaskDO.setDatasetVersionId(datasetVersionId);
        datasetExploreTaskDO.setMetaName(metaName);
        datasetExploreTaskDO.setStatus("RUNNING");
        datasetExploreTaskMapper.insert(datasetExploreTaskDO);
        return false;
    }
}
