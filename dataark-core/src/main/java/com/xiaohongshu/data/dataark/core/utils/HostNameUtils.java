package com.xiaohongshu.data.dataark.core.utils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Collections;
import java.util.Enumeration;

/**
 * 主机相关细心工具类
 */
public class HostNameUtils {

    /**
     * 获取主机信息对象
     * @return inet对象
     */
    public static InetAddress getInetAddress() {
        try {
            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            for (NetworkInterface network : Collections.list(nets)) {
                Enumeration<InetAddress> inetAddresses = network.getInetAddresses();
                for (InetAddress inetAddress : Collections.list(inetAddresses)) {
                    if (inetAddress instanceof Inet4Address && !inetAddress.isLoopbackAddress()) {
                        return  inetAddress;
                    }
                }
            }
            throw new RuntimeException("未获取到本机InetAddress！");
        }  catch (SocketException se) {
            throw new RuntimeException("获取本机InetAddress异常!", se);
        }
    }

    /**
     * 获取服务名称 host_address
     * @return 唯一标识
     */
    public static String getServerName() {
        InetAddress inetAddress = getInetAddress();
        String serverAddress = inetAddress.getHostAddress();
        return String.format("%s_%s", inetAddress.getHostName(), serverAddress);
    }
}
