package com.xiaohongshu.data.dataark.core.manager.oa.pojo;

import com.xiaohongshu.data.dataark.dao.entity.dataset.ProcessingTask;
import lombok.*;

import java.util.List;

/**
 * @author: longya
 * @since: 2023/9/6 12:00
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetPublishApproveParam extends ApproveParam {

    private String datasetNameVersion;

    private String datasetUrl;

    private String description;

    private String versionDescription;

    private String owner;

    private String approveRemark;

    private String verificationReport;

    private List<OaDatasourceParam> dataSources;

    private List<ProcessingTask> processingTasks;


}
