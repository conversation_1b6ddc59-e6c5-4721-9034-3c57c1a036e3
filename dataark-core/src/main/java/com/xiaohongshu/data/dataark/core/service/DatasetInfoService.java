package com.xiaohongshu.data.dataark.core.service;

import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/30
 */
public interface DatasetInfoService {

    DatasetInfoDO selectNoDraftByName(String name);

    DatasetInfoDO selectNoDraftByCode(String code);

    DatasetInfoDO selectNoDraftByNameAndId(String name, Long id);

    DatasetInfoDO selectNoDraftByCodeAndId(String code, Long id);

    DatasetInfoDO selectById(Long id);

    List<DatasetInfoDO> selectByIds(List<Long> ids);

}
