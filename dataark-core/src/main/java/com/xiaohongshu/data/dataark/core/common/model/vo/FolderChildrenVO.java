package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.xiaohongshu.data.dataark.core.common.enums.FolderType;
import com.xiaohongshu.data.dataark.dao.entity.AdhocDO;
import com.xiaohongshu.data.dataark.dao.entity.FolderDO;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/10
 */
@Data
public class FolderChildrenVO {

    private Long id;

    private boolean folderNode;

    private String name;

    private SimpleUser owner;

    private String fileType;

    private List<FolderChildrenVO> children;

    public static FolderChildrenVO createFolderNode(FolderDO folderDO) {
        FolderChildrenVO folderNode = new FolderChildrenVO();
        folderNode.setId(folderDO.getId());
        folderNode.setFolderNode(true);
        folderNode.setName(folderDO.getName());
        folderNode.setOwner(folderDO.getCreator());
        folderNode.setFileType(folderDO.getFolderType());
        return folderNode;
    }

    public static FolderChildrenVO createAdhocNode(AdhocDO adhocDO) {
        FolderChildrenVO adhocNode = new FolderChildrenVO();
        adhocNode.setId(adhocDO.getId());
        adhocNode.setFolderNode(false);
        adhocNode.setName(adhocDO.getName());
        adhocNode.setOwner(adhocDO.getCreator());
        adhocNode.setFileType(FolderType.adhoc.name());
        return adhocNode;
    }

}
