package com.xiaohongshu.data.dataark.core.service.impl;

import com.xiaohongshu.data.dataark.core.common.model.User;
import com.xiaohongshu.data.dataark.core.config.apollo.ApolloCommonConfig;
import com.xiaohongshu.data.dataark.core.service.UserService;
import com.xiaohongshu.dataverse.common.pojo.SimpleEmployee;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.utils.JsonUtil;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.PublicEmployeeService;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.request.SearchUserForSubSystemRequest;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.response.RpcUserInfo;
import com.xiaohongshu.fls.rpc.enterprise.oa_public.response.SearchUserForSubSystemResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/28
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private ApolloCommonConfig apolloCommonConfig;

    @Resource
    private PublicEmployeeService.Iface publicEmployeeService;

    private static final String SYSTEM_CODE = "dataverse";

    @Override
    public List<SimpleEmployee> search(String key) {
        try {
            SearchUserForSubSystemRequest searchUserForSubSystemRequest = new SearchUserForSubSystemRequest();
            searchUserForSubSystemRequest.setKey(key);
            searchUserForSubSystemRequest.setSysCode(SYSTEM_CODE);
            SearchUserForSubSystemResponse response = publicEmployeeService.searchUserForSubSystemFromES(new Context(), searchUserForSubSystemRequest);
            if (response.success) {
                List<RpcUserInfo> userInfoList = response.getUserInfoList();
                if (CollectionUtils.isEmpty(userInfoList)) {
                    return Collections.emptyList();
                }
                log.info("search employee success, key: {}, userInfoList size: {}", key, userInfoList.size());
                return userInfoList.stream().filter(u -> u.onJob).map(it -> {
                    SimpleEmployee simpleEmployee = new SimpleEmployee();
                    simpleEmployee.setUsername(it.emailPre);
                    simpleEmployee.setRealName(it.userName);
                    simpleEmployee.setRedName(it.redName);
                    simpleEmployee.setEmail(it.email);
                    return simpleEmployee;
                }).collect(Collectors.toList());
            }
            log.error("search employee error, key: {}, response: {}", key, JsonUtil.toString(response));
            return Collections.emptyList();
        } catch (TException e) {
            log.error("search employee error, key: {}", key, e);
        }
        return Collections.emptyList();
    }

    @Override
    public User userInfo(SimpleUser simpleUser) {
        User user = new User();
        user.setUserId(simpleUser.getUserId());
        user.setDisplayName(simpleUser.getDisplayName());
        user.setEmail(simpleUser.getEmail());
        if ("prod".equals(apolloCommonConfig.getEnv()) && !apolloCommonConfig.getAllowVisit()) {
            user.setCanVisit(apolloCommonConfig.getAllowVisitUser().contains(simpleUser.getEmail()));
        } else {
            user.setCanVisit(true);
        }
        user.setAdmin(apolloCommonConfig.getDataarkAdmin().contains(simpleUser.getEmail()));
        return user;
    }

}
