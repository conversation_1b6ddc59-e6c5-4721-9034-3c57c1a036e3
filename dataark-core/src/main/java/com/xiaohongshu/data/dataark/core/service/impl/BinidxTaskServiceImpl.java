package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.core.common.enums.BinidxSceneType;
import com.xiaohongshu.data.dataark.core.common.enums.PaiTaskStatus;
import com.xiaohongshu.data.dataark.core.common.enums.SampleType;
import com.xiaohongshu.data.dataark.core.service.BinidxTaskService;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.data.dataark.dao.mapper.BinidxTaskMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/21
 */
@Service
public class BinidxTaskServiceImpl implements BinidxTaskService {

    @Resource
    private BinidxTaskMapper binidxTaskMapper;


    @Override
    public int insertRecord(BinidxTaskDO binidxTaskDO) {
        return binidxTaskMapper.insert(binidxTaskDO);
    }

    @Override
    public IPage<BinidxTaskDO> pageList(Page<BinidxTaskDO> page, Long datasetVersionId) {
        return binidxTaskMapper.pageList(page, datasetVersionId);
    }

    @Override
    public List<BinidxTaskDO> selectUnfinishedSampleBinidxTasks(Long datasetVersionId) {
        LambdaQueryWrapper<BinidxTaskDO> wrapper = Wrappers.<BinidxTaskDO>lambdaQuery()
                .eq(BinidxTaskDO::getDatasetVersionId, datasetVersionId)
                .eq(BinidxTaskDO::getScene, BinidxSceneType.SAMPLE.getCode())
                .notIn(BinidxTaskDO::getStatus, PaiTaskStatus.FINISH_STATUS);
        return binidxTaskMapper.selectList(wrapper);
    }

    @Override
    public BinidxTaskDO selectById(Long binidxTaskId) {
        return binidxTaskMapper.selectById(binidxTaskId);
    }

    @Override
    public void updateRecord(BinidxTaskDO binidxTaskDO) {
        binidxTaskDO.setUpdateTime(LocalDateTime.now());
        binidxTaskMapper.updateById(binidxTaskDO);
    }

    @Override
    public void deleteById(Long binidxTaskId) {
        binidxTaskMapper.deleteById(binidxTaskId);
    }

    @Override
    public List<BinidxTaskDO> selectUnfinishedBinidxTasks() {
        // 先默认查询近7天的任务
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(20);
        LambdaQueryWrapper<BinidxTaskDO> wrapper = Wrappers.<BinidxTaskDO>lambdaQuery()
                .isNotNull(BinidxTaskDO::getJobId)
                .notIn(BinidxTaskDO::getStatus, PaiTaskStatus.FINISH_STATUS)
                .ge(BinidxTaskDO::getUpdateTime, startTime);
        return binidxTaskMapper.selectList(wrapper);
    }

    @Override
    public List<BinidxTaskDO> selectSuccessSampleAllRecord(Long datasetVersionId) {
        LambdaQueryWrapper<BinidxTaskDO> wrapper = Wrappers.<BinidxTaskDO>lambdaQuery()
                .eq(BinidxTaskDO::getDatasetVersionId, datasetVersionId)
                .eq(BinidxTaskDO::getScene, BinidxSceneType.SAMPLE.getCode())
                .eq(BinidxTaskDO::getSampleType, SampleType.ALL.getCode())
                .eq(BinidxTaskDO::getStatus, PaiTaskStatus.SUCCEEDED.getCode());
        return binidxTaskMapper.selectList(wrapper);
    }
}
