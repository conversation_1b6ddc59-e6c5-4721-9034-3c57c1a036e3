package com.xiaohongshu.data.dataark.core.manager.oa.pojo;

import lombok.Data;

import java.util.List;

@Data
public class RedUser {

    /**
     * 用户id, 非邮箱前缀
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 薯名
     */
    private String redName;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 人员类别 1:正式员工 2:实习生 3:编外人员 4:供应商派遣外包 5:BPO
     */
    private int employmentType;

    /**
     * 人员是否在职类别
     */
    private boolean onJob;

    /**
     * 部门id
     */
    private long departmentId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 薯名邮箱
     */
    private String redMail;

    /**
     * 小红书id
     */
    private String xhsId;

    /**
     * 按照部门路径返回的部门名称list
     */
    private List<String> departmentNameList;

    /**
     * 按照部门路径返回的部门idList
     */
    private List<Long> departmentIdList;

}
