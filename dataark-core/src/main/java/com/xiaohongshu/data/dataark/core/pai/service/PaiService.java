package com.xiaohongshu.data.dataark.core.pai.service;

import com.aliyun.pai_dlc20201203.Client;
import com.aliyun.pai_dlc20201203.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.common.collect.Maps;
import com.xiaohongshu.data.dataark.core.common.constant.Constants;
import com.xiaohongshu.data.dataark.core.common.enums.IndexType;
import com.xiaohongshu.data.dataark.core.common.enums.PaiTaskStatus;
import com.xiaohongshu.data.dataark.core.common.enums.PlatformType;
import com.xiaohongshu.data.dataark.core.common.enums.Region;
import com.xiaohongshu.data.dataark.core.config.apollo.ApolloCommonConfig;
import com.xiaohongshu.data.dataark.core.pai.config.PaiApolloConfig;
import com.xiaohongshu.data.dataark.core.pai.req.CreatePaiJobReq;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.IndexTaskDO;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import red.midware.shaded.com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.xiaohongshu.data.dataark.core.common.enums.Region.AP_SOUTHEAST_1;
import static com.xiaohongshu.data.dataark.core.common.enums.Region.CN_SHANGHAI;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/17
 */
@Component
@Slf4j
public class PaiService {

    @Value("${juicesync.cmd.template:echo hello}")
    private String juiceSyncCmdTemplate;

    @Value("${oss.access.key.id:}")
    private String ossAccessKeyId;

    @Value("${oss.access.key.secret:}")
    private String ossAccessKeySecret;

    private static final Map<Region, Pair<String, String>> gptBucketMap = Maps.newHashMap();

    @PostConstruct
    public void initGptBucketMap() {
        gptBucketMap.put(CN_SHANGHAI, Pair.of("oss://lsh-oss-gpt-spam", "oss://" + ossAccessKeyId + ":" + ossAccessKeySecret + "@lsh-oss-gpt-spam.oss-cn-shanghai-internal.aliyuncs.com"));
        gptBucketMap.put(AP_SOUTHEAST_1, Pair.of("oss://lsg-oss-chatgpt-agi-hcfs", "oss://" + ossAccessKeyId + ":" + ossAccessKeySecret + "@lsg-oss-chatgpt-agi-hcfs.oss-ap-southeast-1-internal.aliyuncs.com"));
    }

    public static String PROD_CPFS_MOUNT_PATH = "/prodcpfs";
    public static String CPFS_MOUNT_PATH = "/cpfs";
    public static String JOB_TYPE = "PyTorchJob";
    public static String WORKER = "Worker";
    public static String ACCESSIBILITY = "PUBLIC";
    public static String SGP_IMAGE = "ali-sg-acr-registry-vpc.ap-southeast-1.cr.aliyuncs.com/xhs-llm/pytorch:ngc2408-xiaoming-vllm";
    public static String GPT_OSS_ENDPOINT = "${gpt_oss_endpoints}";
    public static String SGP_OSS_ENDPOINT = "${sgp_oss_endpoints}";
    public static String BASE_SCRIPT = "#!/bin/bash\n" +
            "\n" +
            "# ======== 非必要不用改 =======\n" +
            "TASK_SCRIPTS_GIT=/cpfs/user/laite/workspace/pretrain/task_scripts\n" +
            "export PYTHONPATH=$TASK_SCRIPTS_GIT:$PYTHONPATH\n" +
            "\n" +
            "until pip install -i https://mirrors.aliyun.com/pypi/simple/ fastparquet nltk megatron-core==0.6.0 oss2 pyarrow; do\n" +
            "    echo \"pip install failed. Retrying in 5 seconds...\"\n" +
            "    sleep 5\n" +
            "done\n" +
            "\n" +
            "export PROCES_PER_DEVICE=32\n" +
            "\n" +
            "export ACCESS_KEY_ID='LTAI5tDuCoTTh6gu5PK8gFfN'\n" +
            "export ACCESS_KEY_SECRET='******************************'\n" +
            "# 国外oss（新加坡）\n" +
            "export SGP_INTERNAL_ENDPOINT='https://oss-ap-southeast-1-internal.aliyuncs.com'\n" +
            "export SGP_EXTERNAL_ENDPOINT='https://oss-ap-southeast-1.aliyuncs.com'\n" +
            "export SGP_OSS_BUCKET='lsg-oss-chatgpt-agi-hcfs'\n" +
            "# # 国内oss（上海）\n" +
            "export INTERNAL_ENDPOINT='https://oss-cn-shanghai-internal.aliyuncs.com'\n" +
            "export EXTERNAL_ENDPOINT='https://oss-cn-shanghai.aliyuncs.com'\n" +
            "export OSS_BUCKET='lsh-oss-gpt-spam'\n" +
            "\n" +
            "gpt_oss_endpoints=\"oss://LTAI5tDuCoTTh6gu5PK8gFfN:<EMAIL>\"\n" +
            "sgp_oss_endpoints=\"oss://LTAI5tDuCoTTh6gu5PK8gFfN:<EMAIL>\"\n" +
            "\n" +
            "# ======== 更改项，启动配置 =======\n" +
            "# 参数说明：\n" +
            "#     --input_path: 待转成binidx的原始数据路径，可以是oss路径或cpfs路径\n" +
            "#     --output_path: 转成binidx后保存路径\n" +
            "#     --file_type: 输入文件后缀，支持'parquet', 'json', 'jsonl', 'TableSink1'\n" +
            "#     --input_region: 原始数据所在区域，sh表示上海，sgp表示新加坡\n" +
            "#     --output_region: 原始数据所在区域，sh表示上海，sgp表示新加坡\n" +
            "#     --json_keys: 要转换的字段名，默认为\"text\"\n" +
            "#     --backup2oss: 转成binidx后的文件是否要备份到oss，当保存路径为cpfs时生效，保存路径为oss://lsg-oss-chatgpt-agi-hcfs/ods/binidx/\n" +
            "# ================================\n" +
            "\n" +
            "\n" +
            "\n" +
            "export cache_name=${dataark_cache_name}\n" +
            "export input_path=${dataark_input_path}\n" +
            "export output_path=/${dataark_cpfs}/data/dataark/${dataark_env}/${dataark_scene}/$cache_name\n" +
            "\n" +
            "\n" +
            "dataLockFile=/prodcpfs/user/dataark/data/parquet/cache/${cache_name}.txt\n" +
            "\n" +
            "\n" +
            "\n" +
            " while ! test -e $dataLockFile; do\n" +
            "     echo $dataLockFile not exists\n" +
            "     if [ $RANK -eq 0 ]; then \n" +
            "\n" +
            "         echo \"download $input_path to /prodcpfs/user/dataark/data/parquet/cache/$cache_name/\"\n" +
            "          juicefs sync --ignore-existing -u -p  1000 \\\n" +
            "             ${dataark_oss_region}/$input_path/ \\\n" +
            "             /prodcpfs/user/dataark/data/parquet/cache/$cache_name/\n" +
            "\n" +
            "         juicefs sync --ignore-existing -u -p  1000 \\\n" +
            "             ${dataark_oss_region}/$input_path/ \\\n" +
            "             /prodcpfs/user/dataark/data/parquet/cache/$cache_name/\n" +
            "\n" +
            "         echo \"download $input_path to /prodcpfs/user/dataark/data/parquet/cache/$cache_name/ ok\"\n" +
            "         echo \"ok\" >> $dataLockFile\n" +
            "     else\n" +
            "         echo \"wait 60s for data at rank $RANK\"\n" +
            "         sleep 60s\n" +
            "     fi\n" +
            " done\n" +
            "\n" +
            "\n" +
            "# 转binidx任务\n" +
            "python $TASK_SCRIPTS_GIT/dpu/main_dist.py \\\n" +
            "    $TASK_SCRIPTS_GIT/binidx/transform_binidx/transform_binidx.py \\\n" +
            "    --input_path  /prodcpfs/user/dataark/data/parquet/cache/$cache_name/ \\\n" +
            "    --output_path $output_path \\\n" +
            "    --file_type ${dataark_file_type} \\\n" +
            "    --input_region sgp \\\n" +
            "    --output_region sgp \\\n" +
            "    --json_keys text\n" +
            "\n" +
            "if [ $RANK -eq 0 ]; then\n" +
            "    rm -rf /prodcpfs/user/dataark/data/parquet/cache/$cache_name/\n" +
            "    rm  $dataLockFile\n" +
            "    echo \"success\"\n" +
            "fi";

    public static String CP_SCRIPT = "#!/bin/bash\n" +
            "\n" +
            "# ======== cp file =======\n" +
            "export target_path=/${dataark_cpfs}/data/dataark/${dataark_env}/${dataark_scene}/\n" +
            "cp -r ${dataark_source_path} ${target_path}\n" +
            "cp ${dataark_source_path}.bin ${target_path}\n" +
            "cp ${dataark_source_path}.idx ${target_path}\n" +
            "echo \"success\"\n";

    public static String EMBEDDING_SCRIPT = "#数据名称\n" +
            "export dataName=\"${dataark_dataset_version_name}\"\n" +
            "#数据地址\n" +
            "export input_path=\"${dataark_input_path}\"\n" +
            "#embedding地址\n" +
            "export output_path=\"${dataark_output_path}\"\n" +
            "\n" +
            "\n" +
            "#数据下载到GPU集群的地址\n" +
            "export localPath=/cpfs/user/dataark/embedding/${dataark_env}/$dataName/data/\n" +
            "mkdir -p $localPath\n" +
            "#处理好的embedding数据\n" +
            "export resPath=/cpfs/user/dataark/embedding/${dataark_env}/$dataName/res/\n" +
            "mkdir -p $resPath\n" +
            "#数据同步锁\n" +
            "export dataLockFile=/cpfs/user/dataark/embedding/${dataark_env}/$dataName/lock.txt\n" +
            "\n" +
            "\n" +
            "gpt_oss_endpoints=\"oss://LTAI5tDuCoTTh6gu5PK8gFfN:<EMAIL>\"\n" +
            "sgp_oss_endpoints=\"oss://LTAI5tDuCoTTh6gu5PK8gFfN:<EMAIL>\"\n" +
            "\n" +
            "\n" +
            "if [ $RANK -eq 0 ]; then \n" +
            "  rm -rf $dataLockFile\n" +
            "else\n" +
            "  sleep 30s\n" +
            "fi\n" +
            "\n" +
            "\n" +
            "# 在rank=0时， 下载oss数据到本地\n" +
            "while ! test -e $dataLockFile; do\n" +
            "    echo $dataLockFile not exists\n" +
            "    if [ $RANK -eq 0 ]; then \n" +
            "        echo \"download $input_path/ to $localPath/\"\n" +
            "\n" +
            "        juicefs sync --ignore-existing -u -p  1000 \\\n" +
            "             ${dataark_oss_region}/$input_path/ \\\n" +
            "             $localPath/\n" +
            "\n" +
            "        juicefs sync --ignore-existing -u -p  1000 \\\n" +
            "             ${dataark_oss_region}/$input_path/ \\\n" +
            "             $localPath/\n" +
            "\n" +
            "        echo \"download $input_path to $localPath ok\"\n" +
            "        echo \"ok\" >> $dataLockFile\n" +
            "    else\n" +
            "        echo \"wait 60s for data at rank $RANK\"\n" +
            "        sleep 60s\n" +
            "    fi\n" +
            "done\n" +
            "\n" +
            "\n" +
            "\n" +
            "HOME=/cpfs/user/xinghai/code/data_process_utility\n" +
            "source /prodcpfs/user/xiaoming/envs/semdedup/bin/activate\n" +
            "cd $HOME\n" +
            "export PYTHONPATH=$HOME\n" +
            "#执行embedding脚本\n" +
            "python main_dist.py scripts/semdedup/text_embedding.py -i $localPath -o $resPath -ppd 2\n" +
            "\n" +
            "\n" +
            "\n" +
            "#embedding结果上传到oss\n" +
            "if [ $RANK -eq 0 ]; then \n" +
            "\tjuicefs sync --ignore-existing -u -p  1000 \\\n" +
            "\t\t$resPath/ \\\n" +
            "\t    ${gpt_oss_endpoints}/$output_path/ \\\n" +
            "\n" +
            "\tjuicefs sync --ignore-existing -u -p  1000 \\\n" +
            "\t\t$resPath/ \\\n" +
            "\t    ${gpt_oss_endpoints}/$output_path/ \\\n" +
            "\n" +
            "\techo \"upload $resPath/ to $output_path/  ok\"\n" +
            "fi";

    public static String EMBEDDING_IMAGE_URL = "ali-sg-acr-registry-vpc.ap-southeast-1.cr.aliyuncs.com/xhs-llm/xhs-llm:dsw-data";

    @Autowired
    private Client client;

    @Autowired
    private ApolloCommonConfig apolloCommonConfig;

    @Resource
    private PaiApolloConfig paiApolloConfig;

    @ApolloJsonValue("${embedding.pai.config:{}}")
    private Map<String, String> embeddingPaiConfig;

    @ApolloJsonValue("${juicesync.pai.config:{}}")
    private Map<String, String> juicesyncPaiConfig;

    public String createPaiJob(CreatePaiJobReq req) {
        if (!apolloCommonConfig.isRealPai()) {
            // 不去真实调用pai
            return "mock-job-id";
        }
        CreateJobRequest.CreateJobRequestDataSources dataSources0 = new CreateJobRequest.CreateJobRequestDataSources()
                .setDataSourceId(req.getProdCpfsDatasourceId())
                .setMountPath(PROD_CPFS_MOUNT_PATH);
        CreateJobRequest.CreateJobRequestDataSources dataSources1 = new CreateJobRequest.CreateJobRequestDataSources()
                .setDataSourceId(req.getCpfsDatasourceId())
                .setMountPath(CPFS_MOUNT_PATH);
        ResourceConfig jobSpec0ResourceConfig = new ResourceConfig()
                .setMemory(req.getMemory() + "Gi")
                .setSharedMemory(req.getMemory() + "Gi")
                .setGPU(Objects.isNull(req.getGpu()) ? "0" : String.valueOf(req.getGpu()))
                .setCPU(String.valueOf(req.getCpu()));
        JobSpec jobSpec0 = new JobSpec()
                .setResourceConfig(jobSpec0ResourceConfig)
                .setImage(StringUtils.isNotEmpty(req.getImageUrl()) ? req.getImageUrl() : SGP_IMAGE)
                .setType(WORKER)
                .setPodCount(req.getWorkerPodCount());
        CreateJobRequest createJobRequest = new CreateJobRequest()
                .setJobSpecs(java.util.Arrays.asList(
                        jobSpec0
                ))
                .setDisplayName(req.getJobDisplayName())
                .setJobType(JOB_TYPE)
                .setUserCommand(req.getUserCommand())
                .setDataSources(java.util.Arrays.asList(
                        dataSources0,
                        dataSources1
                ))
                .setWorkspaceId(req.getWorkSpaceId())
                .setPriority(req.getPriority())
                .setResourceId(req.getResourceId())
                .setAccessibility(ACCESSIBILITY);
        RuntimeOptions runtime = new RuntimeOptions();
        Map<String, String> headers = new HashMap<>();
        try {
            // 复制代码运行请自行打印 API 的返回值
            CreateJobResponse jobWithOptions = client.createJobWithOptions(createJobRequest, headers, runtime);
            return jobWithOptions.getBody().getJobId();
        } catch (TeaException error) {
            // 错误 message
            log.error(error.getMessage());
            // 诊断地址
            log.error((String) error.getData().get("Recommend"));
            throw new ServiceException("createPaiJob TeaException:" + error.getMessage());
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            log.error(error.getMessage());
            // 诊断地址
            log.error((String) error.getData().get("Recommend"));
            throw new ServiceException("createPaiJob Exception:" + error.getMessage());
        }
    }

    public GetJobResponseBody getPaiJobInfo(String jobId) {
        if ("mock-job-id".equals(jobId)) {
            // 不去真实调用pai
            GetJobResponseBody mockResponseBody = new GetJobResponseBody();
            mockResponseBody.setStatus(PaiTaskStatus.SUCCEEDED.getCode());
            return mockResponseBody;
        }
        // 不用返回作业详细信息
        GetJobRequest getJobRequest = new GetJobRequest()
                .setNeedDetail(false);
        RuntimeOptions runtime = new RuntimeOptions();
        Map<String, String> headers = new HashMap<>();
        try {
            GetJobResponse jobWithOptions = client.getJobWithOptions(jobId, getJobRequest, headers, runtime);
            return jobWithOptions.getBody();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error(error.getMessage());
            // 诊断地址
            log.error((String) error.getData().get("Recommend"));
            throw new ServiceException("getBinidxJobInfo TeaException:" + error.getMessage());
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            log.error(error.getMessage());
            // 诊断地址
            log.error((String) error.getData().get("Recommend"));
            throw new ServiceException("getBinidxJobInfo Exception:" + error.getMessage());
        }
    }

    public String generateUserCommand(String datasetCacheName, String inputPath, String cpfs, String env, String dataarkScene, String region, String dataarkFileType) {
        String ossRegion = SGP_OSS_ENDPOINT;
        // 后续根据region的枚举值来判断，或者根据输入路径的桶来判断
        if (!Region.AP_SOUTHEAST_1.getRegion().equals(region)) {
            ossRegion = GPT_OSS_ENDPOINT;
        }
        // 生成用户命令
        return BASE_SCRIPT
                .replace("${dataark_cache_name}", datasetCacheName)
                .replace("${dataark_input_path}", inputPath)
                .replace("${dataark_cpfs}", cpfs)
                .replace("${dataark_oss_region}", ossRegion)
                .replace("${dataark_env}", env)
                .replace("${dataark_scene}", dataarkScene)
                .replace("${dataark_file_type}", dataarkFileType);
    }

    public String generateCpCommand(String sourcePath, String cpfsType, String env, String scene) {
        return CP_SCRIPT
                .replace("${dataark_source_path}", sourcePath)
                .replace("${dataark_cpfs}", cpfsType)
                .replace("${dataark_env}", env)
                .replace("${dataark_scene}", scene);
    }

    public String generateEmbeddingCommand(String datasetVersionName, String inputPathRegion, String inputPath, String outputPath, String env) {
        String ossRegion = SGP_OSS_ENDPOINT;
        // 后续根据region的枚举值来判断，或者根据输入路径的桶来判断
        if (!Region.AP_SOUTHEAST_1.getRegion().equals(inputPath)) {
            ossRegion = GPT_OSS_ENDPOINT;
        }
        String normalizedInputPath = normalizeOssPath(inputPath);
        String normalizedOutputPath = normalizeOssPath(outputPath);
        return EMBEDDING_SCRIPT
                .replace("${dataark_dataset_version_name}", datasetVersionName)
                .replace("${dataark_input_path}", normalizedInputPath)
                .replace("${dataark_output_path}", normalizedOutputPath)
                .replace("${dataark_oss_region}", ossRegion)
                .replace("${dataark_env}", env);
    }

    public String generateEmbeddingOutputPath(String datasetVersionName, String env) {
        return "oss://lsh-oss-gpt-spam/dataark/output/" + env + "/embedding/" + datasetVersionName;
    }

    public String getJuiceSyncScript(String ossPath, Long datasetVersionId) {
        if (ObjectUtils.isEmpty(ossPath)) {
            throw new ServiceException("数据集版本信息中ossPath不能为空");
        }
        if (StringUtils.isEmpty(ossPath.replace("/", "")) || ossPath.contains(" ")) {
            throw new ServiceException("数据集版本信息中ossPath格式错误" + ossPath);
        }

        Pair<String, String> srcBucketRegion = gptBucketMap.get(AP_SOUTHEAST_1);

        if (!ossPath.startsWith(srcBucketRegion.getLeft())){
            throw new ServiceException("数据集版本信息中ossPath不在支持的源bucket内");
        }
        if (!ossPath.endsWith("/")){
            ossPath += "/";
        }
        final String srcPath = ossPath.replace(srcBucketRegion.getLeft(), srcBucketRegion.getRight());
        final String dstPath = juiceSyncDstTmpPath(datasetVersionId, true);

        if (srcPath.contains(" ") || dstPath.contains(" ")) {
            throw new ServiceException("oss路径格式错误," + srcPath + ", " + dstPath);
        }

        return juiceSyncCmdTemplate + " " + srcPath + " " + dstPath;
    }

    public String juiceSyncDstTmpPath(Long datasetVersionId, boolean auth) {
        Pair<String, String> dstBucketRegion = gptBucketMap.get(CN_SHANGHAI);
        final String prefix = auth ?
                dstBucketRegion.getRight(): dstBucketRegion.getLeft();
        return prefix + "/dataark/tmp/copy_from_sgp_" + apolloCommonConfig.getEnv().toLowerCase() + "/" + datasetVersionId + "/";
    }

    public String createJuiceSyncTask(DatasetVersionInfoDO datasetVersionInfoDO) {

        if (!AP_SOUTHEAST_1.getRegion().equalsIgnoreCase(datasetVersionInfoDO.getRegion())) {
            throw new ServiceException("仅支持新加坡数据集拷贝");
        }

        final String ossPath = datasetVersionInfoDO.getOssPath();
        final String juiceSyncCmd = getJuiceSyncScript(ossPath, datasetVersionInfoDO.getId());
        String paiJobName = "dataark_juice_sync_" + apolloCommonConfig.getEnv() + "_" + datasetVersionInfoDO.getId();

        CreatePaiJobReq createPaiJobReq = new CreatePaiJobReq();
        createPaiJobReq.setJobDisplayName(paiJobName);
        createPaiJobReq.setPriority(embeddingPaiConfig.get("priority") != null ? Integer.parseInt(embeddingPaiConfig.get("priority")) : 8);
        createPaiJobReq.setWorkerPodCount(1L);
        createPaiJobReq.setCpu(5);
        createPaiJobReq.setGpu(0);
        createPaiJobReq.setMemory(40);
        createPaiJobReq.setImageUrl(EMBEDDING_IMAGE_URL);
        createPaiJobReq.setWorkSpaceId(embeddingPaiConfig.get("workSpaceId"));
        createPaiJobReq.setCpfsDatasourceId(embeddingPaiConfig.get("cpfsId"));
        createPaiJobReq.setProdCpfsDatasourceId(embeddingPaiConfig.get("prodcpfsId"));
        createPaiJobReq.setResourceId(embeddingPaiConfig.get("resourceId"));
        createPaiJobReq.setUserCommand(juiceSyncCmd);
        String paiJobId;
        try {
            paiJobId = createPaiJob(createPaiJobReq);
            log.info("创建juicesync pai任务成功, paiJobId: {}, oss路径: {}", paiJobId, ossPath);
            return paiJobId;
        } catch (Exception e) {
            String errMsg = "创建juicesync pai任务失败";
            log.error("{}, oss路径: {}", errMsg, ossPath, e);
            throw new ServiceException(errMsg);
        }

    }

    public IndexTaskDO createEmbeddingTask(DatasetInfoDO datasetInfoDO, DatasetVersionInfoDO datasetVersionInfoDO, SimpleUser simpleUser) {
        IndexTaskDO indexTaskDO = new IndexTaskDO();
        try {
            indexTaskDO.setDatasetVersionId(datasetVersionInfoDO.getId());
            String datasetVersionName = datasetInfoDO.getCode() + "_v" + datasetVersionInfoDO.getVersion();
            String paiJobName = "dataark_embedding_" + apolloCommonConfig.getEnv() + "_" + datasetVersionName;
            String outputPath = generateEmbeddingOutputPath(datasetVersionName, apolloCommonConfig.getEnv());
            indexTaskDO.setDatasetVersionName(datasetVersionName);
            indexTaskDO.setIndexType(IndexType.VECTOR.getCode());
            indexTaskDO.setPlatform(PlatformType.pai.name());
            indexTaskDO.setStartTime(LocalDateTime.now());
            indexTaskDO.setStatus(PaiTaskStatus.CREATING.getCode());
            indexTaskDO.setInputPath(datasetVersionInfoDO.getOssPath());
            indexTaskDO.setOutputPath(outputPath);
            CreatePaiJobReq createPaiJobReq = new CreatePaiJobReq();
            createPaiJobReq.setJobDisplayName(paiJobName);
            createPaiJobReq.setPriority(embeddingPaiConfig.get("priority") != null ? Integer.parseInt(embeddingPaiConfig.get("priority")) : 8);
            createPaiJobReq.setWorkerPodCount(1L);
            createPaiJobReq.setCpu(100);
            createPaiJobReq.setGpu(embeddingPaiConfig.get("gpu") != null ? Integer.parseInt(embeddingPaiConfig.get("gpu")) : 8);
            createPaiJobReq.setMemory(1000);
            createPaiJobReq.setImageUrl(EMBEDDING_IMAGE_URL);
            createPaiJobReq.setWorkSpaceId(embeddingPaiConfig.get("workSpaceId"));
            createPaiJobReq.setCpfsDatasourceId(embeddingPaiConfig.get("cpfsId"));
            createPaiJobReq.setProdCpfsDatasourceId(embeddingPaiConfig.get("prodcpfsId"));
            createPaiJobReq.setResourceId(embeddingPaiConfig.get("resourceId"));
            createPaiJobReq.setUserCommand(generateEmbeddingCommand(datasetVersionName, datasetVersionInfoDO.getRegion(),
                    datasetVersionInfoDO.getOssPath(), outputPath, apolloCommonConfig.getEnv()));
            String paiJobId = createPaiJob(createPaiJobReq);
            indexTaskDO.setTaskId(paiJobId);
        } catch (Exception e) {
            log.error("创建embedding任务失败，datasetVersionId: {}, error: ", datasetVersionInfoDO.getId(), e);
            throw new ServiceException("创建embedding任务失败: " + e.getMessage());
        }
        return indexTaskDO;
    }

    public String normalizeOssPath(String ossPath) {
        if (ossPath.startsWith(Constants.LSH_OSS_PREFIX)) {
            ossPath = ossPath.substring(Constants.LSH_OSS_PREFIX.length());
        } else if (ossPath.startsWith(Constants.SGP_OSS_PREFIX)) {
            ossPath = ossPath.substring(Constants.SGP_OSS_PREFIX.length());
        } else {
            throw new ServiceException("输入路径为不支持的桶");
        }
        if (ossPath.endsWith("/")) {
            ossPath = ossPath.substring(0, ossPath.length() - 1);
        }
        return ossPath;
    }
}
