package com.xiaohongshu.data.dataark.core.utils;

import com.alibaba.excel.EasyExcel;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.xiaohongshu.sec.kms.common.exception.KmsException;
import com.xiaohongshu.sec.kms.sdk.KmsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.avro.AvroParquetReader;
import org.apache.parquet.hadoop.ParquetReader;
import org.apache.parquet.hadoop.util.HadoopInputFile;
import org.apache.parquet.io.InputFile;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xiaohongshu.data.dataark.core.common.constant.Constants.KMS_KEY;
import static com.xiaohongshu.data.dataark.core.common.constant.Constants.KMS_KEY_ID;

/**
 * (*^▽^*)
 * Project: llm-dmp
 * Author:  Bean
 * Date:    2023/9/11
 * Contact: <EMAIL>
 */
@Slf4j
public class OSSUtils {

    public static final String OSS_REGION_CN_SHANGHAI = "cn-shanghai";
    public static final String OSS_REGION_AP_SOUTHEAST_1 = "oss-ap-southeast-1";
    public static final String OSS_ENDPOINT = "https://oss-cn-shanghai-internal.aliyuncs.com";
    public static final String SGP_OSS_ENDPOINT = "https://oss-ap-southeast-1-internal.aliyuncs.com";
    public static String OSS_KEY;

    static {
        try {
            OSS_KEY = KmsClient.getSecretValue(KMS_KEY_ID);
        } catch (KmsException e) {
            throw new RuntimeException(e);
        }
    }

    public static String OSS_SECRET;

    static {
        try {
            OSS_SECRET = KmsClient.getSecretValue(KMS_KEY);
        } catch (KmsException e) {
            throw new RuntimeException(e);
        }
    }

    public static final String OSS_BUCKET = "lsh-oss-gpt-spam";
    public static final String OSS_PREFIX = "oss://" + OSS_BUCKET + "/";
    public static final String SGP_OSS_BUCKET = "lsg-oss-chatgpt-agi-hcfs";
    public static final String SGP_OSS_PREFIX = "oss://" + SGP_OSS_BUCKET + "/";
    public static final String OSS_DATAVERSE_BUCKET = "xhs-bigdata-dataverse";
    public static final Integer EXCEL_MAX_SIZE = 32767; // Excel单元格最大支持32767


    private static final OSS CLIENT = new OSSClientBuilder().build(
            OSS_ENDPOINT, OSS_KEY, OSS_SECRET);

    private static final OSS SGP_CLIENT = new OSSClientBuilder().build(
            SGP_OSS_ENDPOINT, OSS_KEY, OSS_SECRET);

    static class NamedThreadFactory implements ThreadFactory {
        private final String name;
        private final AtomicInteger i = new AtomicInteger(0);

        public NamedThreadFactory(String name) {
            this.name = name;
        }

        @Override
        public Thread newThread(@NotNull Runnable r) {
            return new Thread(r, name + "-" + i.getAndIncrement());
        }
    }

    static {
        Runtime.getRuntime().addShutdownHook(new NamedThreadFactory("close-oss-client").newThread(CLIENT::shutdown));
        Runtime.getRuntime().addShutdownHook(new NamedThreadFactory("close-sgp-oss-client").newThread(SGP_CLIENT::shutdown));
    }

    public static boolean isDir(String path, OSS client, String bucket) {
        if (!path.endsWith("/")) {
            path += "/";
        }
        ListObjectsRequest request = new ListObjectsRequest(bucket);
        request.setPrefix(path);
        request.setDelimiter("/");
        ObjectListing listing = client.listObjects(request);
        return !listing.getObjectSummaries().isEmpty() || !listing.getCommonPrefixes().isEmpty();
    }

    public static List<OSSObjectSummary> listDir(String path, Integer maxKeys) {
        OSS client;
        String bucket;
        if (path.startsWith(OSS_PREFIX)) {
            path = path.replace(OSS_PREFIX, "");
            client = CLIENT;
            bucket = OSS_BUCKET;
        } else if (path.startsWith(SGP_OSS_PREFIX)) {
            path = path.replace(SGP_OSS_PREFIX, "");
            client = SGP_CLIENT;
            bucket = SGP_OSS_BUCKET;
        } else {
            throw new RuntimeException("不支持的oss路径");
        }
        if (isDir(path, client, bucket)) {
            if (!path.endsWith("/")) {
                path += "/";
            }
            String rawPath = path;
            ListObjectsRequest request = new ListObjectsRequest(bucket);
            request.setPrefix(path);
            request.setMaxKeys(maxKeys);

            ObjectListing listing = client.listObjects(request);

            return listing.getObjectSummaries()
                    .stream()
                    .filter(s -> !s.getKey().equals(rawPath) && s.getKey().endsWith(".parquet") && s.getSize() > 0)
                    .collect(Collectors.toList());
        } else {
            throw new RuntimeException("path is not dir");
        }
    }

    private static <T> T ls0(String path, Function<List<OSSObjectSummary>, T> func) {
        OSS client;
        String bucket;
        if (path.startsWith(OSS_PREFIX)) {
            path = path.replace(OSS_PREFIX, "");
            client = CLIENT;
            bucket = OSS_BUCKET;
        } else if (path.startsWith(SGP_OSS_PREFIX)) {
            path = path.replace(SGP_OSS_PREFIX, "");
            client = SGP_CLIENT;
            bucket = SGP_OSS_BUCKET;
        } else {
            throw new RuntimeException("不支持的oss路径");
        }
        List<OSSObjectSummary> summaries;
        if (isDir(path, client, bucket)) {
            // 需要分页遍历
            if (!path.endsWith("/")) {
                path += "/";
            }
            String rawPath = path;
            ObjectListing listing = null;
            summaries = new ArrayList<>();
            do {
                ListObjectsRequest request = new ListObjectsRequest(bucket);
                request.setPrefix(path);
//                request.setDelimiter("/");
                request.setMaxKeys(200);
                if (listing != null) {
                    request.setMarker(listing.getNextMarker());
                }
                listing = client.listObjects(request);
                summaries.addAll(listing.getObjectSummaries()
                        .stream()
                        .filter(s -> !s.getKey().equals(rawPath) && s.getKey().endsWith(".parquet") && s.getSize() > 0)
                        .collect(Collectors.toList()));
            } while (listing.isTruncated());
        } else {
            ObjectListing objectListing = client.listObjects(bucket, path);
            summaries = objectListing.getObjectSummaries();
        }
        return func.apply(summaries);
    }

    public static long size(String path) {
        return ls0(path, list -> list.stream().mapToLong(OSSObjectSummary::getSize).sum());
    }

    public static String parquetToExcel(Long id, String ossPath, String excelName) {
        log.info("start parquetToExcel id: {}, ossPath: {}", id, ossPath);
        OSS client;
        String bucket;
        if (ossPath.startsWith(OSS_PREFIX)) {
            ossPath = ossPath.replace(OSS_PREFIX, "");
            client = CLIENT;
            bucket = OSS_BUCKET;
        } else if (ossPath.startsWith(SGP_OSS_PREFIX)) {
            ossPath = ossPath.replace(SGP_OSS_PREFIX, "");
            client = SGP_CLIENT;
            bucket = SGP_OSS_BUCKET;
        } else {
            throw new RuntimeException("不支持的oss路径");
        }
        String pathPrefix = ossPath;
        if (!pathPrefix.endsWith("/")) {
            pathPrefix += "/";
        }
        String excelOssKey = "dataark/sample_download_result/" + excelName;
        File tempDir = null;
        try {
            // 1. 列举 parquet 文件
            List<String> parquetKeys = new ArrayList<>();
            String nextMarker = null;
            do {
                ListObjectsV2Request listObjectsRequest = new ListObjectsV2Request(bucket)
                        .withPrefix(pathPrefix)
                        .withMaxKeys(1000)
                        .withContinuationToken(nextMarker);

                ListObjectsV2Result result = client.listObjectsV2(listObjectsRequest);
                for (OSSObjectSummary summary : result.getObjectSummaries()) {
                    if (summary.getKey().endsWith(".parquet")) {
                        parquetKeys.add(summary.getKey());
                    }
                }
                nextMarker = result.getNextContinuationToken();
            } while (nextMarker != null);

            if (parquetKeys.isEmpty()) {
                log.info("未找到parquet文件");
                return null;
            }

            // 2. 读取所有parquet，汇总到 allRows
            List<List<String>> allRows = new ArrayList<>();
            List<String> headers;
            Schema schema = null;

            tempDir = new File("/tmp/", "oss_parquet_temp_" + id + "_" + UUID.randomUUID());
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }

            for (String key : parquetKeys) {
                File parquetFile = new File(tempDir, new File(key).getName());
                OSSObject ossObject = client.getObject(bucket, key);
                try (InputStream in = ossObject.getObjectContent();
                     OutputStream out = Files.newOutputStream(parquetFile.toPath())) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }

                Configuration conf = new Configuration();
                Path path = new Path(parquetFile.getAbsolutePath());
                InputFile inputFile = HadoopInputFile.fromPath(path, conf);

                // 读取parquet
                try (ParquetReader<GenericRecord> reader = AvroParquetReader.<GenericRecord>builder(inputFile).build()) {
                    GenericRecord record;
                    while ((record = reader.read()) != null) {
                        if (schema == null) {
                            schema = record.getSchema();
                            headers = new ArrayList<>();
                            // 以schema的字段顺序作为表头
                            for (Schema.Field f : schema.getFields()) {
                                headers.add(f.name());
                            }
                            allRows.add(headers);
                        }
                        List<String> row = new ArrayList<>();
                        for (Schema.Field f : schema.getFields()) {
                            Object value = record.get(f.name());
                            String cell = value == null ? "" : value.toString();
                            if (cell.length() > EXCEL_MAX_SIZE) { // Excel单元格最大支持32767
                                cell = cell.substring(0, EXCEL_MAX_SIZE); // 或 + "...(truncated)"
                            }
                            row.add(cell);
                        }
                        allRows.add(row);
                    }
                }
                parquetFile.delete();
            }
            // 3. 写Excel
            File excelFile = new File(tempDir, "result.xlsx");
            log.info("start write excel");
            EasyExcel.write(excelFile).sheet("data").doWrite(allRows);
            // 4. 上传到dataverse桶
            CLIENT.putObject(OSS_DATAVERSE_BUCKET, excelOssKey, excelFile);

            // 5. 获取下载链接（带签名，1小时有效，私有推荐，公有改成拼接 URL）
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000); // 1小时
            GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(OSS_DATAVERSE_BUCKET, excelOssKey);
            urlRequest.setExpiration(expiration);
            URL url = CLIENT.generatePresignedUrl(urlRequest);

            // 6. 清理
            excelFile.delete();
            tempDir.delete();
            return url.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 清理临时目录和文件
            if (tempDir != null && tempDir.exists()) {
                if (tempDir.listFiles() != null) {
                    for (File file : tempDir.listFiles()) {
                        file.delete();
                    }
                }
                tempDir.delete();
            }
        }
    }


    public static String parseBucketName(String ossPath) {
        if (ossPath == null || ossPath.isEmpty()) {
            throw new IllegalArgumentException("OSS path cannot be null or empty");
        }

        if (!ossPath.startsWith("oss://")) {
            throw new IllegalArgumentException("Invalid OSS path format");
        }

        String remaining = ossPath.substring("oss://".length());
        int slashIndex = remaining.indexOf('/');
        String bucketName;
        if (slashIndex == -1) {
            bucketName = remaining;
        } else {
            bucketName = remaining.substring(0, slashIndex);
        }

        if (StringUtils.isEmpty(bucketName)){
            throw new IllegalArgumentException("Invalid OSS path format");
        }
        return bucketName;
    }

    public static void downloadObject(String bucketName, String objectName, String localFilePath) {
        try {
            // 下载OSS文件到本地
            OSSUtils.CLIENT.getObject(new GetObjectRequest(bucketName, objectName), new java.io.File(localFilePath));
            log.info("Object downloaded successfully from bucket: {}, object: {}, to local path: {}",
                    bucketName, objectName, localFilePath);
        } catch (Exception e) {
            log.error("downloadObject error", e);
        }

    }

}
