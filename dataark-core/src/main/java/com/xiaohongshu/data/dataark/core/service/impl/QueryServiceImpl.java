package com.xiaohongshu.data.dataark.core.service.impl;

import com.aliyun.oss.model.OSSObjectSummary;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.red.data.dg.common.EngineType;
import com.xiaohongshu.data.capella.spi.ColumnInfo;
import com.xiaohongshu.data.capella.spi.QuerySPI;
import com.xiaohongshu.data.dataark.core.common.constant.Constants;
import com.xiaohongshu.data.dataark.core.common.enums.MediaType;
import com.xiaohongshu.data.dataark.core.common.enums.QueryProgressState;
import com.xiaohongshu.data.dataark.core.common.enums.QueryType;
import com.xiaohongshu.data.dataark.core.common.enums.Region;
import com.xiaohongshu.data.dataark.core.common.model.request.QueryCreateRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.QueryRecordVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.QueryResultVO;
import com.xiaohongshu.data.dataark.core.common.model.request.SampleQueryCreateRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.RegisterCheckVO;
import com.xiaohongshu.data.dataark.core.config.apollo.ApolloCommonConfig;
import com.xiaohongshu.data.dataark.core.rpc.capella.CapellaApiCaller;
import com.xiaohongshu.data.dataark.core.rpc.crossroad.CrossroadApiCaller;
import com.xiaohongshu.data.dataark.core.rpc.dragongate.DragonGateCaller;
import com.xiaohongshu.data.dataark.core.service.DatasetInfoService;
import com.xiaohongshu.data.dataark.core.service.DatasetVersionInfoService;
import com.xiaohongshu.data.dataark.core.service.QueryService;
import com.xiaohongshu.data.dataark.core.service.common.AlarmService;
import com.xiaohongshu.data.dataark.core.utils.OSSUtils;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.QueryRecordDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.QueryRecordMapper;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QueryServiceImpl implements QueryService {

    private static final String QUERY_ENGINE = "spark";

    @Autowired
    private QueryRecordMapper queryRecordMapper;

    @Autowired
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Autowired
    private DragonGateCaller dragonGateCaller;

    @Autowired
    private CapellaApiCaller capellaApiCaller;

    @Autowired
    private CrossroadApiCaller crossroadApiCaller;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private DatasetVersionInfoService datasetVersionInfoService;

    @Autowired
    private DatasetInfoService datasetInfoService;

    @Autowired
    private ApolloCommonConfig apolloCommonConfig;

    @Value("${sample.filePath.prefix.v1}")
    private String sampleFilePathPrefix;

    @Value("${emr.cluster.tag:Data:Dataverse-V2:ali2}")
    private String emrClusterTagSh;

    @Value("${emr.cluster.tag.sgp:Model:GPT:ali2_sgp}")
    private String emrClusterTagSgp;

    @Value("${emr.cluster.tag.web:Ads:Adhoc:ali}")
    private String emrClusterTagWeb;

    @Value("${starrocks.cluster.key:119@@dataark@@Dataverse}")
    private String starrocksClusterKeySh;

    @Value("${starrocks.cluster.key.sgp:120@@dataark@@Dataverse}")
    private String starrocksClusterKeySgp;

    @Value("${spark.conf:}")
    private String sparkDefaultConf;

    @Value("${spark.conf.sgp:}")
    private String sparkDefaultConfSgp;

    @Value("${spark.conf.web:}")
    private String sparkDefaultConfWeb;

    @Value("${running.query.threshold:0}")
    private Integer runningQueryThreshold;

    public List<QueryRecordVO> getSampleRecordList(Long datasetVersionId) {
        List<QueryRecordDO> sampleRecords = queryRecordMapper.selectByDatasetVersionIdAndQueryType(
                datasetVersionId, QueryType.SAMPLE.name());
        return sampleRecords.stream().map(sampleRecord -> {
            QueryRecordVO vo = new QueryRecordVO();
            vo.setId(sampleRecord.getId());
            vo.setDatasetId(sampleRecord.getDatasetId());
//            vo.setDatasetVersion(sampleRecord.getDatasetVersion());
            vo.setDatasetVersionId(sampleRecord.getDatasetVersionId());
            vo.setQueryType(sampleRecord.getQueryType());
            vo.setSampleId(sampleRecord.getSampleId());
            vo.setSamplePath(sampleRecord.getSamplePath());
            vo.setQueryId(sampleRecord.getQueryId());
            vo.setQueryCondition(sampleRecord.getQueryCondition());
            vo.setState(sampleRecord.getState());
            vo.setErrorMsg(sampleRecord.getError());
            vo.setSubmitSql(sampleRecord.getSubmitSql());
            vo.setExecutedSql(sampleRecord.getExecutedSql());
            vo.setProgress(sampleRecord.getProgress());
            vo.setDownloadUrl(sampleRecord.getDownloadUrl());
            vo.setCreateBy(sampleRecord.getCreateBy());
            vo.setUpdateBy(sampleRecord.getUpdateBy());
            vo.setCreateTime(sampleRecord.getCreateTime());
            vo.setUpdateTime(sampleRecord.getUpdateTime());
            return vo;
        }).collect(Collectors.toList());
    }

    public QueryResultVO getQueryResult(Long id) {
        QueryRecordDO queryRecordDO = queryRecordMapper.selectById(id);
        if (ObjectUtils.isEmpty(queryRecordDO)) {
            throw new ServiceException("查询记录不存在");
        }
        String queryId = queryRecordDO.getQueryId();
        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("queryId为空");
        }
        QuerySPI.AsyncQueryResult asyncQueryResult;
        try {
            asyncQueryResult = capellaApiCaller.queryResult(queryId);
        } catch (Exception e) {
            String errMsg = "capella获取查询结果接口调用失败";
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
        if (asyncQueryResult.getRows() == 0) {
            return new QueryResultVO(null, null, false);
        }
        Collection<ColumnInfo> columns = asyncQueryResult.getColumns();
        List<String> columnsNames = Objects.requireNonNull(columns)
                .stream().map(ColumnInfo::getColName)
                .collect(Collectors.toList());
        List<String> sortedColumnNames = sortColumnNames(columnsNames);
        return new QueryResultVO(sortedColumnNames, asyncQueryResult.getData(), true);
    }

    @Override
    public RegisterCheckVO registerCheck(Long id, Long datasetVersionId) {
        RegisterCheckVO registerCheckVO = new RegisterCheckVO();
        registerCheckVO.setValid(true);
        if (apolloCommonConfig.isSkipRegisterCheck()) {
            return registerCheckVO;
        }
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(datasetVersionId);
        if (datasetVersionInfoDO == null) {
            log.error("数据集版本不存在");
            registerCheckVO.setValid(false);
            registerCheckVO.setErrorMsg("数据集版本不存在");
            return registerCheckVO;
        }
        // 只校验文本
        DatasetInfoDO datasetInfoDO = datasetInfoService.selectById(datasetVersionInfoDO.getDatasetId());
        if (datasetInfoDO == null) {
            log.error("数据集不存在");
            registerCheckVO.setValid(false);
            registerCheckVO.setErrorMsg("数据集不存在");
            return registerCheckVO;
        }
        // 原始数据集不校验
        if (datasetInfoDO.getIsRaw()) {
            log.info("数据集为原始数据集，不进行校验");
            return registerCheckVO;
        }
        if (!(MediaType.TEXT.getNameCn().equals(datasetInfoDO.getMediaType()) && !datasetInfoDO.getIsRaw())) {
            return registerCheckVO;
        }
        QueryResultVO queryResult = getQueryResult(id);
        // 判断列
        List<String> columnNames = queryResult.getColumns();
        if (CollectionUtils.isEmpty(columnNames)) {
            log.error("查询结果为空");
            registerCheckVO.setValid(false);
            registerCheckVO.setErrorMsg("查询结果为空");
            return registerCheckVO;
        }
        if (!columnNames.contains("meta") || !columnNames.contains("text")) {
            log.error("数据集必须包含meta和text列");
            registerCheckVO.setValid(false);
            registerCheckVO.setErrorMsg("数据集必须包含meta和text列");
            return registerCheckVO;
        }
        // meta列必须包含doc_id
        Collection<?> dataList = queryResult.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            log.error("查询结果数据为空");
            registerCheckVO.setValid(false);
            registerCheckVO.setErrorMsg("查询结果数据为空");
            return registerCheckVO;
        }
        List<Map<String, Object>> data = (List<Map<String, Object>>) dataList;
        for (Map<String, Object> stringObjectMap : data) {
            Object values = stringObjectMap.get("meta");
            if (values instanceof String) {
                String meta = (String) values;
                //把meta中的特殊字符替换/[\u0000-\u001F]/g
                meta = meta.replaceAll("[\u0000-\u001F]", "");
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    // 尝试解析 JSON 字符串
                    JsonNode jsonNode = objectMapper.readTree(meta);

                    // 判断是否是对象类型
                    if (jsonNode.isObject()) {
                        // 检查是否包含 key "doc_id"
                        if (!jsonNode.has("docid")) {
                            log.error("meta列必须包含docid");
                            registerCheckVO.setValid(false);
                            registerCheckVO.setErrorMsg("meta列必须包含docid");
                            return registerCheckVO;
                        }
                        if (!jsonNode.has("source")) {
                            log.error("meta列必须包含source");
                            registerCheckVO.setValid(false);
                            registerCheckVO.setErrorMsg("meta列必须包含source信息");
                            return registerCheckVO;
                        }
                    } else {
                        log.error("meta列不是合法的 JSON 格式");
                        registerCheckVO.setValid(false);
                        registerCheckVO.setErrorMsg("meta列不是合法的 JSON 格式");
                        return registerCheckVO;
                    }
                } catch (Exception e) {
                    log.error("meta列不是合法的 JSON 格式");
                    registerCheckVO.setValid(false);
                    registerCheckVO.setErrorMsg("meta列不是合法的 JSON 格式");
                    return registerCheckVO;
                }
            }
        }
        return registerCheckVO;
    }

    @Override
    public QueryRecordVO getLatestQueryRecord(Long datasetVersionId) {
        QueryRecordDO queryRecordDO = queryRecordMapper.selectLatestOneByDatasetVersionIdAndQueryType(datasetVersionId, QueryType.PREVIEW.name());
        if (ObjectUtils.isEmpty(queryRecordDO)) {
            log.warn("无最新查询记录, datasetVersionId:{}", datasetVersionId);
            return null;
        } else {
            QueryRecordVO queryRecord = getQueryRecord(queryRecordDO.getId());
            // 当前时间超过查询记录创建时间5天，则认为过期，重新触发查询
            if (isQueryOverdue(queryRecordDO)) {
                log.warn("查询记录过期, queryRecordId:{}, createTime:{}", queryRecord.getId(), queryRecord.getCreateTime());
                return null;
            }
            return queryRecord;
        }
    }

    @Override
    public QueryRecordVO getLatestSampleQueryRecord(Long sampleId) {
        QueryRecordDO queryRecordDO = queryRecordMapper.selectLatestSamplePreview(sampleId, QueryType.SAMPLE_PREVIEW.name());
        if (ObjectUtils.isEmpty(queryRecordDO)) {
            log.warn("无最新抽样预览记录, sampleId:{}", sampleId);
            return null;
        } else {
            QueryRecordVO queryRecord = getQueryRecord(queryRecordDO.getId());
            // 当前时间超过查询记录创建时间5天，则认为过期，重新触发查询
            if (isQueryOverdue(queryRecordDO)) {
                log.warn("抽样预览记录过期, queryRecordId:{}, createTime:{}", queryRecord.getId(), queryRecord.getCreateTime());
                return null;
            }
            return queryRecord;
        }
    }

    @Override
    public QueryRecordVO getQueryRecord(Long id) {

        QueryRecordDO queryRecordDO = queryRecordMapper.selectById(id);
        if (ObjectUtils.isEmpty(queryRecordDO)) {
            log.warn("查询记录不存在, id:{}", id);
            return null;
        }

        QueryRecordVO queryRecordVO = new QueryRecordVO();
        queryRecordVO.setId(queryRecordDO.getId());
        queryRecordVO.setDatasetId(queryRecordDO.getDatasetId());
//        queryRecordVO.setDatasetVersion(queryRecordDO.getDatasetVersion());
        queryRecordVO.setDatasetVersionId(queryRecordDO.getDatasetVersionId());
        queryRecordVO.setQueryType(queryRecordDO.getQueryType());
        queryRecordVO.setQueryId(queryRecordDO.getQueryId());
        queryRecordVO.setQueryCondition(queryRecordDO.getQueryCondition());
        queryRecordVO.setState(queryRecordDO.getState());
        queryRecordVO.setErrorMsg(queryRecordDO.getError());
        queryRecordVO.setSubmitSql(queryRecordDO.getSubmitSql());
        queryRecordVO.setExecutedSql(queryRecordDO.getExecutedSql());
        queryRecordVO.setProgress(queryRecordDO.getProgress());
        queryRecordVO.setDownloadUrl(queryRecordDO.getDownloadUrl());
        queryRecordVO.setRows(queryRecordDO.getRows());
        queryRecordVO.setSamplePath(queryRecordDO.getSamplePath());
        queryRecordVO.setCreateBy(queryRecordDO.getCreateBy());
        queryRecordVO.setUpdateBy(queryRecordDO.getUpdateBy());
        return queryRecordVO;
    }

    public Long updateQueryStatus(Long queryRecordId) {
        QueryRecordDO queryRecordDO = queryRecordMapper.selectById(queryRecordId);
        if (ObjectUtils.isEmpty(queryRecordDO)) {
            throw new ServiceException("查询记录不存在");
        }
        String queryId = queryRecordDO.getQueryId();
        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("queryId为空");
        }

        QuerySPI.ProgressResp progressResp;
        try {
            progressResp = capellaApiCaller.queryProgress(queryId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        log.info("更新查询状态, {}", progressResp.toString());
        queryRecordDO.setState(progressResp.getState());
        queryRecordDO.setProgress(progressResp.getProgress());
        queryRecordDO.setError(progressResp.getError());
        if (StringUtils.isNotEmpty(progressResp.getError())) {
            queryRecordDO.setState(QueryProgressState.ERROR.getCode());
        }
        SimpleUser simpleUser = new SimpleUser();
        simpleUser.setUserId("dataark");
        queryRecordDO.setUpdateBy(simpleUser);
        queryRecordMapper.updateStateAndProgressAndErrorById(queryRecordDO);
        return queryRecordDO.getId();
    }

    @Override
    @Transactional
    public Long createQueryRecord(QueryCreateRequest request, SimpleUser loginUser) {

        Integer runningRecordCount = queryRecordMapper.selectRunningRecordCount();
        if (runningRecordCount >= runningQueryThreshold) {
            throw new ServiceException("当前查询任务数过多，请稍后重试");
        }

        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(request.getDatasetVersionId());

        if (ObjectUtils.isEmpty(datasetVersionInfoDO)) {
            throw new ServiceException("数据集不存在");
        }

        if (!"oss".equalsIgnoreCase(datasetVersionInfoDO.getDataType())) {
            throw new ServiceException("仅开放oss数据集查询");
        }
        if (!"parquet".equalsIgnoreCase(datasetVersionInfoDO.getFileType())) {
            throw new ServiceException("仅支持parquet格式数据集预览");
        }
        if (request.getRows() == null) {
            throw new ServiceException("请指定抽样行数");
        }

        QueryType queryType = QueryType.of(request.getQueryType());

        // 预先生成query_record记录，待更新sql
        QueryRecordDO queryRecordDO = prepareQueryRecordDO(request, loginUser, datasetVersionInfoDO.getRegion());
        queryRecordMapper.insert(queryRecordDO);

        // 生成sql语句
        List<String> queryStatements = Lists.newLinkedList();
        String insertStatement = "", orderStatement = "";
        String fromStatement = null, engine = null;
        String selectStatement = "select *";
        if (QueryType.SAMPLE.equals(queryType)) {
            /**
             * INSERT OVERWRITE DIRECTORY 'oss://xhs-etl-test/xiaomi/parquet_test/business_zh_quiz_v0_pos'
             * USING parquet
             * select meta, text from parquet.`oss://lsh-oss-gpt-spam/user/xinghai/output/data/infer/merge/business_zh_quiz_v0_pos/` ORDER BY rand() LIMIT 1000;
             */
            engine = "spark";

            String bucketName = OSSUtils.parseBucketName(datasetVersionInfoDO.getOssPath());

            String keyPrefix = sampleFilePathPrefix;
            if (!keyPrefix.endsWith("/")) {
                keyPrefix += "/";
            }
            final String sampleFilePath = "oss://" + bucketName + "/" + keyPrefix + queryRecordDO.getId();
            if (!sampleFilePath.contains("/dataark/") || sampleFilePath.contains(" ")
                    || !sampleFilePath.matches("^.*/\\d+$")) {
                log.error("抽样路径不合法, sampleFilePath:{}", sampleFilePath);
                throw new ServiceException("抽样路径不合法");
            }
            queryRecordDO.setSamplePath(sampleFilePath);
            insertStatement = "INSERT OVERWRITE DIRECTORY '" + sampleFilePath + "' USING parquet";
            fromStatement = "from parquet.`" + datasetVersionInfoDO.getOssPath() + "`";
            orderStatement = "ORDER BY rand()";
        } else {
            engine = "starrocks";
            List<OSSObjectSummary> ossObjectSummaries = OSSUtils.listDir(datasetVersionInfoDO.getOssPath(), 200);
            if (ossObjectSummaries.isEmpty()) {
                throw new ServiceException("数据集为空");
            }
            OSSObjectSummary ossObjectSummary = ossObjectSummaries.get(new Random().nextInt(ossObjectSummaries.size()));
            final String filePath = "oss://" + ossObjectSummary.getBucketName() + "/" + ossObjectSummary.getKey();
            if (!filePath.contains(datasetVersionInfoDO.getOssPath())) {
                throw new ServiceException("数据集文件抽样路径错误");
            }

            String endpoint = "https://oss-" + queryRecordDO.getRegion() + "-internal.aliyuncs.com";
            String s3aFilePath = filePath.replace("oss://", "s3a://");
            fromStatement = "from files (\n" +
                    "  \"path\" = \"" + s3aFilePath + "\",\n" +
                    "  \"format\" = \"parquet\",\n" +
                    "  \"fs.s3a.access.key\" = \"" + OSSUtils.OSS_KEY + "\",\n" +
                    "  \"fs.s3a.secret.key\" = \"" + OSSUtils.OSS_SECRET + "\",\n" +
                    "  \"fs.s3a.endpoint\" = \"" + endpoint + "\"\n" +
                    ")";
            orderStatement = "ORDER BY RED_RANDOM() NULLS FIRST";
        }

        String whereStatement = "";
        if (StringUtils.isNotEmpty(request.getCondition())) {
            whereStatement = " where " + request.getCondition();
        }
        String limitStatement = "LIMIT " + request.getRows();
        queryStatements.add(insertStatement);
        queryStatements.add(selectStatement);
        queryStatements.add(fromStatement);
        queryStatements.add(whereStatement);
        queryStatements.add(orderStatement);
        queryStatements.add(limitStatement);
        String sql = String.join(" ", queryStatements);
        String applicationTag = String.format("query_record_id=%d,platform=dataark", queryRecordDO.getId());
        String queryId = submitCapellaQuery(sql, false, loginUser, applicationTag, engine, queryRecordDO.getRegion());
        // 更新sql和queryId
        queryRecordDO.setSubmitSql(sql);
        queryRecordDO.setQueryId(queryId);
        queryRecordDO.setState(QueryProgressState.RUNNING.name());

        queryRecordMapper.updateSubmitSqlAndQueryIdAndStateById(queryRecordDO);

        return queryRecordDO.getId();
    }

    @NotNull
    private static QueryRecordDO prepareQueryRecordDO(QueryCreateRequest request, SimpleUser loginUser, String region) {
        if (StringUtils.isEmpty(region)) {
            throw new ServiceException("查询的数据集缺少所属区域");
        }
        QueryRecordDO queryRecordDO = new QueryRecordDO();
        queryRecordDO.setDatasetVersionId(request.getDatasetVersionId());
        queryRecordDO.setQueryType(request.getQueryType());
        queryRecordDO.setQueryCondition(request.getCondition());
        queryRecordDO.setEngine(QUERY_ENGINE);
        queryRecordDO.setDownload(false);
        queryRecordDO.setRows(request.getRows());
        queryRecordDO.setCreateBy(loginUser);
        queryRecordDO.setRegion(region);
        return queryRecordDO;
    }

    @NotNull
    private static QueryRecordDO getSampleQueryRecordDO(QueryRecordDO sampleRecord, SimpleUser loginUser, String sql) {
        QueryRecordDO queryRecordDO = new QueryRecordDO();
        queryRecordDO.setDatasetId(sampleRecord.getDatasetId());
        queryRecordDO.setDatasetVersionId(sampleRecord.getDatasetVersionId());
        queryRecordDO.setSampleId(sampleRecord.getId());
        queryRecordDO.setSamplePath(sampleRecord.getSamplePath());
        queryRecordDO.setQueryType(QueryType.SAMPLE_PREVIEW.getValue());
        queryRecordDO.setQueryCondition(null);
        queryRecordDO.setRegion(Objects.requireNonNull(sampleRecord.getRegion()));
        queryRecordDO.setSubmitSql(sql);
        queryRecordDO.setEngine(QUERY_ENGINE);
        queryRecordDO.setDownload(false);
        queryRecordDO.setRows(100L);
        queryRecordDO.setCreateBy(loginUser);
        return queryRecordDO;
    }

    @NotNull
    private static QueryRecordDO getSampleDownloadRecordDO(QueryRecordDO sampleRecord, SimpleUser loginUser) {
        QueryRecordDO queryRecordDO = new QueryRecordDO();
        queryRecordDO.setDatasetId(sampleRecord.getDatasetId());
//        queryRecordDO.setDatasetVersion(sampleRecord.getDatasetVersion());
        queryRecordDO.setDatasetVersionId(sampleRecord.getDatasetVersionId());
        queryRecordDO.setSampleId(sampleRecord.getId());
        queryRecordDO.setSamplePath(sampleRecord.getSamplePath());
        queryRecordDO.setQueryType(QueryType.SAMPLE_DOWNLOAD.getValue());
        queryRecordDO.setState(QueryProgressState.RUNNING.getCode());
        queryRecordDO.setQueryCondition(null);
        queryRecordDO.setDownload(true);
        queryRecordDO.setCreateBy(loginUser);
        return queryRecordDO;
    }

    public Double queryProgress(String queryId) {
        try {
            QuerySPI.ProgressResp resp = capellaApiCaller.queryProgress(queryId);
            return resp.getProgress();
        } catch (Exception e) {
            String errMsg = "capella进度查询接口调用失败";
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
    }

    public Object getQueryResult(String queryId) {
        try {
            QuerySPI.AsyncQueryResult asyncQueryResult = capellaApiCaller.queryResult(queryId);
            return asyncQueryResult;
        } catch (Exception e) {
            String errMsg = "capella获取查询结果接口调用失败";
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
    }

    public Object queryLog(String queryId) {
        QuerySPI.SqlLogV2Response sqlLogV2Response = null;
        try {
            sqlLogV2Response = capellaApiCaller.queryLogV2(queryId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sqlLogV2Response;
    }

    @Deprecated
    public String submitQuery(Long queryRecordId, SimpleUser loginUser) {
        QueryRecordDO queryRecordDO = queryRecordMapper.selectById(queryRecordId);
        String sql = queryRecordDO.getSubmitSql();
        return dragonGateCaller.runSqlAsync(EngineType.SPARK_SQL, sql, loginUser.getEmail(), 30, false, new HashMap<>());
    }

    public String submitCapellaQuery(String sql, boolean download, SimpleUser loginUser, String applicationTag, String engine, String region) {
        String verbose;
        if ("spark".equalsIgnoreCase(engine)) {
            if (Region.AP_SOUTHEAST_1.getRegion().equals(region)){
                sql = sparkDefaultConfSgp + sql;
                verbose = emrClusterTagSgp;
            }else if (Region.CN_SHANGHAI_1.getRegion().equals(region)){
                sql = sparkDefaultConfWeb + sql;
                verbose = emrClusterTagWeb;
            }  else {
                sql = sparkDefaultConf + sql;
                verbose = emrClusterTagSh;
            }
        } else if ("starrocks".equalsIgnoreCase(engine)) {
            if (Region.AP_SOUTHEAST_1.getRegion().equals(region)){
                verbose = starrocksClusterKeySgp;
            } else {
                verbose = starrocksClusterKeySh;
            }
            engine = "mysql";
        } else {
            throw new ServiceException("不支持的引擎" + engine);
        }
        QuerySPI.RunSQLRequest sqlRequest = new QuerySPI.RunSQLRequest(engine,
                loginUser.getEmail(), sql, true, verbose, null,
                download, 50, null, null);
        try {
            QuerySPI.RunSQLResponse runSQLResponse = capellaApiCaller.runSql(null, sqlRequest);
            return runSQLResponse.getQueryId();
        } catch (Exception e) {
            String errMag = "capella任务提交接口调用失败";
            log.error(errMag, e);
            throw new ServiceException(errMag);
        }
    }

    @Override
    public Long createSampleQuery(SampleQueryCreateRequest request, SimpleUser loginUser) {
        Long sampleId = request.getSampleId();
        if (sampleId == null) {
            throw new ServiceException("请指定抽样id");
        }
        QueryRecordDO sampleRecord = queryRecordMapper.selectById(sampleId);
        if (Objects.isNull(sampleRecord) || !QueryType.SAMPLE.getValue().equalsIgnoreCase(sampleRecord.getQueryType())) {
            throw new ServiceException("抽样任务不存在");
        }
        if (CollectionUtils.isNotEmpty(getRunningSamplePreviewRecordBySampleId(sampleId))) {
            throw new ServiceException("有在执行中的预览任务，请稍后重试");
        }
        List<OSSObjectSummary> ossObjectSummaries = OSSUtils.listDir(sampleRecord.getSamplePath(), 200);
        if (ossObjectSummaries.isEmpty()) {
            throw new ServiceException("采样数据为空，采样数据集路径为:" + sampleRecord.getSamplePath());
        }

        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(sampleRecord.getDatasetVersionId());
        DatasetInfoDO datasetInfoDO = datasetInfoService.selectById(datasetVersionInfoDO.getDatasetId());
        Integer limit = 100;
        if (MediaType.MULTIMODAL.getNameCn().equals(datasetInfoDO.getMediaType())) {
            limit = 10; // 多模态数据集预览限制为10条
        }

        String s3aSamplePath = sampleRecord.getSamplePath().replace("oss://", "s3a://");
        if (!s3aSamplePath.endsWith("/")) {
            s3aSamplePath += "/";
        }
        s3aSamplePath += "*.parquet";
        String endpoint = "https://oss-" + Objects.requireNonNull(sampleRecord.getRegion()) + "-internal.aliyuncs.com";
        String sql = "select * from files (\n" +
                "  \"path\" = \"" + s3aSamplePath + "\",\n" +
                "  \"format\" = \"parquet\",\n" +
                "  \"fs.s3a.access.key\" = \"" + OSSUtils.OSS_KEY + "\",\n" +
                "  \"fs.s3a.secret.key\" = \"" + OSSUtils.OSS_SECRET + "\",\n" +
                "  \"fs.s3a.endpoint\" = \"" + endpoint + "\"\n" +
                ") LIMIT " + limit + ";";

        QueryRecordDO queryRecordDO = getSampleQueryRecordDO(sampleRecord, loginUser, sql);
        String applicationTag = String.format("query_record_id=%d,platform=dataark", queryRecordDO.getId());
        String queryId = submitCapellaQuery(queryRecordDO.getSubmitSql(), queryRecordDO.getDownload(), loginUser, applicationTag, "starrocks", sampleRecord.getRegion());
        queryRecordDO.setQueryId(queryId);
        queryRecordDO.setState(QueryProgressState.RUNNING.name());

        queryRecordMapper.insert(queryRecordDO);
        return queryRecordDO.getId();
    }

    @Override
    public Long sampleDownload(SampleQueryCreateRequest request, SimpleUser loginUser) {
        Long sampleId = request.getSampleId();
        if (sampleId == null) {
            throw new ServiceException("请指定抽样id");
        }
        QueryRecordDO sampleRecord = queryRecordMapper.selectById(sampleId);
        if (Objects.isNull(sampleRecord) || !QueryType.SAMPLE.getValue().equalsIgnoreCase(sampleRecord.getQueryType())) {
            throw new ServiceException("抽样任务不存在");
        }
        if (StringUtils.isEmpty(sampleRecord.getSamplePath())) {
            throw new ServiceException("抽样文件还未生成，无法下载");
        }
        QueryRecordDO sampleDownloadRecordDO = getSampleDownloadRecordDO(sampleRecord, loginUser);
        queryRecordMapper.insert(sampleDownloadRecordDO);
        CompletableFuture.runAsync(() -> sampleDownload(sampleDownloadRecordDO, loginUser));
        return sampleDownloadRecordDO.getId();
    }

    @Override
    public void cancelSample(SampleQueryCreateRequest request, SimpleUser loginUser) {
        Long sampleId = request.getSampleId();
        if (sampleId == null) {
            throw new ServiceException("请指定抽样id");
        }
        QueryRecordDO sampleRecord = queryRecordMapper.selectById(sampleId);
        if (Objects.isNull(sampleRecord) || !QueryType.SAMPLE.getValue().equalsIgnoreCase(sampleRecord.getQueryType())) {
            throw new ServiceException("抽样任务不存在");
        }
        String queryId = sampleRecord.getQueryId();
        if (StringUtils.isEmpty(queryId)) {
            throw new ServiceException("queryId为空");
        }
        try {
            log.info("开始取消采样任务, sampleId: {}, queryId: {}", sampleId, queryId);
            QuerySPI.CancelRequest cancelRequest = new QuerySPI.CancelRequest(queryId);
            capellaApiCaller.cancel(cancelRequest);
        } catch (Exception e) {
            String errMsg = "capella 取消任务接口调用失败";
            log.error(errMsg, e);
            throw new ServiceException(errMsg);
        }
        QueryRecordDO updateDO = new QueryRecordDO();
        updateDO.setId(sampleId);
        updateDO.setState(QueryProgressState.KILLED.getCode());
        updateDO.setUpdateBy(loginUser);
        queryRecordMapper.updateById(updateDO);
    }

    public List<QueryRecordDO> getRunningSamplePreviewRecordBySampleId(Long sampleId) {
        LambdaQueryWrapper<QueryRecordDO> wrapper = Wrappers.<QueryRecordDO>lambdaQuery()
                .eq(QueryRecordDO::getSampleId, sampleId)
                .eq(QueryRecordDO::getQueryType, QueryType.SAMPLE_PREVIEW.getValue())
                .eq(QueryRecordDO::getState, QueryProgressState.RUNNING.getCode());
        return queryRecordMapper.selectList(wrapper);
    }

    public void sampleDownload(QueryRecordDO queryRecordDO, SimpleUser user) {
        try {
            String url = OSSUtils.parquetToExcel(queryRecordDO.getId(), queryRecordDO.getSamplePath(),
                    "sample_download_" + queryRecordDO.getId() + "_" + System.currentTimeMillis() + ".xlsx");
            QueryRecordDO updateDO = new QueryRecordDO();
            updateDO.setId(queryRecordDO.getId());
            updateDO.setDownloadUrl(url);
            updateDO.setState(QueryProgressState.FINISHED.getCode());
            queryRecordMapper.updateById(updateDO);
            if (StringUtils.isEmpty(url)) {
                log.info("sample parquet is empty, sample id:{} url is empty", queryRecordDO.getSampleId());
                crossroadApiCaller.sendMd2Wechat(Lists.newArrayList(user.getEmail()), buildEmptyUrl(queryRecordDO.getSampleId()));
                return;
            }
            crossroadApiCaller.sendMd2Wechat(Lists.newArrayList(user.getEmail()), buildDownLoadUrl(queryRecordDO.getSampleId(), url));
        } catch (Exception e) {
            alarmService.alarmVChat("sampleDownload error, queryRecord id:" + queryRecordDO.getId());
            log.error("sampleDownload error : {} ", queryRecordDO.getId(), e);
        }
    }

    private String buildDownLoadUrl(Long id, String url) {
        return "## 采样结果下载(" + id + ")" + "\n" +
                "[点击下载](" + url + ")" + "\n";
    }

    private String buildEmptyUrl(Long id) {
        return "## 采样结果为空，无内容下载(" + id + ")";
    }

    private Boolean isQueryOverdue(QueryRecordDO queryRecordDO) {
        LocalDateTime now = LocalDateTime.now();
        return queryRecordDO.getCreateTime().plusDays(5).isBefore(now);
    }

    private List<String> sortColumnNames(List<String> columnNames) {
        List<String> metaList = new ArrayList<>();
        List<String> textList = new ArrayList<>();
        List<String> valueList = new ArrayList<>();
        List<String> otherList = new ArrayList<>();
        for (String columnName : columnNames) {
            if ("meta".equals(columnName)) {
                metaList.add(columnName);
            } else if ("text".equals(columnName)) {
                textList.add(columnName);
            } else if ("value".equals(columnName)) {
                valueList.add(columnName);
            }
            else {
                otherList.add(columnName);
            }
        }
        // 清空原始集合
        columnNames.clear();

        // 将 "meta" 和 "text" 添加到集合的最前面，然后添加其他字符串
        columnNames.addAll(metaList);
        columnNames.addAll(textList);
        columnNames.addAll(valueList);
        columnNames.addAll(otherList);
        return columnNames;
    }

}
