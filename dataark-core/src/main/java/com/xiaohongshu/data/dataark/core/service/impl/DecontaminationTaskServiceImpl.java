package com.xiaohongshu.data.dataark.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.data.dataark.core.service.DecontaminationTaskService;
import com.xiaohongshu.data.dataark.dao.entity.DatasetInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.entity.DecontaminationTaskDO;
import com.xiaohongshu.data.dataark.dao.enums.DecontaminationTaskStatus;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.DecontaminationTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2025/8/12 11:40
 */

@Service
@Slf4j
public class DecontaminationTaskServiceImpl implements DecontaminationTaskService {

    @Resource
    private DecontaminationTaskMapper decontaminationTaskMapper;

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Override
    public Boolean createDecontaminationTask(DatasetInfoDO datasetInfoDO, DatasetVersionInfoDO datasetVersionInfoDO) {

        String datasetVersionName = datasetInfoDO.getCode() + "_v" + datasetVersionInfoDO.getVersion();

        DecontaminationTaskDO decontaminationTaskDO = new DecontaminationTaskDO();
        decontaminationTaskDO.setTaskId(UUID.randomUUID().toString());
        decontaminationTaskDO.setDatasetId(datasetInfoDO.getId());
        decontaminationTaskDO.setDatasetVersionId(datasetVersionInfoDO.getId());
        decontaminationTaskDO.setDatasetVersionName(datasetVersionName);
        decontaminationTaskDO.setFilePath(datasetVersionInfoDO.getOssPath());
        decontaminationTaskDO.setStatus(DecontaminationTaskStatus.RUNNING.getKey());
        decontaminationTaskDO.setStartTime(LocalDateTime.now());
        decontaminationTaskMapper.insert(decontaminationTaskDO);

        return true;
    }

    @Override
    public Boolean updateDecontaminationTask(String taskId, String status, String errorMsg) {
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(status)) {
            throw new IllegalArgumentException("taskId and status cannot be null or empty");
        }
        LambdaQueryWrapper<DecontaminationTaskDO> wrapper = Wrappers.<DecontaminationTaskDO>lambdaQuery()
                .eq(DecontaminationTaskDO::getTaskId, taskId);

        DecontaminationTaskDO decontaminationTaskDO = decontaminationTaskMapper.selectOne(wrapper);
        if (decontaminationTaskDO == null) {
            throw new IllegalArgumentException("DecontaminationTaskDO not found for taskId: " + taskId);
        }
        DecontaminationTaskStatus decontaminationTaskStatus = DecontaminationTaskStatus.parse(status);
        decontaminationTaskDO.setStatus(decontaminationTaskStatus.getKey());
        decontaminationTaskDO.setEndTime(LocalDateTime.now());
        if (decontaminationTaskStatus == DecontaminationTaskStatus.FAILED) {
            decontaminationTaskDO.setErrorMsg(errorMsg);
        } else {
            decontaminationTaskDO.setErrorMsg(null);
        }
        decontaminationTaskMapper.updateById(decontaminationTaskDO);
        return true;
    }



    @Override
    public List<DecontaminationTaskDO> getRunningDecontaminationTasks() {
    LambdaQueryWrapper<DecontaminationTaskDO> wrapper = Wrappers.<DecontaminationTaskDO>lambdaQuery()
            .eq(DecontaminationTaskDO::getStatus, DecontaminationTaskStatus.RUNNING.getKey());
    List<DecontaminationTaskDO> runningTasks = decontaminationTaskMapper.selectList(wrapper);
        return runningTasks;
    }

    @Override
    public List<DecontaminationTaskDO> getDecontaminationTasksByDatasetId(Long datasetId) {
    LambdaQueryWrapper<DecontaminationTaskDO> wrapper = Wrappers.<DecontaminationTaskDO>lambdaQuery()
            .eq(DecontaminationTaskDO::getDatasetId, datasetId);
    List<DecontaminationTaskDO> tasks = decontaminationTaskMapper.selectList(wrapper);
        return tasks;
    }

    @Override
    public Boolean decontaminationTasksDone(Long datasetVersionId) {
        LambdaQueryWrapper<DatasetVersionInfoDO> wrapper = Wrappers.<DatasetVersionInfoDO>lambdaQuery()
                .eq(DatasetVersionInfoDO::getId, datasetVersionId);
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectOne(wrapper);
        return decontaminationTasksDone(datasetVersionInfoDO);
    }

    @Override
    public Boolean decontaminationTasksDone(DatasetVersionInfoDO datasetVersionInfoDO) {
        if(ObjectUtils.isEmpty(datasetVersionInfoDO)) {
            throw new IllegalArgumentException("DatasetVersionInfoDO cannot be null");
        }
        if (!datasetVersionInfoDO.getNeedDecontamination()){
            log.info("Dataset version {} does not require decontamination, skipping task completion.", datasetVersionInfoDO.getId());
            return true;
        }
        LambdaQueryWrapper<DecontaminationTaskDO> wrapper = Wrappers.<DecontaminationTaskDO>lambdaQuery()
                .eq(DecontaminationTaskDO::getDatasetVersionId, datasetVersionInfoDO.getId());
        DecontaminationTaskDO decontaminationTaskDO = decontaminationTaskMapper.selectOne(wrapper);

        if (ObjectUtils.isEmpty(decontaminationTaskDO)){
            log.warn("No decontamination task found for dataset version ID: {}", datasetVersionInfoDO.getId());
            return false;
        }
        DecontaminationTaskStatus taskStatus = DecontaminationTaskStatus.parse(decontaminationTaskDO.getStatus());
        if (taskStatus.isFinished()) {
            log.info("Decontamination task for dataset version ID {} is already finished with status: {}",
                     datasetVersionInfoDO.getId(), taskStatus.getDes());
            return true;
        }
        return false;
    }

    @Override
    public DecontaminationTaskDO getDecontaminationTaskByTaskId(String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            throw new IllegalArgumentException("taskId cannot be null or empty");
        }
        LambdaQueryWrapper<DecontaminationTaskDO> wrapper = Wrappers.<DecontaminationTaskDO>lambdaQuery()
                .eq(DecontaminationTaskDO::getTaskId, taskId);

        DecontaminationTaskDO decontaminationTaskDO = decontaminationTaskMapper.selectOne(wrapper);
        if (decontaminationTaskDO == null) {
            throw new IllegalArgumentException("DecontaminationTaskDO not found for taskId: " + taskId);
        }
        return decontaminationTaskDO;
    }


}
