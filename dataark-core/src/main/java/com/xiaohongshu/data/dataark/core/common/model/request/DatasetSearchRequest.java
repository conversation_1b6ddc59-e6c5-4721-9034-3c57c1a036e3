package com.xiaohongshu.data.dataark.core.common.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetSearchRequest {
    /**
     * 数据集名称
     */
    private String name;

    /**
     * 数据集id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 数据集类型
     */
    private String mediaType;

    /**
     * 数据集内容类型
     */
    private String contentType;

    /**
     * 数据集语言
     */
    private String language;

    /**
     * 数据集状态
     */
    private String status;

    /**
     * 是否我的数据集
     */
    private Boolean isMine;
}
