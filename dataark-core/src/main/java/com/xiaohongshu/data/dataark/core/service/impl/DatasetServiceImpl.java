package com.xiaohongshu.data.dataark.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.data.dataark.core.common.enums.*;
import com.xiaohongshu.data.dataark.core.common.model.DatasetType;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetChangeOwnerRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetCommitRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetDeleteRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.DatasetVersionIndexRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.data.dataark.core.config.apollo.ApolloCommonConfig;
import com.xiaohongshu.data.dataark.core.es.EsService;
import com.xiaohongshu.data.dataark.core.gravitino.service.GravitinoFilesetService;
import com.xiaohongshu.data.dataark.core.manager.oa.OaEmployeeManager;
import com.xiaohongshu.data.dataark.core.pai.service.PaiService;
import com.xiaohongshu.data.dataark.core.service.*;
import com.xiaohongshu.data.dataark.core.snapshot.OssSnapshotService;
import com.xiaohongshu.data.dataark.core.utils.DateUtils;
import com.xiaohongshu.data.dataark.dao.entity.*;
import com.xiaohongshu.data.dataark.dao.entity.dataset.*;
import com.xiaohongshu.data.dataark.dao.enums.DataSourceType;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetInfoMapper;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
@Service
@Slf4j
public class DatasetServiceImpl implements DatasetService {

    private static final String DEFAULT_REGION = "cn-shanghai";

    @Resource
    private GravitinoFilesetService gravitinoFilesetService;

    @Resource
    private DatasetVersionInfoService datasetVersionInfoService;

    @Resource
    private DatasetInfoMapper datasetInfoMapper;

    @Resource
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Resource
    private DecontaminationTaskService decontaminationTaskService;

    @Resource
    private OaEmployeeManager oaEmployeeManager;

    @Resource
    private ApproveRecordService approveRecordService;

    @Resource
    private DatasetInfoService datasetInfoService;

    @Resource
    private ApolloCommonConfig apolloCommonConfig;

    @Resource
    private PaiService paiService;

    @Resource
    private IndexTaskService indexTaskService;

    @Resource
    private VectorService vectorService;

    @Resource
    private EsService esService;

    @Value("${third_party.milvus.uri}")
    private String milvusUri;

    @Autowired
    private OssSnapshotService ossSnapshotService;

    @Override
    public List<String> contentTypesNameCn(String mediaType) {
        // 查询MediaType枚举类中nameCn = mediaType的枚举类
        Optional<MediaType> first = Arrays.stream(MediaType.values())
                .filter(e -> e.getNameCn().equals(mediaType))
                .findFirst();
        if (!first.isPresent()) {
            throw new ServiceException("无效的数据集类型");
        }

        return Arrays.stream(ContentType.values())
                .filter(e -> e.getMediaType().getCode().equals(first.get().getCode()))
                .map(ContentType::getNameCn)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getLanguages() {
        return Arrays.stream(Languages.values())
                .map(Languages::getNameCn)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getStatus() {
        return Arrays.stream(DatasetVersionStatus.values())
                .map(DatasetVersionStatus::toMap)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getRegions() {
        return Arrays.stream(Region.values())
                .map(Region::toMap)
                .collect(Collectors.toList());
    }

    /**
     * 创建或更新数据集，更新操作仅支持草稿态
     *
     * @param datasetWarpVO
     * @param loginUser
     * @return
     */
    @Transactional
    public DatasetWarpVO createDataset(DatasetWarpVO datasetWarpVO, SimpleUser loginUser) {
        DatasetInfoVO datasetInfoVO = datasetWarpVO.getBasic();
        DatasetInfoDO datasetInfoDO = upsertDatasetInfo(datasetInfoVO, loginUser);
        Long datasetId = datasetInfoDO.getId();

        DatasetVersionInfoVO datasetVersionInfoVO = datasetWarpVO.getPayload();
        datasetVersionInfoVO.setDatasetId(datasetId);
        // 初始状态为草稿
        datasetVersionInfoVO.setStatus(DatasetVersionStatus.DRAFT.getStatus());

        DatasetVersionInfoDO datasetVersionInfoDO = upsertDatasetVersionInfoUserDefined(datasetVersionInfoVO, loginUser);

        // 回填id
        datasetInfoVO.setId(datasetId);
        datasetInfoVO.setOwner(loginUser);
        datasetVersionInfoVO.setId(datasetVersionInfoDO.getId());
        datasetVersionInfoVO.setDatasetId(datasetId);
        datasetVersionInfoVO.setStatus(datasetVersionInfoDO.getStatus());
        datasetVersionInfoVO.setCreateTime(datasetVersionInfoDO.getCreateTime());
        datasetVersionInfoVO.setUpdateTime(datasetVersionInfoDO.getUpdateTime());
        datasetVersionInfoVO.setSnapshotStatus(datasetVersionInfoDO.getSnapshotStatus());
        datasetVersionInfoVO.setSnapshotPath(datasetVersionInfoDO.getSnapshotPath());
        datasetVersionInfoVO.setCreateBy(datasetVersionInfoVO.getCreateBy());
        datasetVersionInfoVO.setUpdateBy(datasetVersionInfoVO.getUpdateBy());

        return datasetWarpVO;
    }

    /**
     * 数据集创建活更新，
     *
     * @param datasetInfoVO
     * @param loginUser
     * @return
     */
    public DatasetInfoDO upsertDatasetInfo(DatasetInfoVO datasetInfoVO, SimpleUser loginUser) {
        DatasetType datasetType = DatasetType.of(
                datasetInfoVO.getMediaType(), datasetInfoVO.getContentType()
        );

        DatasetInfoDO datasetInfoDO = getDatasetInfoDO(datasetInfoVO, loginUser, datasetType);
        if (datasetInfoService.selectNoDraftByName(datasetInfoVO.getName()) != null) {
            throw new ServiceException("数据集名称已存在，请修改");
        }
        if (datasetInfoService.selectNoDraftByCode(datasetInfoVO.getCode()) != null) {
            throw new ServiceException("数据集code已存在，请修改");
        }
        if (Objects.isNull(datasetInfoVO.getId())) {
            datasetInfoDO.setCreateBy(loginUser);
            datasetInfoMapper.insert(datasetInfoDO);
        } else {
            DatasetInfoDO existDatasetInfoDO = datasetInfoMapper.selectById(datasetInfoVO.getId());
            if (ObjectUtils.isEmpty(existDatasetInfoDO)) {
                throw new ServiceException("数据集id不存在, 无法更新");
            }
            // 如果有非草稿态的数据集不允许更新
            List<DatasetVersionInfoDO> datasetVersionInfoDOS = datasetVersionInfoMapper.selectNonDraftByDatasetId(datasetInfoVO.getId());

            boolean onlyDraftVersion = Optional.ofNullable(datasetVersionInfoDOS).orElse(Collections.emptyList())
                    .stream().allMatch(v -> v.getStatus().equals(DatasetVersionStatus.DRAFT.getStatus()));
            if (!onlyDraftVersion) {
                log.warn("数据集{}存在已经提交过的版本,不允许修改", datasetInfoVO.getId());
                throw new ServiceException("数据集已提交，不允许更新");
            }
            datasetInfoDO.setUpdateBy(loginUser);
            datasetInfoDO.setUpdateTime(LocalDateTime.now());
            datasetInfoMapper.updateById(datasetInfoDO);
        }
        return datasetInfoDO;
    }

    @NotNull
    private static DatasetInfoDO getDatasetInfoDO(DatasetInfoVO datasetInfoVO, SimpleUser loginUser, DatasetType datasetType) {
        DatasetInfoDO datasetInfoDO = new DatasetInfoDO();
        datasetInfoDO.setId(datasetInfoVO.getId());
        datasetInfoDO.setName(datasetInfoVO.getName());
        datasetInfoDO.setCode(datasetInfoVO.getCode());
        datasetInfoDO.setDescription(datasetInfoVO.getDescription());
        datasetInfoDO.setMediaType(datasetType.getMediaType().getNameCn());
        datasetInfoDO.setContentType(datasetType.getContentType().getNameCn());
        datasetInfoDO.setIsRaw(datasetInfoVO.getIsRaw());
        datasetInfoDO.setOwner(loginUser);
        return datasetInfoDO;
    }

    @Transactional
    public DatasetVersionInfoDO upsertDatasetVersionInfoUserDefined(DatasetVersionInfoVO datasetVersionInfoVO, SimpleUser loginUser) {
        DatasetVersionInfoDO datasetVersionInfoDO = new DatasetVersionInfoDO();
        datasetVersionInfoDO.setId(datasetVersionInfoVO.getId());
        datasetVersionInfoDO.setDatasetId(datasetVersionInfoVO.getDatasetId());
        datasetVersionInfoDO.setDataType(datasetVersionInfoVO.getDataType());
        if (DataType.oss.name().equals(datasetVersionInfoVO.getDataType())) {
            String ossPath = datasetVersionInfoVO.getMetadata().getLocation();
            if (ossPath.endsWith("/")) {
                ossPath = ossPath.substring(0, ossPath.length() - 1);
            }
            datasetVersionInfoDO.setOssPath(ossPath);
            datasetVersionInfoDO.setFileType(datasetVersionInfoVO.getMetadata().getFileFormat());
            // 数据集中的oss路径末尾不会包含'/'
            DatasetVersionInfoDO existVersion = datasetVersionInfoService.selectNoDraftByOssPath(ossPath);
            if (Objects.nonNull(existVersion)) {
                DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(existVersion.getDatasetId());
                String errorMsg = String.format("数据集版本路径已存在,请勿重复添加,对应数据集名称: %s, 版本: %s", datasetInfoDO.getName(), existVersion.getVersion());
                log.error(errorMsg);
                throw new ServiceException(errorMsg);
            }

        } else if (DataType.iceberg.name().equals(datasetVersionInfoVO.getDataType())) {
            datasetVersionInfoDO.setCatalog(datasetVersionInfoVO.getMetadata().getCatalog());
            datasetVersionInfoDO.setDbName(datasetVersionInfoVO.getMetadata().getDbName());
            datasetVersionInfoDO.setTableName(datasetVersionInfoVO.getMetadata().getTableName());
        }
        datasetVersionInfoDO.setLanguage(datasetVersionInfoVO.getLanguage());
        datasetVersionInfoDO.setDescription(datasetVersionInfoVO.getDescription());
        datasetVersionInfoDO.setIsCustom(datasetVersionInfoVO.getIsCustom());
        datasetVersionInfoDO.setRedocLink(datasetVersionInfoVO.getRedocLink());
//        datasetVersionInfoDO.setDataSources(datasetVersionInfoVO.getDataSources());
        datasetVersionInfoDO.setDataSources(JSONObject.toJSONString(datasetVersionInfoVO.getDataSources()));
        datasetVersionInfoDO.setProcessingTasks(datasetVersionInfoVO.getProcessingTasks());
        datasetVersionInfoDO.setDataFunnel(datasetVersionInfoVO.getDataFunnel());
        datasetVersionInfoDO.setDataValidation(datasetVersionInfoVO.getDataValidation());
        datasetVersionInfoDO.setExperimentValidation(datasetVersionInfoVO.getExperimentValidation());
        // 第一版仅支持上海oss数据集
        datasetVersionInfoDO.setRegion(datasetVersionInfoVO.getRegion());
        datasetVersionInfoDO.setStatus(datasetVersionInfoVO.getStatus());

        if (Objects.isNull(datasetVersionInfoVO.getId())) {
            datasetVersionInfoDO.setCreateBy(loginUser);
            datasetVersionInfoMapper.insert(datasetVersionInfoDO);
        } else {
            DatasetVersionInfoDO existVersion = datasetVersionInfoMapper.selectById(datasetVersionInfoVO.getId());
            if (ObjectUtils.isEmpty(existVersion)) {
                log.warn("数据集版本id: {}, 不存在, 无法更新", datasetVersionInfoVO.getId());
                throw new ServiceException("数据集版本id存存在");
            }
            if (!Objects.requireNonNull(datasetVersionInfoVO.getDatasetId()).equals(existVersion.getDatasetId())) {
                throw new ServiceException("不允许更新datasetId");
            }
            if (!DatasetVersionStatus.DRAFT.getStatus().equals(existVersion.getStatus())) {
                log.warn("数据集版本id: {}, 状态为{}, 无法更新", datasetVersionInfoVO.getId(), existVersion.getStatus());
                throw new ServiceException("数据集版本已注册不允许更新");
            }
            datasetVersionInfoDO.setUpdateBy(loginUser);
            datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
            datasetVersionInfoMapper.updateById(datasetVersionInfoDO);
        }

        // 更新备份状态和目标路径
        String snapshotPath = ossSnapshotService.snapshotPathFormat(datasetVersionInfoDO);
        datasetVersionInfoDO.setSnapshotPath(snapshotPath);
        datasetVersionInfoDO.setSnapshotStatus(OssSnapshotStatus.BLOCKED.getStatus());
        datasetVersionInfoMapper.updateById(datasetVersionInfoDO);
        return datasetVersionInfoDO;

    }

    @Deprecated
    public Integer nextVersion(Long datasetId) {
        if (datasetId == null) {
            throw new ServiceException("数据集id为空");
        }

        Integer maxVersion = datasetVersionInfoMapper.selectMaxVersion(datasetId);
        if (maxVersion == null) {
            return 1;
        } else {
            return maxVersion + 1;
        }
    }

    private Integer versionNumberFormat(String version) {
        if (StringUtils.isEmpty(version)) {
            return null;
        }
        return Integer.parseInt(version.replaceAll("v", ""));
    }

    @Override
    @Transactional
    public Long commitDataset(DatasetCommitRequest commitRequest, SimpleUser loginUser) {
        if (ObjectUtils.isEmpty(commitRequest) ||
                commitRequest.getDatasetVersionId() == null) {
            throw new ServiceException("数据集提交请求参数无效");
        }

        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(commitRequest.getDatasetVersionId());
        DatasetVersionStatus status = DatasetVersionStatus.getByStatus(datasetVersionInfoDO.getStatus());
        if (!DatasetVersionStatus.DRAFT.equals(status)) {
            String msg = String.format("数据集状态为%s，无法提交", datasetVersionInfoDO.getStatus());
            log.error(msg);
            throw new ServiceException(msg);
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        // 校验name/code/路径
        DatasetInfoDO sameNameDataset = datasetInfoService.selectNoDraftByNameAndId(datasetInfoDO.getName(), datasetInfoDO.getId());
        if (sameNameDataset != null) {
            throw new ServiceException("数据集名称已存在，请修改");
        }
        DatasetInfoDO sameCodeDataset = datasetInfoService.selectNoDraftByCodeAndId(datasetInfoDO.getCode(), datasetInfoDO.getId());
        if (sameCodeDataset != null) {
            throw new ServiceException("数据集code已存在，请修改");
        }
        if (datasetVersionInfoDO.getDataType().equalsIgnoreCase(DataType.oss.name())) {
            String ossPath = datasetVersionInfoDO.getOssPath();
            if (ossPath.endsWith("/")) {
                ossPath = ossPath.substring(0, ossPath.length() - 1);
            }
            DatasetVersionInfoDO sameOssPathDataset = datasetVersionInfoService.selectNoDraftByOssPathAndId(ossPath, datasetVersionInfoDO.getId());
            if (sameOssPathDataset != null) {
                throw new ServiceException("数据集版本路径已存在,请勿重复添加");
            }
        }
        if (commitRequest.getNeedDecontamination()) {
            // 如果需要去污，先创建去污任务
            boolean createDecontaminationTask = decontaminationTaskService.createDecontaminationTask(datasetInfoDO, datasetVersionInfoDO);
            if (!createDecontaminationTask) {
                log.error("创建去污任务失败, 数据集版本id: {}", commitRequest.getDatasetVersionId());
                throw new ServiceException("创建去污任务失败");
            }
        }

        // 注册到gravitino
        // 查询是否有非草稿态的version info
        boolean existNotDraftVersion = datasetVersionInfoService.existNotDraftVersion(datasetVersionInfoDO.getDatasetId());
        Integer version;
        if (existNotDraftVersion) {
            // 如果有非草稿态的version info，说明是新增版本
            version = gravitinoFilesetService.addFilesetVersion(datasetInfoDO.getCode(), datasetVersionInfoDO.getOssPath());
        } else {
            // 如果都是草稿态的version info，说明是第一次提交注册gravitino
            version = gravitinoFilesetService.createFileset(datasetInfoDO.getCode(), datasetVersionInfoDO.getOssPath());
        }
        datasetVersionInfoDO.setNeedDecontamination(commitRequest.getNeedDecontamination());
        datasetVersionInfoDO.setStatus(DatasetVersionStatus.UNVERIFIED.getStatus());
        datasetVersionInfoDO.setSnapshotStatus(OssSnapshotStatus.PENDING.getStatus());
        datasetVersionInfoDO.setVersion(version);
        datasetVersionInfoDO.setUpdateBy(loginUser);
        Long affected = datasetVersionInfoMapper.updateStateAndVersionById(datasetVersionInfoDO);
        log.info("数据集版本提交成功, 数据集版本id: {}, 版本: {}, 更新行数: {}", commitRequest.getDatasetVersionId(), version, affected);

        // 更新dataset_info中的MaxVersionId
        datasetInfoDO.setMaxVersionId(datasetVersionInfoDO.getId());
        datasetInfoDO.setUpdateTime(LocalDateTime.now());
        datasetInfoMapper.updateById(datasetInfoDO);
        return datasetVersionInfoDO.getId();
    }

    public DatasetVersionInfoVO createVersion(DatasetVersionInfoVO datasetVersionInfoVO, SimpleUser loginUser) {

//        Integer version = nextVersion(datasetVersionInfoVO.getDatasetId());
//
//        datasetVersionInfoVO.setVersion(String.valueOf(version));
        // 判断region不能不一样
//        List<DatasetVersionInfoDO> datasetVersionInfoDOS = datasetVersionInfoService.selectNoDraftByDatasetId(datasetVersionInfoVO.getDatasetId());
//        if (CollectionUtils.isNotEmpty(datasetVersionInfoDOS)) {
//            DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoDOS.get(0);
//            if (!datasetVersionInfoDO.getRegion().equals(datasetVersionInfoVO.getRegion())) {
//                throw new ServiceException("数据集版本region不能和历史版本不一致");
//            }
//        }
        // 初始状态为草稿
        datasetVersionInfoVO.setStatus(DatasetVersionStatus.DRAFT.getStatus());
        DatasetVersionInfoDO datasetVersionInfoDO = upsertDatasetVersionInfoUserDefined(datasetVersionInfoVO, loginUser);

        log.info("创建数据集版本成功, 数据集id: {}, 版本: {}", datasetVersionInfoDO.getDatasetId(), datasetVersionInfoDO.getVersion());
        // 回填id
        datasetVersionInfoVO.setId(datasetVersionInfoDO.getId());
        // 回填快照状态和路径
        datasetVersionInfoVO.setSnapshotStatus(datasetVersionInfoDO.getSnapshotStatus());
        datasetVersionInfoVO.setSnapshotPath(datasetVersionInfoDO.getSnapshotPath());

        // 更新maxVersionId
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
//        datasetInfoDO.setMaxVersionId(datasetVersionInfoDO.getId());
        datasetInfoDO.setUpdateBy(loginUser);
        Long affectedRows = datasetInfoMapper.updateMaxVersionIdById(datasetInfoDO);
        log.info("更新数据集maxVersionId成功,数据集id:{}, maxVersionId:{}, 更新行数:{}",
                datasetInfoDO.getId(), datasetInfoDO.getMaxVersionId(), affectedRows);

        return datasetVersionInfoVO;
    }

    @Override
    public DatasetVersionInfoVO updateDatasetVersionOutput(DatasetVersionInfoVO datasetVersionInfoVO, SimpleUser loginUser) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(datasetVersionInfoVO.getId());
        if (ObjectUtils.isEmpty(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        datasetVersionInfoDO.setIsCustom(datasetVersionInfoVO.getIsCustom());
        datasetVersionInfoDO.setRedocLink(datasetVersionInfoVO.getRedocLink());
        datasetVersionInfoDO.setDataSources(JSONObject.toJSONString(datasetVersionInfoVO.getDataSources()));
        datasetVersionInfoDO.setProcessingTasks(datasetVersionInfoVO.getProcessingTasks());
        datasetVersionInfoDO.setDataFunnel(datasetVersionInfoVO.getDataFunnel());
        datasetVersionInfoDO.setDataValidation(datasetVersionInfoVO.getDataValidation());
        datasetVersionInfoDO.setExperimentValidation(datasetVersionInfoVO.getExperimentValidation());
        datasetVersionInfoDO.setUpdateBy(loginUser);
        datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
        datasetVersionInfoMapper.updateById(datasetVersionInfoDO);

        datasetVersionInfoVO.setUpdateBy(datasetVersionInfoDO.getUpdateBy());
        datasetVersionInfoVO.setUpdateTime(datasetVersionInfoDO.getUpdateTime());
        return datasetVersionInfoVO;
    }

    @Override
    public Long updateDatasetBasicInfo(DatasetInfoVO datasetInfoVO, SimpleUser loginUser) {
        if (ObjectUtils.isEmpty(datasetInfoVO)) {
            throw new ServiceException("数据集信息为空");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetInfoVO.getId());
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        if (datasetInfoService.selectNoDraftByNameAndId(datasetInfoVO.getName(), datasetInfoDO.getId()) != null) {
            throw new ServiceException("数据集名称已存在，请修改");
        }
        datasetInfoDO.setName(datasetInfoVO.getName());
        datasetInfoDO.setDescription(datasetInfoVO.getDescription());
        datasetInfoDO.setMediaType(datasetInfoVO.getMediaType());
        datasetInfoDO.setContentType(datasetInfoVO.getContentType());
        datasetInfoDO.setIsRaw(datasetInfoVO.getIsRaw());
        datasetInfoDO.setUpdateBy(loginUser);

        datasetInfoMapper.updateBasicInfoById(datasetInfoDO);

        return datasetInfoDO.getId();
    }

    @Override
    public DatasetInfoVO getDatasetBasicInfo(Long id, SimpleUser loginUser) {
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(id);
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            log.warn("数据集不存在, id:{}", id);
            return null;
        }
        return getDatasetInfoVO(datasetInfoDO);
    }

    @Override
    public Long updateDatasetOwner(DatasetChangeOwnerRequest request, SimpleUser loginUser) {
        if (ObjectUtils.isEmpty(request) || request.getDatasetId() == null || request.getUserId() == null) {
            throw new ServiceException("数据集id或用户id为空");
        }

        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(request.getDatasetId());
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }

        SimpleUser owner = oaEmployeeManager.queryRedUsersById(request.getUserId());

        if (ObjectUtils.isEmpty(owner)) {
            throw new ServiceException("查询用户信息为空");
        }

        datasetInfoDO.setOwner(owner);
        datasetInfoDO.setUpdateBy(loginUser);

        datasetInfoMapper.updateOwnerById(datasetInfoDO);
        return datasetInfoDO.getId();
    }

    @Override
    @Transactional
    public void deleteDataset(DatasetDeleteRequest request, SimpleUser loginUser) {
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(request.getId());
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        String region;
        List<DatasetVersionInfoDO> datasetVersionInfoDOS = datasetVersionInfoMapper.selectByDatasetId(request.getId());
        if (ObjectUtils.isNotEmpty(datasetVersionInfoDOS)) {

            boolean hasDeployedVersion = datasetVersionInfoDOS.stream()
                    .anyMatch(datasetVersionInfoDO ->
                            DatasetVersionStatus.DEPLOYED.getStatus().equalsIgnoreCase(
                                    datasetVersionInfoDO.getStatus()
                            )
                    );

            if (hasDeployedVersion) {
                throw new ServiceException("此数据集已发布完成，无法删除");
            }
            datasetVersionInfoMapper.deleteBatchIds(
                    datasetVersionInfoDOS.stream().map(DatasetVersionInfoDO::getId)
                            .collect(Collectors.toList())
            );
            // 删除gravitino中注册的信息
            gravitinoFilesetService.dropFileset(datasetVersionInfoDOS.get(0).getRegion(), datasetInfoDO.getCode());
        }

        datasetInfoMapper.deleteById(request.getId());
    }

    @Override
    public Boolean datasetDeletable(Long id, SimpleUser loginUser) {
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(id);
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }

        List<DatasetVersionInfoDO> datasetVersionInfoDOS = datasetVersionInfoMapper.selectByDatasetId(id);
        if (ObjectUtils.isNotEmpty(datasetVersionInfoDOS)) {

            boolean hasDeployedVersion = datasetVersionInfoDOS.stream()
                    .anyMatch(datasetVersionInfoDO ->
                            DatasetVersionStatus.DEPLOYED.getStatus().equalsIgnoreCase(
                                    datasetVersionInfoDO.getStatus()
                            )
                    );

            return !hasDeployedVersion;
        } else {
            return true;
        }
    }

    @Override
    public Boolean cancelRegister(Long datasetVersionId) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(datasetVersionId);
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        if (!DatasetVersionStatus.DRAFT.getStatus().equals(datasetVersionInfoDO.getStatus())) {
            throw new ServiceException("非草稿态数据集版本不允许取消注册");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        if (Objects.isNull(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        if (Objects.isNull(datasetInfoDO.getMaxVersionId())) {
            datasetInfoMapper.deleteById(datasetInfoDO.getId());
        }
        datasetVersionInfoMapper.deleteById(datasetVersionId);
        return true;
    }

    @Override
    public void datasetVersionDeprecate(Long datasetVersionId, SimpleUser simpleUser) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(datasetVersionId);
        if (ObjectUtils.isEmpty(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        if (!DatasetVersionStatus.UNVERIFIED.getStatus().equalsIgnoreCase(datasetVersionInfoDO.getStatus())) {
            throw new ServiceException("待验证的数据集版本才能废弃");
        }
        datasetVersionInfoDO.setStatus(DatasetVersionStatus.DEPRECATED.getStatus());
        datasetVersionInfoDO.setUpdateBy(simpleUser);
        datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
        datasetVersionInfoMapper.updateById(datasetVersionInfoDO);
    }

    @Override
    public PageResult<DatasetWrapFlattenVO> flattenDatasetList(String name, Long id, String userId,
                                                               String mediaType, String contentType,
                                                               String language, String status, Boolean isMine,
                                                               Integer pageIndex, Integer pageSize,
                                                               SimpleUser loginUser) {

        String userId1 = null;
        if (Objects.nonNull(isMine) && isMine) {
            userId1 = loginUser.getUserId();
        }
        Page<DatasetWrapFlattenDO> page = new Page<>(pageIndex, pageSize);
        IPage<DatasetWrapFlattenDO> datasetWrapFlattenDOSIpage = datasetInfoMapper.selectFlattenList(
                page, name, id, userId, mediaType, contentType, language, status, userId1
        );
        List<DatasetWrapFlattenDO> datasetWrapFlattenDOS = datasetWrapFlattenDOSIpage.getRecords();
        log.info("数据集列表查询成功, 数据集数量: {}", datasetWrapFlattenDOS.size());

        List<DatasetWrapFlattenVO> datasetWrapFlattenVOS = datasetWrapFlattenDOS.stream().map(d -> {
            DatasetWrapFlattenVO vo = new DatasetWrapFlattenVO();
            vo.setId(d.getId());
            vo.setName(d.getName());
            vo.setMediaType(d.getMediaType());
            vo.setContentType(d.getContentType());
            vo.setIsRaw(d.getIsRaw());
            vo.setMaxVersionId(d.getMaxVersionId());
            vo.setStatus(d.getStatus());
            vo.setLanguage(d.getLanguage());
            vo.setOwner(d.getOwner());
            vo.setCreateTime(DateUtils.format(d.getCreateTime(), DateUtils.YYYY_MM_DD_HHMMSS));
            if (TokenTaskStatus.SUCCESS.getCode().equals(d.getTokenTaskStatus())) {
                // token除以10的9次方保留两位小数
                String token = String.format("%.2f", d.getTokens() / Math.pow(10, 9));
                // size除以1024的3次方保留两位小数
                String size = String.format("%.2f", d.getSize() / Math.pow(1024, 3));
                vo.setStatistics(token + "B/" + size + "GB/" + d.getRecords());
            } else {
                vo.setStatistics(TokenTaskStatus.fromCode(d.getTokenTaskStatus()).getDesc());
            }
            return vo;
        }).collect(Collectors.toList());
        return PageResult.build(pageIndex, pageSize, datasetWrapFlattenDOSIpage.getTotal(), datasetWrapFlattenVOS);
    }

    /**
     * 补充平台数据源信息
     *
     * @param datasetVersionInfoDOS
     */
    private void joinPlatformDatasourceInfo(List<DatasetVersionInfoDO> datasetVersionInfoDOS) {
        // 获取datasetVersionInfoDOS 中dataSources 的id 集合
        Set<Long> datasetVersionIds = datasetVersionInfoDOS.stream()
                .filter(it -> !StringUtils.isEmpty(it.getDataSources()))
                .map(it -> {
                    List<DataSource> dataSources = JSON.parseObject(it.getDataSources(), new TypeReference<List<DataSource>>() {
                    });
                    return dataSources.stream()
                            .filter(it1 -> DataSourceType.platform.equals(it1.getType()) && Objects.nonNull(it1.getDatasetVersionId()))
                            .map(DataSource::getDatasetVersionId)
                            .collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(datasetVersionIds)) {
            return;
        }

        List<DatasetVersionSimpleDO> datasetVersionSimpleDOS = datasetVersionInfoMapper.selectDatasetVersionSimpleBatchIds(new ArrayList<>(datasetVersionIds));
        Map<Long, DatasetVersionSimpleDO> datasetVersionSimpleDOMap = datasetVersionSimpleDOS.stream()
                .collect(Collectors.toMap(DatasetVersionSimpleDO::getId, Function.identity()));

        // 补充datasource 中的datasetName和datasetCode
        for (DatasetVersionInfoDO datasetVersionInfoDO : datasetVersionInfoDOS) {
            List<DataSource> dataSources = JSON.parseObject(datasetVersionInfoDO.getDataSources(), new TypeReference<List<DataSource>>() {
            });
            for (DataSource dataSource : dataSources) {
                if (!DataSourceType.platform.equals(dataSource.getType())) {
                    continue;
                }
                DatasetVersionSimpleDO versionSimple = datasetVersionSimpleDOMap.get(dataSource.getDatasetVersionId());
                if (versionSimple != null) {
                    dataSource.setDatasetName(versionSimple.getName());
                    dataSource.setDatasetCode(versionSimple.getCode());
                    dataSource.setVersion(String.valueOf(versionSimple.getVersion()));
                }
            }
            datasetVersionInfoDO.setDataSources(JSON.toJSONString(dataSources));
        }
    }

    @Override
    public DatasetVersionsWrapVO getDatasetVersions(Long datasetId, SimpleUser loginUser) {
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetId);
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }

        DatasetInfoVO datasetInfoVO = getDatasetInfoVO(datasetInfoDO);

        List<DatasetVersionInfoDO> datasetVersionInfoDOS = datasetVersionInfoMapper.selectNonDraftByDatasetId(datasetId);
        joinPlatformDatasourceInfo(datasetVersionInfoDOS);

        List<DatasetVersionInfoVO> versionsVO = datasetVersionInfoDOS.stream()
                .map(this::getDatasetVersionInfoVO)
                .collect(Collectors.toList());


        List<DecontaminationTaskDO> decontaminationTaskDOS = decontaminationTaskService.getDecontaminationTasksByDatasetId(datasetId);
        Map<Long, DecontaminationTaskDO> decontaminationTaskMap = decontaminationTaskDOS.stream()
                .collect(Collectors.toMap(DecontaminationTaskDO::getDatasetVersionId, Function.identity()));

        // 补充去污任务信息
        for (DatasetVersionInfoVO versionVO : versionsVO) {
            DecontaminationTaskDO decontaminationTaskDO = decontaminationTaskMap.get(versionVO.getId());
            if (decontaminationTaskDO != null) {
                versionVO.setDecontaminationStatus(decontaminationTaskDO.getStatus());
                versionVO.setDecontaminationErrorMsg(decontaminationTaskDO.getErrorMsg());
            }
        }


        return new DatasetVersionsWrapVO(datasetInfoVO, versionsVO);
    }

    @Override
    public DatasetVersionsWrapVO getDatasetVersionDetail(Long datasetVersionId) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoService.selectById(datasetVersionId);
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoMapper.selectById(datasetVersionInfoDO.getDatasetId());
        if (ObjectUtils.isEmpty(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        DatasetInfoVO datasetInfoVO = getDatasetInfoVO(datasetInfoDO);

        List<DatasetVersionInfoDO> datasetVersionInfoDOS = Collections.singletonList(datasetVersionInfoDO);

        joinPlatformDatasourceInfo(datasetVersionInfoDOS);

        List<DatasetVersionInfoVO> versionsVO = datasetVersionInfoDOS.stream()
                .map(this::getDatasetVersionInfoVO)
                .collect(Collectors.toList());

        return new DatasetVersionsWrapVO(datasetInfoVO, versionsVO);
    }

    @Override
    public Map<String, Object> getPathRegion(String path) {
        // 获取region
        String bucket = path.split("//")[1].split("/")[0];
        Region region = Region.fromBucket(bucket);
        return region.toMap();
    }

    @Override
    public List<String> getDatasetVersionLabels() {
        return datasetVersionInfoMapper.selectAllLabels();
    }

    private void saveJuiceSyncIndexTask(DatasetVersionInfoDO datasetVersionInfoDO, String paiJobId) {
        IndexTaskDO indexTaskDO = new IndexTaskDO();
        indexTaskDO.setDatasetVersionId(datasetVersionInfoDO.getId());
        indexTaskDO.setIndexType(IndexType.INVERTED.getCode());
        indexTaskDO.setPlatform(PlatformType.pai.name());
        indexTaskDO.setStartTime(LocalDateTime.now());
        indexTaskDO.setStatus(PaiTaskStatus.CREATING.getCode());
        indexTaskDO.setInputPath(datasetVersionInfoDO.getOssPath());
        indexTaskDO.setOutputPath(null);
        indexTaskDO.setTaskId(paiJobId);
        indexTaskService.saveIndexTask(indexTaskDO);
    }

    @Override
    public void createIndex(DatasetVersionIndexRequest request, SimpleUser simpleUser) {
        checkCreateIndex(request);
        List<String> indexTypes = request.getIndexTypes();
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(request.getDatasetVersionId());
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        DatasetInfoDO datasetInfoDO = datasetInfoService.selectById(datasetVersionInfoDO.getDatasetId());
        if (Objects.isNull(datasetInfoDO)) {
            throw new ServiceException("数据集不存在");
        }
        for (String indexType : indexTypes) {
            if (IndexType.INVERTED.getCode().equals(indexType)) {
                // 创建倒排索引

                if (Region.AP_SOUTHEAST_1.getRegion().equalsIgnoreCase(datasetVersionInfoDO.getRegion())) {
                    String paiJobId = paiService.createJuiceSyncTask(datasetVersionInfoDO);
                    saveJuiceSyncIndexTask(datasetVersionInfoDO, paiJobId);
                    datasetVersionInfoDO.setInvertedIndexState(InvertedIndexState.WAIT_FOR_SYNC.getCode());
                } else {
                    datasetVersionInfoDO.setInvertedIndexState(InvertedIndexState.PENDING.getCode());
                }
                datasetVersionInfoDO.setInvertedErrorMsg("");
            } else if (IndexType.VECTOR.getCode().equals(indexType)) {
                // 创建向量索引
                datasetVersionInfoDO.setVectorIndexState(VectorIndexState.EMBEDDING.getCode());
                IndexTaskDO embeddingTask = paiService.createEmbeddingTask(
                        datasetInfoDO,
                        datasetVersionInfoDO,
                        simpleUser
                );
                datasetVersionInfoDO.setVectorDatasetVersionName(embeddingTask.getDatasetVersionName());
                datasetVersionInfoDO.setVectorCluster(milvusUri);
                indexTaskService.saveIndexTask(embeddingTask);
                datasetVersionInfoDO.setVectorErrorMsg("");
            } else {
                throw new ServiceException("不支持的索引类型: " + indexType);
            }
        }
        datasetVersionInfoDO.setUpdateBy(simpleUser);
        datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
        datasetVersionInfoService.updateById(datasetVersionInfoDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIndex(DatasetVersionIndexRequest request, SimpleUser simpleUser) {
        checkDeleteIndex(request);
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(request.getDatasetVersionId());
        String indexType = request.getIndexType();
        if (IndexType.INVERTED.getCode().equals(indexType)) {
            // 删除倒排索引
            esService.deleteIndex(datasetVersionInfoDO.getInvertedCluster(), datasetVersionInfoDO.getId());
            datasetVersionInfoDO.setInvertedIndexState(InvertedIndexState.NONE.getCode());
        } else if (IndexType.VECTOR.getCode().equals(indexType)) {
            // 删除向量索引
            datasetVersionInfoDO.setVectorIndexState(VectorIndexState.NONE.getCode());
            vectorService.dropCollection(datasetVersionInfoDO.getVectorDatasetVersionName());
            indexTaskService.deleteIndexTaskByDatasetVersionIdAndIndexType(datasetVersionInfoDO.getId(), IndexType.VECTOR.getCode());
        } else {
            throw new ServiceException("不支持的索引类型: " + indexType);
        }
        datasetVersionInfoDO.setUpdateBy(simpleUser);
        datasetVersionInfoDO.setUpdateTime(LocalDateTime.now());
        datasetVersionInfoService.updateById(datasetVersionInfoDO);
    }

    @Override
    public List<DatasetVersionDetailVO> datasetVersionList(List<Long> datasetVersionIds) {
        if (CollectionUtils.isEmpty(datasetVersionIds)) {
            return Collections.emptyList();
        }
        List<DatasetVersionInfoDO> datasetVersionInfoDOS = datasetVersionInfoMapper.selectBatchIds(datasetVersionIds);
        if (CollectionUtils.isEmpty(datasetVersionInfoDOS)) {
            return Collections.emptyList();
        }
        List<DatasetVersionInfoDetailDO> datasetVersionInfoDetailDOS = datasetVersionInfoService.selectDetailByIds(datasetVersionIds);
        return datasetVersionInfoDetailDOS.stream().map(d -> {
            DatasetVersionDetailVO datasetVersionDetailVO = new DatasetVersionDetailVO();
            datasetVersionDetailVO.setId(d.getId());
            datasetVersionDetailVO.setDatasetId(d.getDatasetId());
            datasetVersionDetailVO.setName(d.getName());
            datasetVersionDetailVO.setCode(d.getCode());
            datasetVersionDetailVO.setVersion(d.getVersion());
            datasetVersionDetailVO.setMediaType(d.getMediaType());
            datasetVersionDetailVO.setContentType(d.getContentType());
            datasetVersionDetailVO.setIsRaw(d.getIsRaw());
            datasetVersionDetailVO.setLanguage(d.getLanguage());
            datasetVersionDetailVO.setStatus(d.getStatus());
            datasetVersionDetailVO.setCreateTime(DateUtils.format(d.getCreateTime(), DateUtils.YYYY_MM_DD_HHMMSS));
            if (TokenTaskStatus.SUCCESS.getCode().equals(d.getTokenTaskStatus())) {
                // token除以10的9次方保留两位小数
                String token = String.format("%.2f", d.getTokens() / Math.pow(10, 9));
                // size除以1024的3次方保留两位小数
                String size = String.format("%.2f", d.getSize() / Math.pow(1024, 3));
                datasetVersionDetailVO.setStatistics(token + "B/" + size + "GB/" + d.getRecords());
            } else {
                datasetVersionDetailVO.setStatistics(TokenTaskStatus.fromCode(d.getTokenTaskStatus()).getDesc());
            }
            datasetVersionDetailVO.setOwner(d.getOwner());
            return datasetVersionDetailVO;
        }).collect(Collectors.toList());
    }

    private void checkDeleteIndex(DatasetVersionIndexRequest request) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(request.getDatasetVersionId());
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        String indexType = request.getIndexType();
        if (StringUtils.isEmpty(indexType)) {
            throw new ServiceException("索引类型不能为空");
        }
        if (IndexType.INVERTED.getCode().equals(indexType) && !InvertedIndexState.SUCCESS.getCode().equals(datasetVersionInfoDO.getInvertedIndexState())) {
            throw new ServiceException("倒排索引不存在或未创建成功，请勿重复删除");
        }
        if (IndexType.VECTOR.getCode().equals(indexType) && !VectorIndexState.SUCCESS.getCode().equals(datasetVersionInfoDO.getVectorIndexState())) {
            throw new ServiceException("向量索引不存在或未创建成功，请勿重复删除");
        }
    }

    @NotNull
    public DatasetVersionInfoVO getDatasetVersionInfoVO(DatasetVersionInfoDO datasetVersionInfoDO) {
        DatasetVersionInfoVO versionVO = new DatasetVersionInfoVO();
        versionVO.setId(datasetVersionInfoDO.getId());
        versionVO.setDatasetId(datasetVersionInfoDO.getDatasetId());
        versionVO.setVersion(String.valueOf(datasetVersionInfoDO.getVersion()));
        versionVO.setLabel(datasetVersionInfoDO.getLabel());
        versionVO.setDataType(datasetVersionInfoDO.getDataType());

        DatasetMetadata datasetMetadata = new DatasetMetadata();
        datasetMetadata.setLocation(datasetVersionInfoDO.getOssPath());
        datasetMetadata.setFileFormat(datasetVersionInfoDO.getFileType());
        datasetMetadata.setCatalog(datasetVersionInfoDO.getCatalog());
        datasetMetadata.setDbName(datasetVersionInfoDO.getDbName());
        datasetMetadata.setTableName(datasetVersionInfoDO.getTableName());
        versionVO.setMetadata(datasetMetadata);

        versionVO.setLanguage(datasetVersionInfoDO.getLanguage());
        versionVO.setDescription(datasetVersionInfoDO.getDescription());
        versionVO.setIsCustom(datasetVersionInfoDO.getIsCustom());
        versionVO.setRedocLink(datasetVersionInfoDO.getRedocLink());
        versionVO.setDataSources(JSON.parseObject(datasetVersionInfoDO.getDataSources(), new TypeReference<List<DataSource>>() {
        }));
        versionVO.setProcessingTasks(datasetVersionInfoDO.getProcessingTasks());
        versionVO.setDataFunnel(datasetVersionInfoDO.getDataFunnel());
        versionVO.setDataValidation(datasetVersionInfoDO.getDataValidation());
        versionVO.setExperimentValidation(datasetVersionInfoDO.getExperimentValidation());
        versionVO.setVerificationReport(datasetVersionInfoDO.getVerificationReport());
        versionVO.setRegion(datasetVersionInfoDO.getRegion());
        versionVO.setTokens(datasetVersionInfoDO.getTokens());
        versionVO.setSize(datasetVersionInfoDO.getSize());
        versionVO.setRecords(datasetVersionInfoDO.getRecords());
        if (DataType.iceberg.name().equals(datasetVersionInfoDO.getDataType())) {
            versionVO.setDbTableName(datasetVersionInfoDO.getDbName() + "." + datasetVersionInfoDO.getTableName());
        }
        versionVO.setStatus(datasetVersionInfoDO.getStatus());
        if (DatasetVersionStatus.DEPLOYING.getStatus().equals(datasetVersionInfoDO.getStatus())) {
            // 获取OA链接
            ApproveRecordDO latestApproveRecord = approveRecordService.getLatestApproveRecord(datasetVersionInfoDO.getId());
            if (latestApproveRecord != null) {
                versionVO.setOaUrl(generateOaUrl(latestApproveRecord.getApproveFormId()));
            }
        }
        versionVO.setNeedDecontamination(datasetVersionInfoDO.getNeedDecontamination());
        versionVO.setInvertedIndexState(datasetVersionInfoDO.getInvertedIndexState());
        versionVO.setVectorIndexState(datasetVersionInfoDO.getVectorIndexState());
        versionVO.setCreateBy(datasetVersionInfoDO.getCreateBy());
        versionVO.setUpdateBy(datasetVersionInfoDO.getUpdateBy());
        versionVO.setCreateTime(datasetVersionInfoDO.getCreateTime());
        versionVO.setUpdateTime(datasetVersionInfoDO.getUpdateTime());
        versionVO.setSnapshotStatus(datasetVersionInfoDO.getSnapshotStatus());
        versionVO.setSnapshotPath(datasetVersionInfoDO.getSnapshotPath());
        return versionVO;
    }

    @NotNull
    private static DatasetInfoVO getDatasetInfoVO(DatasetInfoDO datasetInfoDO) {
        DatasetInfoVO datasetInfoVO = new DatasetInfoVO();
        datasetInfoVO.setId(datasetInfoDO.getId());
        datasetInfoVO.setName(datasetInfoDO.getName());
        datasetInfoVO.setCode(datasetInfoDO.getCode());
        datasetInfoVO.setDescription(datasetInfoDO.getDescription());
        datasetInfoVO.setMediaType(datasetInfoDO.getMediaType());
        datasetInfoVO.setContentType(datasetInfoDO.getContentType());
        datasetInfoVO.setIsRaw(datasetInfoDO.getIsRaw());
        datasetInfoVO.setMaxVersionId(datasetInfoDO.getMaxVersionId());
        datasetInfoVO.setOwner(datasetInfoDO.getOwner());
        datasetInfoVO.setCreateBy(datasetInfoDO.getCreateBy());
        datasetInfoVO.setUpdateBy(datasetInfoDO.getUpdateBy());
        datasetInfoVO.setCreateTime(datasetInfoDO.getCreateTime());
        return datasetInfoVO;
    }

    private String generateOaUrl(String formId) {
        return apolloCommonConfig.getOaUrl() + formId;
    }

    private void checkCreateIndex(DatasetVersionIndexRequest request) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(request.getDatasetVersionId());
        List<String> indexTypes = request.getIndexTypes();
        if (Objects.isNull(datasetVersionInfoDO)) {
            throw new ServiceException("数据集版本不存在");
        }
        if (DatasetVersionStatus.DRAFT.getStatus().equals(datasetVersionInfoDO.getStatus())) {
            throw new ServiceException("数据集版本处于草稿态，无法创建索引");
        }
        if (CollectionUtils.isEmpty(indexTypes)) {
            throw new ServiceException("索引类型不能为空");
        }
        if (indexTypes.contains(IndexType.INVERTED.getCode()) && !InvertedIndexState.ALLOW_CREATE_INDEX_STATUS.contains(datasetVersionInfoDO.getInvertedIndexState())) {
            throw new ServiceException("倒排索引已存在或正在创建中，请勿重复创建");
        }
        if (indexTypes.contains(IndexType.VECTOR.getCode()) && !VectorIndexState.ALLOW_CREATE_INDEX_STATUS.contains(datasetVersionInfoDO.getVectorIndexState())) {
            throw new ServiceException("向量索引已存在或正在创建中，请勿重复创建");
        }
    }
}
