package com.xiaohongshu.data.dataark.core.pai.config;

import com.xiaohongshu.data.dataark.core.pai.pojo.RegionConfig;
import com.xiaohongshu.data.dataark.core.pai.pojo.WorkSpaceConfig;
import org.springframework.context.annotation.Configuration;
import red.midware.shaded.com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/17
 */
@Configuration
public class PaiApolloConfig {

    @ApolloJsonValue("${pai.region.config:{}}")
    private Map<String, RegionConfig> paiRegionConfig;

    public List<String> paiRegions() {
        return new ArrayList<>(paiRegionConfig.keySet());
    }

    public Map<String, RegionConfig> getPaiRegionConfig() {
        return paiRegionConfig;
    }

    public String getImageUrl(String region) {
        RegionConfig regionConfig = paiRegionConfig.get(region);
        if (regionConfig == null) {
            throw new IllegalArgumentException("Region not found: " + region);
        }
        return regionConfig.getImageUrl();
    }

    public WorkSpaceConfig getWorkSpaceConfig(String region, String workSpaceId) {
        RegionConfig regionConfig = paiRegionConfig.get(region);
        if (regionConfig == null) {
            throw new IllegalArgumentException("Region not found: " + region);
        }
        List<WorkSpaceConfig> workSpaces = regionConfig.getWorkSpaces();
        for (WorkSpaceConfig workSpace : workSpaces) {
            if (workSpace.getWorkSpaceId().equals(workSpaceId)) {
                return workSpace;
            }
        }
        throw new IllegalArgumentException("WorkSpace not found: " + workSpaceId);
    }

}
