package com.xiaohongshu.data.dataark.core.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/4/24
 */
@Getter
public enum TokenTaskStatus {

    PENDING("pending", "等待计算"),
    RUNNING("running", "计算中"),
    SUCCESS("success", "计算成功"),
    FAILED("failed", "计算失败"),
    FINISHED("finished", "计算完成");

    private final String code;
    private final String desc;

    TokenTaskStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TokenTaskStatus fromCode(String code) {
        for (TokenTaskStatus status : values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("unknown TokenTaskStatus value of " + code);
    }

}
