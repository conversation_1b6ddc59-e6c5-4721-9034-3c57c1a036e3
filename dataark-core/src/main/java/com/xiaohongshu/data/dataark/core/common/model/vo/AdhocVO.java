package com.xiaohongshu.data.dataark.core.common.model.vo;

import com.xiaohongshu.data.dataark.dao.entity.dataset.DataSource;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email ying<PERSON><PERSON>@xiaohongshu.com
 * @date 2025/6/9
 */
@Data
public class AdhocVO {

    private Long adhocId;

    private String name;

    private String queryType;

    private List<Long> datasetVersionIds;

    private String condition;

    private Long rows;

    private Long folderId;

    private SimpleUser owner;

    private String createTime;

    private String updateTime;

}
