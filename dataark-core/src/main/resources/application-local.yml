server:
  port: 8082
  servlet:
    session:
      timeout: 86400
    context-path: /api/dataark

spring:
  datasource:
    dataark:
      type: com.alibaba.druid.pool.DruidDataSource
      name: dataark
      driver-class-name: com.mysql.jdbc.Driver
      url: ********************************************************************************************************************************************************************************************
      username: dataark_rw
      password: aIdEPsmboZuwcX*q
      druid:
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false

dataark:
  domain: https://dataark.devops.sit.xiaohongshu.com
  publicKey: MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZpPg7DtWkY+oeIX/Utzw4nsAemN6+gtut00l/sN8YZfa/lRuByNIVlJ3LHKbwpkIVeZjEJLHzWivJea5Pa9xEg==

rocketmq:
  oa:
    topic: oasis_form-notice
    consumer:
      group-name: oasis_form-notice-dataark-consumer

redschedule:
  appid:  dataark
  domain:  data

third_party:
  capella:
    domain: http://capella.int.beta.xiaohongshu.com/capella
  gravitino:
    url: https://unifiedcatalog.devops.beta.xiaohongshu.com/
    metalake: metalake_dev
    catalog: agi_oss
    catalog_sgp: agi_oss_sgp
    username: dataark
    password: sAp2V1RPE4rqO7ioGoknTw
  oa:
    url: https://oa.sit.xiaohongshu.com/billbuildermobile/detail/
  milvus:
    uri: http://c-2463a1c40b80e1e5-internal.milvus.aliyuncs.com:19530
    token: root:Milvus@666
    database: data_agi_poc
  embedding:
    domain: http://dataark.devops.xiaohongshu.com
    get_embedding: ${third_party.embedding.domain}/get_embedings

api:
  crossroad:
    domain: https://crossroad.xiaohongshu.com
    token: 893ae1c4d36b432d927b5eb98e14bd25

redCity:
  domain: https://redcity.devops.sit.xiaohongshu.com
  dataark:
    appId: appeeaafed170003341dd2e81616e058737
    appSecret: sk8cda2eb8512e84f712753f3631f8505829fb75efcbfd7698d49c6b795dd433d0
    asnId: asn7537971585249247232
    accountId: <EMAIL>
    accessToken: sk87d5acbcbd482df9ddaa7c6566ee5a8bcb8f600fd31c784a62b22f20f8d7f6f9