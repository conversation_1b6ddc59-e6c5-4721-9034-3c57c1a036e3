<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <property name="LOG_LEVEL" value="info"/>
    <springProperty scope="context" name="logPath" source="logging.path" defaultValue="/tmp/logs"/>

    <conversionRule conversionWord="trace" converterClass="com.xiaohongshu.data.dataark.core.config.log.TraceConvertor" />
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern> [%-5level][%d{yyyy-MM-dd'T'HH:mm:ss.SSS+0800}][%F:%line]%trace%n</pattern>
        </encoder>
    </appender>

    <appender name="fileRollingLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logPath}/dataark.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                [%-5level][%d{yyyy-MM-dd'T'HH:mm:ss.SSS+0800}][%F:%line]%trace%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/dataark.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>

    <root level="${LOG_LEVEL}">
        <appender-ref ref="fileRollingLog"/>
        <appender-ref ref="STDOUT"/>
    </root>

    <include resource="stalker-defaults.xml"/>

    <property name="APPID" value="dataark"/>
    <property name="PRD_LINE" value="dataark"/>
</configuration>
