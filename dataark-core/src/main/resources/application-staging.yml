server:
  port: 8080
  servlet:
    session:
      timeout: 86400
    context-path: /api/dataark

spring:
  datasource:
    dataark:
      type: com.alibaba.druid.pool.DruidDataSource
      name: dataark
      driver-class-name: com.mysql.jdbc.Driver
      url: **************************************************************************************************************************************************************************************************
      username: dataark_staging_rw
      password: k5Pd5aS9jUySxj*a
      druid:
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false

dataark:
  domain: https://dataark.devops.beta.xiaohongshu.com
  publicKey: MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEgsWd7HMklQdtlxtoHoAUKTL9+84iPZCfXEgxTQOjLhA2dCpalgr2OcdR1TyIkoiL5wHC5WLHBYP6reL9mQiKRQ==

rocketmq:
  oa:
    topic: oasis_form-notice
    consumer:
      group-name: oasis_form-notice-dataark-consumer

redschedule:
  appid:  dataark
  domain:  data

third_party:
  capella:
    domain: http://capella.int.beta.xiaohongshu.com/capella
  gravitino:
    url: https://unifiedcatalog.devops.beta.xiaohongshu.com/
    metalake: metalake_dev
    catalog: agi_oss_beta
    catalog_sgp: agi_oss_sgp
    username: dataark
    password: sAp2V1RPE4rqO7ioGoknTw
  oa:
    url: https://oa.beta.xiaohongshu.com/billbuildermobile/detail/
  milvus:
    uri: http://c-2463a1c40b80e1e5-internal.milvus.aliyuncs.com:19530
    token: root:Milvus@666
    database: data_agi_poc
  embedding:
    domain: http://dataark.devops.xiaohongshu.com
    get_embedding: ${third_party.embedding.domain}/get_embedings

api:
  crossroad:
    domain: https://crossroad.xiaohongshu.com
    token: 893ae1c4d36b432d927b5eb98e14bd25

redCity:
  domain: https://redcity-open.xiaohongshu.com
  dataark:
    appId: app371a2541eed8f06d4b35a6c067c41fd4
    appSecret: sk651739f5e0163233ccc0e374d3e3f2516d26fdd7a3f88b602d1a2302385aba6e
    asnId: asn7538045664845135873
    accountId: <EMAIL>
    accessToken: skf0925bea329854f488cfd252b4e416cb589f3c19b62af79d7799b8f6d66d464e