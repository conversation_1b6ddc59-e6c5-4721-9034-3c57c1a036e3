image: docker-reg.devops.xiaohongshu.com/fulishe/ci:$CI_IMAGE_VERSION
variables:
  DOCKER_IMAGE_NAME: 'dataark-web'

stages:
  - build
  - deploy

build:
  stage: build
  script:
    - mvn clean package -Dmaven.test.skip=true -Dsource.skip
    - mkdir -p target
    - mv dataark-web/target/*.jar dataark-web/target/${DOCKER_IMAGE_NAME}.jar
  only:
    - branches
    - /^\d+(\.\d+)+$/
  except:
    - master
  artifacts:
    paths:
      - "dataark-web/target/*.jar"
    expire_in: 30 mins

deploy:feature:
  stage: deploy
  image: docker-reg.devops.xiaohongshu.com/library/docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY_NAME
  script:

    - export BRANCH_PREFIX=${CI_COMMIT_REF_NAME//[\/|_]/-}
    - docker build -t docker-reg.devops.xiaohongshu.com/data/$DOCKER_IMAGE_NAME:${BRANCH_PREFIX}-${CI_COMMIT_SHA:0:8} .
    - docker push docker-reg.devops.xiaohongshu.com/data/$DOCKER_IMAGE_NAME:${BRANCH_PREFIX}-${CI_COMMIT_SHA:0:8}
  only:
    - branches
  except:
    - master


deploy:release:
  stage: deploy
  image: docker-reg.devops.xiaohongshu.com/library/docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY_NAME
  script:
    - docker build -t docker-reg.devops.xiaohongshu.com/data/$DOCKER_IMAGE_NAME:${CI_COMMIT_REF_NAME} .
    - docker push docker-reg.devops.xiaohongshu.com/data/$DOCKER_IMAGE_NAME:${CI_COMMIT_REF_NAME}
  only:
    - /^\d+(\.\d+)+$/