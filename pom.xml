<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>dataark-web</module>
        <module>dataark-core</module>
        <module>dataark-dao</module>
    </modules>

    <groupId>com.xiaohongshu.data</groupId>
    <artifactId>dataark</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.0.RELEASE</version>
    </parent>

    <properties>
        <mysql.version>5.1.47</mysql.version>
        <midware.version>3.3.5-RELEASE</midware.version>
        <fastjson.version>1.2.70</fastjson.version>
        <capella.spi.version>0.0.3-SNAPSHOT</capella.spi.version>
        <soda.http.proxy.version>1.0-SNAPSHOT</soda.http.proxy.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <!-- mysql/mybatis相关 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu.data</groupId>
                <artifactId>mybatis-plus-starter</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.1.18</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.12</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>thrift-rpc</artifactId>
                <version>${midware.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>dataverse-common</artifactId>
                <version>25.3.21</version>
            </dependency>
            <dependency>
                <groupId>com.red.data.infra</groupId>
                <artifactId>stalker</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>
            <!--    OA流程相关        -->
            <dependency>
                <groupId>com.xiaohongshu.fls.thrift</groupId>
                <artifactId>lib-thrift-oa-public</artifactId>
                <version>master-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>thrift-rpc</artifactId>
                <version>3.3.5-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>thrift-springboot</artifactId>
                <version>3.3.5-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>events-client</artifactId>
                <version>1.2.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.xiaohongshu</groupId>
                        <artifactId>infra-framework-context</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>pai_dlc20201203</artifactId>
                <version>1.4.17</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu.sec</groupId>
                <artifactId>kms-sdk</artifactId>
                <version>4.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu.infra.midware</groupId>
                <artifactId>redschedule-spring-boot-starter</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.red.data.dg</groupId>
                <artifactId>dragon-gate-client-sdk</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu.data</groupId>
                <artifactId>capella-spi</artifactId>
                <version>${capella.spi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dataverse.infra</groupId>
                <artifactId>soda-http-proxy</artifactId>
                <version>${soda.http.proxy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.gravitino</groupId>
                <artifactId>gravitino-client-java-runtime</artifactId>
                <version>0.9.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.9</version>
            </dependency>


            <dependency>
                <groupId>com.xiaohongshu.infra.midware</groupId>
                <artifactId>redis-spring</artifactId>
                <version>${midware.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>nexus snapshot repository</name>
            <url>https://artifactory.devops.xiaohongshu.com/artifactory/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>nexus repository</name>
            <url>https://artifactory.devops.xiaohongshu.com/artifactory/maven-releases</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>xhs-release</id>
            <url>https://artifactory.devops.xiaohongshu.com/artifactory/maven-releases</url>
        </repository>
        <repository>
            <id>xhs-snapshot</id>
            <url>https://artifactory.devops.xiaohongshu.com/artifactory/maven-snapshots/</url>
        </repository>
    </repositories>


</project>