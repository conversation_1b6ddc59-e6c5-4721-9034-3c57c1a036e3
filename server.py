from fastapi import FastAPI, Request
from FlagEmbedding import BGEM3FlagModel

app = FastAPI()

# 加载模型（首次运行会自动从 Huggingface 下载，之后本地读取）
model = BGEM3FlagModel('./bge-m3', use_fp16=True)

@app.post("/get_embedings")
async def embed(request: Request):
    try:
        data = await request.json()
        sentences = data["sentences"]
        res = model.encode(sentences, batch_size=12, max_length=8192)['dense_vecs']
        # 转 list 便于 json 返回
        return {"success":True,"dense_vecs": res.tolist()}
    except Exception as E:
        print(E)
        return {"success":False,"err_msg":str(e)[:20000]}
@app.get("/hb")
def heartbeat():
    return {"status": "ok"}