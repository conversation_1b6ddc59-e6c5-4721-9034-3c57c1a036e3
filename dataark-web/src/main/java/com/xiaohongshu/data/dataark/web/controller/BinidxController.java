package com.xiaohongshu.data.dataark.web.controller;

import com.xiaohongshu.data.dataark.core.common.model.request.BinidxPublishTaskRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxSampleTaskRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.BinidxTaskListVO;
import com.xiaohongshu.data.dataark.core.pai.pojo.RegionConfig;
import com.xiaohongshu.data.dataark.core.common.model.request.BinidxGenerateScriptRequest;
import com.xiaohongshu.data.dataark.core.service.BinidxService;
import com.xiaohongshu.data.dataark.dao.entity.BinidxTaskDO;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.web.RestResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/17
 */
@RestController
@RequestMapping("/binidx")
public class BinidxController extends BaseController {

    @Resource
    private BinidxService binidxService;

    @GetMapping("/cpfsType")
    public RestResult<List<String>> getCpfsType() {
        return successResponse(binidxService.getCpfsType());
    }

    @GetMapping("/paiResourceConfig")
    public RestResult<Map<String, RegionConfig>> getPaiResourceConfig() {
        return successResponse(binidxService.getPaiResourceConfig());
    }

    @PostMapping("/generateScript")
    public RestResult<String> generateScript(@RequestBody BinidxGenerateScriptRequest req) {
        return successResponse(binidxService.generateScript(req));
    }

    @PostMapping("/sampleGenerateTask")
    public RestResult<String> sampleGenerateTask(@RequestBody BinidxSampleTaskRequest req) {
        SimpleUser user = loginUser();
        return successResponse(binidxService.sampleGenerateTask(req, user));
    }

    @PostMapping("/publishGenerateTask")
    public RestResult<String> publishGenerateTask(@RequestBody BinidxPublishTaskRequest req) {
        SimpleUser user = loginUser();
        return successResponse(binidxService.publishGenerateTask(req, user));
    }

    @GetMapping("/list")
    public RestResult<PageResult<BinidxTaskListVO>> list(@RequestParam(name = "datasetVersionId") Long datasetVersionId,
                                                         @RequestParam(name = "pageIndex", defaultValue = "1") Integer pageIndex,
                                                         @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return successResponse(binidxService.list(datasetVersionId, pageIndex, pageSize));
    }

    @GetMapping("/searchSampleAllRecord")
    public RestResult<List<BinidxTaskDO>> searchSampleAllRecord(@RequestParam(name = "datasetVersionId") Long datasetVersionId) {
        return successResponse(binidxService.searchSampleAllRecord(datasetVersionId));
    }

}
