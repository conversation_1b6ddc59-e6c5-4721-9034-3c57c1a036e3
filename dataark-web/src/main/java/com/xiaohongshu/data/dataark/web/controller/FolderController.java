package com.xiaohongshu.data.dataark.web.controller;

import com.xiaohongshu.data.dataark.core.common.model.request.FolderRequest;
import com.xiaohongshu.data.dataark.core.common.model.request.MoveRequest;
import com.xiaohongshu.data.dataark.core.common.model.vo.FolderChildrenVO;
import com.xiaohongshu.data.dataark.core.service.FolderService;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.web.RestResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
@RestController
@RequestMapping("/folder")
public class FolderController extends BaseController {

    @Resource
    private FolderService folderService;

    @PostMapping("/create")
    public RestResult<Long> create(@RequestBody FolderRequest request) {
        SimpleUser user = loginUser();
        return successResponse(folderService.create(request, user));
    }

    @PostMapping("/rename")
    public RestResult<String> rename(@RequestBody FolderRequest request) {
        SimpleUser user = loginUser();
        folderService.rename(request.getFolderId(), request.getName(), user);
        return successResponse("success");
    }

    @PostMapping("/delete")
    public RestResult<String> delete(@RequestBody FolderRequest request) {
        SimpleUser user = loginUser();
        folderService.delete(request.getFolderId(), user);
        return successResponse("success");
    }

    @PostMapping("/move")
    public RestResult<String> move(@RequestBody MoveRequest request) {
        SimpleUser user = loginUser();
        folderService.move(request, user);
        return successResponse("success");
    }

    @GetMapping("/show")
    public RestResult<FolderChildrenVO> show(@RequestParam String folderType,
                                             @RequestParam(defaultValue = "true") boolean showFile,
                                             @RequestParam(defaultValue = "false") boolean own,
                                             @RequestParam(required = false) String keyword) {
        SimpleUser user = loginUser();
        return successResponse(folderService.show(folderType, showFile, own, keyword, user));
    }

}
