package com.xiaohongshu.data.dataark.web.controller;

import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryResultVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.CalculateTokenListVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.InvertedIndexExportVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.VectorEmbeddingExportVO;
import com.xiaohongshu.data.dataark.core.service.OpenApiService;
import com.xiaohongshu.dataverse.common.web.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/23
 */
@RestController
@RequestMapping("/openapi")
@Slf4j
public class OpenApiController extends BaseController {

    @Resource
    private OpenApiService openApiService;

    @GetMapping("/getCalculateTokenList")
    public RestResult<List<CalculateTokenListVO>> getCalculateTokenList() {
        return successResponse(openApiService.getCnShCalculateTokenList());
    }

    @GetMapping("/getSgpCalculateTokenList")
    public RestResult<List<CalculateTokenListVO>> getSgpCalculateTokenList() {
        return successResponse(openApiService.getSgpShCalculateTokenList());
    }

    @PostMapping("/lockCalculateToken")
    public RestResult<Integer> lockCalculateToken(@RequestBody LockCalculateTokenRequest request) {
        return successResponse(openApiService.lockCalculateToken(request));
    }

    @PostMapping("/saveTokenResult")
    public RestResult<String> saveTokenResult(@RequestBody SaveTokenResultRequest request) {
        openApiService.saveTokenResult(request);
        return successResponse("success");
    }

    @GetMapping("/getInvertedIndexExportTask")
    public RestResult<InvertedIndexExportVO> getInvertedIndexExportTask() {
        return successResponse(openApiService.getInvertedIndexExportTask());
    }

    @PostMapping("/lockInvertedIndexExport")
    public RestResult<Integer> lockInvertedIndexExport(@RequestBody LockIndexExportRequest request) {
        return successResponse(openApiService.lockInvertedIndexExport(request));
    }

    @PostMapping("/saveInvertedIndexResult")
    public RestResult<String> saveInvertedIndexResult(@RequestBody SaveIndexResultRequest request) {
        openApiService.saveInvertedIndexResult(request);
        return successResponse("success");
    }

    @GetMapping("/getVectorEmbeddingExportTask")
    public RestResult<VectorEmbeddingExportVO> getVectorEmbeddingExportTask() {
        return successResponse(openApiService.getVectorEmbeddingExportTask());
    }

    @PostMapping("/lockVectorExport")
    public RestResult<Integer> lockVectorExport(@RequestBody LockIndexExportRequest request) {
        return successResponse(openApiService.lockVectorExport(request));
    }

    @PostMapping("/saveVectorResult")
    public RestResult<String> saveVectorResult(@RequestBody SaveIndexResultRequest request) {
        openApiService.saveVectorResult(request);
        return successResponse("success");
    }

    @PostMapping("/adhoc/query")
    public RestResult<AdhocQueryResultVO> adhocQuery(@RequestBody AdhocQueryRequest request) {
        return successResponse(openApiService.adhocQuery(request));
    }

    @PostMapping(
            path = "/dataset",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public String initDataset(@RequestPart("file") MultipartFile file) {
        try {
            openApiService.initDataset(file);
            return "init success";
        } catch (Exception e) {
            log.error("init dataset error", e);
            return "init fail";
        }
    }

}
