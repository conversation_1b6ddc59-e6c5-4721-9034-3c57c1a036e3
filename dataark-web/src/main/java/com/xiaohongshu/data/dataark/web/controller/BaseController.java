package com.xiaohongshu.data.dataark.web.controller;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.data.dataark.core.common.constant.Constants;
import com.xiaohongshu.data.dataark.core.utils.ECDSAUtils;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.web.RestResult;
import com.xiaohongshu.dataverse.common.web.RestResultCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletRequest;
import java.util.Base64;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/1/10
 */
public class BaseController {

    @Autowired
    protected HttpServletRequest request;

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${dataark.publicKey}")
    private String publicKey;

    public SimpleUser loginUser() {
        String debugUserInfoB64Encode = request.getHeader("debug-userinfo");
        if (!StringUtils.isEmpty(debugUserInfoB64Encode)) {
            byte[] decodedBytes = Base64.getDecoder().decode(debugUserInfoB64Encode);
            String jsonString = new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);
            try {
                return JSON.parseObject(jsonString, SimpleUser.class);
            } catch (Exception e) {
                throw new ServiceException("获取debug模式登陆人信息失败");
            }
        }
        if (Constants.LOCAL.equals(env)) {
            SimpleUser simpleUser = new SimpleUser();
            simpleUser.setUserId("yingyuhui");
            simpleUser.setDisplayName("齐衡(应宇晖)");
            simpleUser.setEmail("<EMAIL>");
            return simpleUser;
        }

        String signedUserinfo = request.getHeader("signed-userinfo");
        if (StringUtils.isNotEmpty(signedUserinfo)) {
            try {
                return ECDSAUtils.getUserInfo(signedUserinfo, publicKey);
            } catch (Exception e) {
                throw new ServiceException("获取登陆人信息失败");
            }
        }
        throw new ServiceException("signed-userinfo is empty");
    }


    /**
     * 创建成功的响应
     * @param data data实体
     * @param <T> data类型
     * @return 带data的response
     */
    public <T> RestResult<T> successResponse(T data) {
        return RestResult.success(data);
    }

    /**
     * 创建失败的响应
     * @param error_msg 错误信息
     * @return 带错误信息的response
     */
    public RestResult failedResponse(String error_msg) {
        return RestResult.fail(RestResultCodeEnum.SERVER_ERROR, error_msg);
    }
}
