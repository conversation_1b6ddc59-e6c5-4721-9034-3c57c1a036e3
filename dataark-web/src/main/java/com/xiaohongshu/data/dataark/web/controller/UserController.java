package com.xiaohongshu.data.dataark.web.controller;

import com.xiaohongshu.data.dataark.core.common.model.User;
import com.xiaohongshu.data.dataark.core.service.UserService;
import com.xiaohongshu.dataverse.common.pojo.SimpleEmployee;
import com.xiaohongshu.dataverse.common.web.RestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/28
 */
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @GetMapping("/search")
    public RestResult<List<SimpleEmployee>> search(@RequestParam String key) {
        return RestResult.success(userService.search(key));
    }

    @GetMapping("/userInfo")
    public RestResult<User> userInfo() {
        return RestResult.success(userService.userInfo(loginUser()));
    }

}
