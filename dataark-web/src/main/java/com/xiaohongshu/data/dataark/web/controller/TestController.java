package com.xiaohongshu.data.dataark.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.AdhocQueryResultVO;
import com.xiaohongshu.data.dataark.core.es.EsService;
import com.xiaohongshu.data.dataark.core.gravitino.service.GravitinoFilesetService;
import com.xiaohongshu.data.dataark.core.milvus.service.MilvusService;
import com.xiaohongshu.data.dataark.core.pai.Sample;
import com.xiaohongshu.data.dataark.core.pai.config.PaiApolloConfig;
import com.xiaohongshu.data.dataark.core.pai.pojo.RegionConfig;
import com.xiaohongshu.data.dataark.core.pai.service.PaiService;
import com.xiaohongshu.data.dataark.core.rpc.embedding.EmbeddingCaller;
import com.xiaohongshu.data.dataark.core.schedule.IndexTaskScanner;
import com.xiaohongshu.data.dataark.core.service.VectorService;
import com.xiaohongshu.data.dataark.core.service.impl.BinidxServiceImpl;
import com.xiaohongshu.data.dataark.core.service.impl.DatasetServiceImpl;
import com.xiaohongshu.data.dataark.core.service.impl.QueryServiceImpl;
import com.xiaohongshu.data.dataark.core.snapshot.OssSnapshotService;
import com.xiaohongshu.data.dataark.core.utils.OSSUtils;
import com.xiaohongshu.data.dataark.dao.entity.DatasetVersionInfoDO;
import com.xiaohongshu.data.dataark.dao.mapper.DatasetVersionInfoMapper;
import com.xiaohongshu.dataverse.common.web.RestResult;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/17
 */
@RestController
@RequestMapping("/test")
public class TestController extends BaseController {
    @Resource
    private DatasetServiceImpl datasetService;

    @Autowired
    private QueryServiceImpl queryService;

    @Autowired
    private Sample sample;

    @Autowired
    private PaiApolloConfig paiApolloConfig;

    @Autowired
    private GravitinoFilesetService gravitinoFilesetService;

    @Autowired
    private BinidxServiceImpl binidxService;

    @Autowired
    private EsService esService;

    @Autowired
    private PaiService paiService;

    @Autowired
    private DatasetVersionInfoMapper datasetVersionInfoMapper;

    @Resource
    private VectorService vectorService;

    @Resource
    private MilvusService milvusService;

    @Resource
    private EmbeddingCaller embeddingCaller;

    @Resource
    private IndexTaskScanner indexTaskScanner;

    @Autowired
    private OssSnapshotService ossSnapshotService;

    @GetMapping("/sample")
    public String testSample() {
        try {
            sample.test();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return "ok";
    }

    @GetMapping("/nextVersion")
    public RestResult<Integer> nextVersion(@RequestParam Long datasetId) {
        return successResponse(datasetService.nextVersion(datasetId));
    }

    @GetMapping("/getJobInfo")
    public String getJobInfo() {
        try {
            sample.getJobInfo("dlctkwe880jb1wtp");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return "ok";
    }

    @GetMapping("/getPaiConfig")
    public String getPaiConfig() {
        Map<String, RegionConfig> paiRegionConfig = paiApolloConfig.getPaiRegionConfig();
        for (Map.Entry<String, RegionConfig> entry : paiRegionConfig.entrySet()) {
            String region = entry.getKey();
            RegionConfig regionConfig = entry.getValue();
            System.out.println("Region: " + region);
            System.out.println("Image URL: " + regionConfig.getImageUrl());
            System.out.println("Workspaces: " + regionConfig.getWorkSpaces());
        }
        return "ok";
    }

    @GetMapping("/query/progress")
    public RestResult<Double> queryProgress(@RequestParam String queryId) {
        return successResponse(queryService.queryProgress(queryId));

    }

    @GetMapping("/query/result")
    public RestResult<Object> queryResult(@RequestParam String queryId) {
        return successResponse(queryService.getQueryResult(queryId));
    }

    @GetMapping("/query/logs")
    public RestResult<Object> queryLog(@RequestParam String queryId) {
        return successResponse(queryService.queryLog(queryId));
    }

//    @PostMapping("/query/submit")
//    public RestResult<String> submitQuery(@RequestParam Long queryRecordId) {
//        return successResponse(queryService.submitCapellaQuery(queryRecordId, loginUser()));
//    }

    @GetMapping("/filesetUpdate")
    public RestResult<Integer> filesetUpdate(@RequestParam String datasetCode, @RequestParam String location) {
        return successResponse(gravitinoFilesetService.addFilesetVersion(datasetCode, location));
    }

    @GetMapping("/filesetCreate")
    public RestResult<Integer> filesetCreate(@RequestParam String datasetCode, @RequestParam String location) {
        return successResponse(gravitinoFilesetService.createFileset(datasetCode, location));
    }

    @GetMapping("/filesetGet")
    public RestResult<String> filesetGet(@RequestParam String datasetCode, @RequestParam Integer version) {
        String location = gravitinoFilesetService.getFilesetVersionLocation("cn-shanghai", datasetCode, version);
        return successResponse(location);
    }

    @GetMapping("/ossUtil")
    public RestResult<Long> ossUtil(@RequestParam String path) {
        long size = OSSUtils.size(path);
        return successResponse(size);
    }

    @GetMapping("/cpScript")
    public RestResult<String> cpScript() {
        String s = binidxService.generateCpScript("/prodcpfs/data/dataark/local/sample/qh_test_01_2_1745321146329_qwen_with_st", "prodcpfs", "publish");
        System.out.println(s);
        return successResponse(s);
    }

    @GetMapping("/ossSnapshotTest")
    public RestResult<String> ossSnapshotTest(@RequestParam Long datasetVersionId){
        ossSnapshotService.datasetVersionSnapshot(datasetVersionId);
        return successResponse("success");
    }

    @GetMapping("/ossSnapshotTestV2")
    public RestResult<String> ossSnapshotTestV2(){
        ossSnapshotService.datasetVersionSnapshot();
        return successResponse("success");
    }

    @GetMapping("/ossutilTest")
    public RestResult<Boolean> ossutilTest() {
        return successResponse(ossSnapshotService.isOssUtilAvailable());
    }

    @GetMapping("/ossutilDownloadTest")
    public RestResult<Object> ossutilDownloadTest() {
        ossSnapshotService.downloadOssUtilBin();
        return successResponse("success");
    }

    @GetMapping("/os/arch")
    public RestResult<String> archTest() {
        return successResponse(System.getProperty("os.name") + "," + System.getProperty("os.arch"));
    }

    @GetMapping("/juiceSyncCmd")
    public RestResult<String> juiceSyncCmd(@RequestParam String ossPath,
                                           @RequestParam Long datasetVersionId) {
        String s = paiService.getJuiceSyncScript(ossPath, datasetVersionId);
        return successResponse(s);
    }

    @PostMapping("/createJuiceSyncTask")
    public RestResult<String> createJuiceSyncTask(@RequestParam Long datasetVersionId) {
        DatasetVersionInfoDO datasetVersionInfoDO = datasetVersionInfoMapper.selectById(datasetVersionId);
        paiService.createJuiceSyncTask(datasetVersionInfoDO);
        return successResponse("success");
    }

    @PostMapping("/invertedTaskHandleFinishStatus")
    public RestResult<String> invertedTaskHandleFinishStatus(@RequestParam Long datasetVersionId,
                                                             @RequestParam String paiTaskStatusCode) {
        indexTaskScanner.invertedTaskHandleFinishStatus(datasetVersionId, paiTaskStatusCode);
        return successResponse("success");
    }

    @DeleteMapping("/deleteIndex")
    public RestResult<String> deleteIndex( @RequestParam String clusterName,
                                           @RequestParam Long datasetVersionId){
        esService.deleteIndex(clusterName, datasetVersionId);
        return successResponse("success");
    }

    @GetMapping("/queryTest")
    public RestResult<Object> queryTest() {
        List<String> indexes = new ArrayList<>();
        indexes.add("dataark_staging_dataset_version_6");
        String keyword = "互联网是什么";
        return successResponse(esService.textMatch("data-agi-es",
                indexes, keyword, 10L, null));
    }

    @GetMapping("/dropCollection")
    public RestResult<String> dropCollection() {
        vectorService.dropCollection("qh_test_11");
        return successResponse("Collection dropped successfully");
    }

    @GetMapping("/searchVector")
    public RestResult<AdhocQueryResultVO> searchVector() {
        List<String> collections = Lists.newArrayList("qh_test_fb_v1");
        List<Float> queryVector = embeddingCaller.getEmbedding("你好");
        List<SearchResp.SearchResult> results = milvusService.search(collections, queryVector, 10L);
        SearchResp.SearchResult searchResult = results.get(0);
        Resp resp = new Resp();
        resp.setDocid(searchResult.getEntity().get("docid").toString());
        resp.setText(searchResult.getEntity().get("text").toString());
        JsonPrimitive meta = (JsonPrimitive) searchResult.getEntity().get("meta");
        Gson gson = new Gson();
        String json = gson.toJson(meta);
        JSONObject jsonObject = new JSONObject();
        try {
            // 先将JsonPrimitive转换为字符串
            String jsonString = meta.getAsString();

            // 使用Gson解析字符串为JsonObject
            JsonElement jsonElement = JsonParser.parseString(jsonString);
            JsonObject gsonObject = jsonElement.getAsJsonObject();

            // 将Gson JsonObject转换为org.json JSONObject
            for (String key : gsonObject.keySet()) {
                jsonObject.put(key, gsonObject.get(key).getAsString());
            }
            resp.setMetaJson(jsonObject);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert JsonPrimitive to JSONObject", e);
        }
        System.out.println(jsonObject);
        resp.setMeta(json);
        AdhocQueryResultVO adhocQueryResultVO = new AdhocQueryResultVO();
        adhocQueryResultVO.setSearchResult(resp);
        return successResponse(adhocQueryResultVO);
    }



    @Data
    static class Resp {
        private String docid;
        private String text;
        private String meta;
        private JSONObject metaJson;
    }

}
