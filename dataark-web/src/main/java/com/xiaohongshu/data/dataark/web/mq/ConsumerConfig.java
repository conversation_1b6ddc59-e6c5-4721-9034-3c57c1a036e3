package com.xiaohongshu.data.dataark.web.mq;

import com.xiaohongshu.data.dataark.web.mq.consumer.oa.OaNoticeMsgProcessor;
import com.xiaohongshu.events.client.consumer.EventsPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 外部数据总线
 * @author: longya
 * @since: 2024/12/11 17:23
 */
@Slf4j
@Configuration
public class ConsumerConfig {

    @Value("${rocketmq.oa.topic:}")
    private String topic;

    @Value("${rocketmq.oa.consumer.group-name:}")
    private String group;

    @Resource
    private OaNoticeMsgProcessor oaNoticeMsgProcessor;


    @Bean
    @Qualifier("oaNoticeMsgConsumer")
    public EventsPushConsumer oaNoticeMsgConsumer() {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(topic);
        consumer.setMessageProcessor(oaNoticeMsgProcessor);
        consumer.setGroup(group);
        return consumer;
    }
}
