package com.xiaohongshu.data.dataark.web.controller;

import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.data.dataark.core.service.AdhocService;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.web.RestResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/6
 */
@RestController
@RequestMapping("/adhoc")
public class AdhocController extends BaseController{

    @Resource
    private AdhocService adhocService;

    @GetMapping("/datasetVersionList")
    public RestResult<PageResult<DatasetVersionDetailVO>> getDatasetVersionList(@RequestParam String queryType,
                                                                                @RequestParam(required = false) String name,
                                                                                @RequestParam(required = false) Integer version,
                                                                                @RequestParam(required = false) String mediaType,
                                                                                @RequestParam(required = false) String contentType,
                                                                                @RequestParam(required = false) String label,
                                                                                @RequestParam(required = false) String userId,
                                                                                @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                                @RequestParam(defaultValue = "10") Integer pageSize) {
        return successResponse(adhocService.getDatasetVersionList(queryType, name, version, mediaType, contentType, label, userId, pageIndex, pageSize));
    }

    @PostMapping("/create")
    public RestResult<Long> createAdhoc() {
        SimpleUser user = loginUser();
        return successResponse(adhocService.createAdhoc(user));
    }

    @PostMapping("/delete")
    public RestResult<?> deleteAdhoc(@RequestBody AdhocRequest request) {
        SimpleUser user = loginUser();
        adhocService.deleteAdhoc(request.getAdhocId(), user);
        return successResponse("success");
    }

    @PostMapping("/save")
    public RestResult<?> saveAdhoc(@RequestBody AdhocRequest request) {
        SimpleUser user = loginUser();
        adhocService.saveAdhoc(request, user);
        return successResponse("success");
    }

    @GetMapping("/detail")
    public RestResult<AdhocVO> getAdhocDetail(@RequestParam Long adhocId) {
        return successResponse(adhocService.getAdhocDetail(adhocId));
    }

    @PostMapping("/query")
    public RestResult<AdhocQueryResultVO> query(@RequestBody AdhocQueryRequest request) {
        SimpleUser user = loginUser();
        return successResponse(adhocService.query(request, user));
    }

    @PostMapping("/query/progress")
    public RestResult<AdhocQueryProgressVO> queryProgress(@RequestBody AdhocQueryProgressRequest request) {
        SimpleUser user = loginUser();
        return successResponse(adhocService.queryProgress(request, user));
    }


    @PostMapping("/query/data")
    public RestResult<AdhocQueryDataVO> queryData(@RequestBody AdhocQueryDataRequest request) {
        SimpleUser user = loginUser();
        return successResponse(adhocService.queryData(request, user));
    }

    @GetMapping("/query/records")
    public RestResult<PageResult<AdhocQueryRecordVO>> getQueryRecords(@RequestParam Long adhocId,
                                                                      @RequestParam(defaultValue = "adhoc") String folderType,
                                                                      @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                      @RequestParam(defaultValue = "10") Integer pageSize) {
        return successResponse(adhocService.getQueryRecords(adhocId,folderType, pageIndex, pageSize));
    }

    @GetMapping("/query/record/detail")
    public RestResult<AdhocQueryRecordVO> getQueryRecordDetail(@RequestParam Long adhocQueryRecordId) {
        return successResponse(adhocService.getQueryRecordDetail(adhocQueryRecordId));
    }

    @PostMapping("/rename")
    public RestResult<?> renameAdhoc(@RequestBody AdhocRequest request) {
        SimpleUser user = loginUser();
        adhocService.renameAdhoc(request.getAdhocId(), request.getName(), user);
        return successResponse("success");
    }

    @GetMapping("/query/download")
    public RestResult<String> queryDownload(@RequestParam Long adhocQueryRecordId) {
        SimpleUser user = loginUser();
        adhocService.queryDownload(adhocQueryRecordId, user);
        return successResponse("success");
    }


    @PostMapping("/html/info")
    public RestResult<AdhocHtmlInfoVO> getHtmlInfoData(@RequestBody AdhocHtmlInfoRequest request) {
        SimpleUser user = loginUser();
        return successResponse(adhocService.getHtmlInfoData(request, user));
    }

    @PostMapping("/cancel/query")
    public RestResult<Boolean> cancelQuery(@RequestBody AdhocCancelQueryRequest request) {
        SimpleUser user = loginUser();
        return successResponse(adhocService.cancelQuery(request, user));
    }



}
