package com.xiaohongshu.data.dataark.web.handler;

import com.xiaohongshu.data.dataark.web.controller.BaseController;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import com.xiaohongshu.dataverse.common.web.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * controller的错误处理包装切面逻辑
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private final BaseController baseController = new BaseController();

    /**
     * 处理ServiceException异常
     * @param e
     * @return
     */
    @ExceptionHandler(ServiceException.class)
    public RestResult<?> serviceExceptionHandler(ServiceException e) {
        return baseController.failedResponse(e.getMessage());
    }

    /**
     * 处理请求校验参数异常
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public RestResult<?> resolveArgumentError(MethodArgumentNotValidException e) {
        FieldError error = e.getBindingResult().getFieldError();
        if (error == null) {
            return baseController.failedResponse(e.getMessage());
        }
        String message = error.getField() + ":" + error.getDefaultMessage();
        return baseController.failedResponse(message);
    }

    @ExceptionHandler(Exception.class)
    public RestResult<?> exceptionHandler(Exception e) {
        log.error("未知异常!原因是:", e);
        return baseController.failedResponse(e.getMessage());
    }
}
