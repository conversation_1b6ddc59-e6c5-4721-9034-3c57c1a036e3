package com.xiaohongshu.data.dataark.web.config;

import com.google.common.collect.Sets;
import com.red.data.infra.stalker.config.StalkerAutoConfig;
import com.red.data.infra.stalker.filter.HttpStalkingFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.Ordered;

import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/2/16
 */
@Configuration
@Import(StalkerAutoConfig.class)
public class DataarkAppConfig {

    @Bean
    public FilterRegistrationBean<HttpStalkingFilter> httpStalkingFilter() {
        FilterRegistrationBean<HttpStalkingFilter> bean = new FilterRegistrationBean<>();
        Set<String> ignores = Sets.newHashSet("/api/dataark/health/hb");
        bean.setFilter(new HttpStalkingFilter(ignores));
        bean.setName("HttpStalkingFilter");
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return bean;
    }


}
