package com.xiaohongshu.data.dataark.web.controller;

import com.xiaohongshu.data.dataark.core.common.model.request.*;
import com.xiaohongshu.data.dataark.core.common.model.vo.DatasetWrapFlattenVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.*;
import com.xiaohongshu.data.dataark.core.schedule.UpdateQueryRecord;
import com.xiaohongshu.data.dataark.core.service.DatasetExploreTaskService;
import com.xiaohongshu.data.dataark.core.service.DatasetService;
import com.xiaohongshu.data.dataark.core.service.QueryService;
import com.xiaohongshu.dataverse.common.pager.PageResult;
import com.xiaohongshu.dataverse.common.pojo.SimpleUser;
import com.xiaohongshu.dataverse.common.web.RestResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/12
 */
@RestController
@RequestMapping("/dataset")
public class DatasetController extends BaseController {

    @Resource
    private DatasetService datasetService;

    @Resource
    private DatasetExploreTaskService datasetExploreTaskService;

    @Resource
    private QueryService queryService;

    @Resource
    private UpdateQueryRecord updateQueryRecord;

    @GetMapping("/content/types")
    public RestResult<List<String>> getContentTypes(@RequestParam String mediaType) {
        return successResponse(datasetService.contentTypesNameCn(mediaType));
    }

    @GetMapping("/languages")
    public RestResult<List<String>> getLanguages() {
        return successResponse(datasetService.getLanguages());
    }

    @GetMapping("/status")
    public RestResult<List<Map<String, Object>>> getDatasetVersionStatus() {
        return successResponse(datasetService.getStatus());
    }

    @GetMapping("/regions")
    public RestResult<List<Map<String, Object>>> getRegions() {
        return successResponse(datasetService.getRegions());
    }

    @PostMapping("/upsert")
    public RestResult<DatasetWarpVO> createDataset(@RequestBody DatasetWarpVO datasetWarpVO) {
        return successResponse(datasetService.createDataset(datasetWarpVO, loginUser()));
    }

    @PostMapping("/commit")
    public RestResult<Long> commitDataset(@RequestBody DatasetCommitRequest commitRequest) {
        return successResponse(datasetService.commitDataset(commitRequest, loginUser()));
    }

    @PostMapping("/version/upsert")
    public RestResult<DatasetVersionInfoVO> createVersion(@RequestBody DatasetVersionInfoVO datasetVersionInfoVO) {
        return successResponse(datasetService.createVersion(datasetVersionInfoVO, loginUser()));
    }

    @PostMapping("/version/output")
    public RestResult<DatasetVersionInfoVO> updateDatasetVersionOutput(@RequestBody DatasetVersionInfoVO datasetVersionInfoVO) {
        return successResponse(datasetService.updateDatasetVersionOutput(datasetVersionInfoVO, loginUser()));
    }

    @PostMapping("/basic")
    public RestResult<Long> updateDatasetBasicInfo(@RequestBody DatasetInfoVO datasetInfoVO) {
        return successResponse(datasetService.updateDatasetBasicInfo(datasetInfoVO, loginUser()));
    }

    @GetMapping("/basic")
    public RestResult<DatasetInfoVO> getDatasetBasicInfo(@RequestParam Long id) {
        return successResponse(datasetService.getDatasetBasicInfo(id, loginUser()));
    }

    @PostMapping("/owner")
    public RestResult<Long> updateDatasetOwner(@RequestBody DatasetChangeOwnerRequest request) {
        return successResponse(datasetService.updateDatasetOwner(request, loginUser()));
    }

    @DeleteMapping("/delete")
    public RestResult<Long> deleteDataset(@RequestBody DatasetDeleteRequest request) {
        datasetService.deleteDataset(request, loginUser());
        return successResponse(1L);
    }

    @GetMapping("/deletable")
    public RestResult<Boolean> datasetDeletable(@RequestParam Long id) {
        return successResponse(datasetService.datasetDeletable(id, loginUser()));
    }

    @PostMapping("/cancelRegister")
    public RestResult<Boolean> cancelRegister(@RequestBody DatasetVersionRequest datasetVersionRequest) {
        return successResponse(datasetService.cancelRegister(datasetVersionRequest.getDatasetVersionId()));
    }

    @PostMapping("/version/deprecate")
    public RestResult<String> datasetVersionDeprecate(@RequestBody DatasetVersionRequest request) {
        datasetService.datasetVersionDeprecate(request.getDatasetVersionId(), loginUser());
        return successResponse("success");
    }

    @GetMapping("/flatten")
    public RestResult<PageResult<DatasetWrapFlattenVO>> flattenDatasetList(@RequestParam(required = false) String name,
                                                                           @RequestParam(required = false) Long id,
                                                                           @RequestParam(required = false) String userId,
                                                                           @RequestParam(required = false) String mediaType,
                                                                           @RequestParam(required = false) String contentType,
                                                                           @RequestParam(required = false) String language,
                                                                           @RequestParam(required = false) String status,
                                                                           @RequestParam(required = false) Boolean isMine,
                                                                           @RequestParam(name = "pageIndex", defaultValue = "1") Integer pageIndex,
                                                                           @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        SimpleUser loginUser = loginUser();
        return successResponse(datasetService.flattenDatasetList(name, id, userId, mediaType, contentType,
                language, status, isMine, pageIndex, pageSize, loginUser));
    }

    @GetMapping("/versions")
    public RestResult<DatasetVersionsWrapVO> getDatasetVersions(@RequestParam Long datasetId) {
        return successResponse(datasetService.getDatasetVersions(datasetId, loginUser()));
    }

    @GetMapping("/version/detail")
    public RestResult<DatasetVersionsWrapVO> getDatasetVersionDetail(@RequestParam Long datasetVersionId) {
        return successResponse(datasetService.getDatasetVersionDetail(datasetVersionId));
    }

    @PostMapping("/query")
    public RestResult<Long> createQuery(@RequestBody QueryCreateRequest request) {
        return successResponse(queryService.createQueryRecord(request, loginUser()));
    }

    @PostMapping("/query/update")
    public RestResult<Long> updateQueryState(@RequestBody QueryRecordRequest request) {
        return successResponse(queryService.updateQueryStatus(request.getQueryRecordId()));
    }

    @PostMapping("/query/update/batch")
    public RestResult<String> updateQueryStateBatch() {
        updateQueryRecord.updateQueryRecord();
        return successResponse("success");
    }

    @GetMapping("/query")
    public RestResult<QueryRecordVO> getQueryRecordVO(@RequestParam Long id) {
        return successResponse(queryService.getQueryRecord(id));
    }

    @GetMapping("/query/preview/latest")
    public RestResult<QueryRecordVO> getLatestQueryRecordVO(@RequestParam Long datasetVersionId) {
        return successResponse(queryService.getLatestQueryRecord(datasetVersionId));
    }

    @GetMapping("/sample/preview/latest")
    public RestResult<QueryRecordVO> getLatestSampleQueryRecordVO(@RequestParam Long sampleId) {
        return successResponse(queryService.getLatestSampleQueryRecord(sampleId));
    }

    @GetMapping("/query/result")
    public RestResult<Object> getQueryResult(@RequestParam Long id) {
        return successResponse(queryService.getQueryResult(id));
    }

    @GetMapping("/register/check")
    public RestResult<RegisterCheckVO> registerCheck(@RequestParam Long id, @RequestParam Long datasetVersionId) {
        return successResponse(queryService.registerCheck(id, datasetVersionId));
    }

    @PostMapping("/sample/query")
    public RestResult<Long> createSampleQuery(@RequestBody SampleQueryCreateRequest request) {
        return successResponse(queryService.createSampleQuery(request, loginUser()));
    }

    @GetMapping("/query/sample/records")
    public RestResult<Object> getSampleRecords(@RequestParam Long datasetVersionId) {
        return successResponse(queryService.getSampleRecordList(datasetVersionId));
    }


    @PostMapping("/sample/download")
    public RestResult<Long> sampleDownload(@RequestBody SampleQueryCreateRequest request) {
        return successResponse(queryService.sampleDownload(request, loginUser()));
    }

    @PostMapping("/sample/cancel")
    public RestResult<?> cancelSample(@RequestBody SampleQueryCreateRequest request) {
        queryService.cancelSample(request, loginUser());
        return successResponse("success");
    }

    @GetMapping("/path/region")
    public RestResult<Map<String, Object>> getPathRegion(@RequestParam String path) {
        return successResponse(datasetService.getPathRegion(path));
    }

    @GetMapping("/labels")
    public RestResult<List<String>> getDatasetVersionLabels() {
        return successResponse(datasetService.getDatasetVersionLabels());
    }

    @PostMapping("/createIndex")
    public RestResult<?> createIndex(@RequestBody DatasetVersionIndexRequest request) {
        datasetService.createIndex(request, loginUser());
        return successResponse("success");
    }

    @PostMapping("/deleteIndex")
    public RestResult<?> deleteIndex(@RequestBody DatasetVersionIndexRequest request) {
        datasetService.deleteIndex(request, loginUser());
        return successResponse("success");
    }

    @PostMapping("/datasetVersionList")
    public RestResult<List<DatasetVersionDetailVO>> datasetVersionList(@RequestBody DatasetVersionRequest request) {
        return successResponse(datasetService.datasetVersionList(request.getDatasetVersionIds()));
    }

    @PostMapping("/version/explore")
    public RestResult<?> createExploreTask(@RequestBody DatasetExploreRequest request) {
        datasetExploreTaskService.createExploreTask(request.getDatasetId(), request.getDatasetVersionId(), request.getMetaName());
        return successResponse("success");
    }
}
