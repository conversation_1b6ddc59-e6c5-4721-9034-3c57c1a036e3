package com.xiaohongshu.data.dataark.web.mq.consumer.oa;

import lombok.Data;

import java.util.Map;

/**
 * @author: longya
 * @since: 2023/9/2 17:49
 * @description:
 */
@Data
public class AuditMessage {

    private String formType;

    private String auditPhaseKey;

    private String startUserEmail;

    private boolean processEnd;

    private String auditManEmail;

    private String auditPhase;

    private String businessId;

    private String currentAuditUser;

    private String auditMan;

    private String startUserId;

    private String startUserName;

    private String auditStatus;

    private String formNo;

    private String comment;

    private Map<String, Object> formContent;
}
