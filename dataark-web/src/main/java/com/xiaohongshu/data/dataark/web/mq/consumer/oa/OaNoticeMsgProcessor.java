package com.xiaohongshu.data.dataark.web.mq.consumer.oa;

import com.alibaba.fastjson.JSONObject;
import com.xiaohongshu.data.dataark.core.common.enums.AuditStatus;
import com.xiaohongshu.data.dataark.core.service.ApproveRecordService;
import com.xiaohongshu.data.dataark.core.service.ApproveService;
import com.xiaohongshu.data.dataark.core.service.common.AlarmService;
import com.xiaohongshu.data.dataark.dao.entity.ApproveRecordDO;
import com.xiaohongshu.data.dataark.web.exception.OaFormNotFoundException;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/14
 */
@Component
@Slf4j
public class OaNoticeMsgProcessor implements MessageProcessor {

    @Resource
    private ApproveService approveService;

    @Resource
    private ApproveRecordService approveRecordService;

    @Resource
    private AlarmService alarmService;

    @Override
    public ConsumeStatus process(MessageExt messageExt, ConsumeContext consumeContext) {
        String msg = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        try {
            log.info("receive oa message: {}", msg);
            AuditMessage auditMessage = JSONObject.parseObject(msg, AuditMessage.class);
            ApproveRecordDO recordDO = approveRecordService.selectByApproveFormId(auditMessage.getFormNo());
            if (Objects.isNull(recordDO)) {
                log.error("未识别到有效的审批单: {}", auditMessage.getFormNo());
                throw new OaFormNotFoundException("can not find approveForm: " + auditMessage.getFormNo());
            }

            if (Objects.equals(auditMessage.getAuditStatus(), AuditStatus.AUDIT_PASS.name())) {
                log.info("审批通过");
                approveService.approvePass(recordDO);
            } else if (AuditStatus.REFUSED_STATUS.contains(auditMessage.getAuditStatus())) {
                log.info("审批拒绝");
                approveService.approveRefuse(recordDO, auditMessage.getAuditStatus());
            } else {
                approveRecordService.updateApproveStatusByApproveFormId(auditMessage.getFormNo(), auditMessage.getAuditStatus());
            }
            return ConsumeStatus.SUCCESS;
        } catch (OaFormNotFoundException ex) {
            log.error("未找到此工单: {}", ex.getMessage());
            throw new RuntimeException(ex);
        } catch (Exception e) {
            log.error("消费失败：", e);
            alarmService.alarmVChat("process oa flow failed : " + msg);
            throw new RuntimeException(e);
        }
    }

}
